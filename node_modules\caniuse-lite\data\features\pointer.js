module.exports={A:{A:{"1":"B","2":"K D E F gC","164":"A"},B:{"1":"5 6 7 8 9 C L M G N O P Q H R S T U V W X Y Z a b c d e f g h i j k l m n o p q r s t u v w x AB BB CB DB EB FB GB HB IB JB KB I"},C:{"1":"5 6 7 8 9 JC tB KC uB vB wB xB yB zB 0B 1B 2B 3B 4B 5B 6B 7B 8B 9B AC Q H R LC S T U V W X Y Z a b c d e f g h i j k l m n o p q r s t u v w x AB BB CB DB EB FB GB HB IB JB KB I BC MC NC iC","2":"hC IC J LB jC kC","8":"0 1 2 3 4 K D E F A B C L M G N O P MB y z NB OB PB QB RB SB TB UB VB WB XB YB ZB aB","328":"bB cB dB eB fB gB hB iB jB kB lB mB nB oB pB qB rB sB"},D:{"1":"5 6 7 8 9 pB qB rB sB JC tB KC uB vB wB xB yB zB 0B 1B 2B 3B 4B 5B 6B 7B 8B 9B AC Q H R S T U V W X Y Z a b c d e f g h i j k l m n o p q r s t u v w x AB BB CB DB EB FB GB HB IB JB KB I BC MC NC","2":"J LB K D E F A B C L M G N O P MB y z","8":"0 1 2 3 4 NB OB PB QB RB SB TB UB VB WB XB YB ZB aB bB cB dB eB fB gB hB iB jB kB lB","584":"mB nB oB"},E:{"1":"L M G qC rC sC QC RC EC tC FC SC TC UC VC WC uC GC XC YC ZC aC bC vC HC cC dC wC","2":"J LB K lC OC mC","8":"D E F A B C nC oC pC PC CC","1096":"DC"},F:{"1":"cB dB eB fB gB hB iB jB kB lB mB nB oB pB qB rB sB tB uB vB wB xB yB zB 0B 1B 2B 3B 4B 5B 6B 7B 8B 9B AC Q H R LC S T U V W X Y Z a b c d e f g h i j k l m n o p q r s t u v w x","2":"F B C xC yC zC 0C CC eC 1C DC","8":"0 1 2 3 4 G N O P MB y z NB OB PB QB RB SB TB UB VB WB XB YB","584":"ZB aB bB"},G:{"1":"GD HD ID JD KD LD QC RC EC MD FC SC TC UC VC WC ND GC XC YC ZC aC bC OD HC cC dC","8":"E OC 2C fC 3C 4C 5C 6C 7C 8C 9C AD BD CD DD ED","6148":"FD"},H:{"2":"PD"},I:{"1":"I","8":"IC J QD RD SD TD fC UD VD"},J:{"8":"D A"},K:{"1":"H","2":"A","8":"B C CC eC DC"},L:{"1":"I"},M:{"1":"BC"},N:{"1":"B","36":"A"},O:{"1":"EC"},P:{"1":"0 1 2 3 4 y z XD YD ZD aD PC bD cD dD eD fD FC GC HC gD","2":"WD","8":"J"},Q:{"1":"hD"},R:{"1":"iD"},S:{"1":"kD","328":"jD"}},B:2,C:"Pointer events",D:true};
