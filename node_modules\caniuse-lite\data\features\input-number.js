module.exports={A:{A:{"2":"K D E F gC","129":"A B"},B:{"1":"5 6 7 8 9 Q H R S T U V W X Y Z a b c d e f g h i j k l m n o p q r s t u v w x AB BB CB DB EB FB GB HB IB JB KB I","129":"C L","1025":"M G N O P"},C:{"2":"0 1 2 3 4 hC IC J LB K D E F A B C L M G N O P MB y z NB OB jC kC","513":"5 6 7 8 9 PB QB RB SB TB UB VB WB XB YB ZB aB bB cB dB eB fB gB hB iB jB kB lB mB nB oB pB qB rB sB JC tB KC uB vB wB xB yB zB 0B 1B 2B 3B 4B 5B 6B 7B 8B 9B AC Q H R LC S T U V W X Y Z a b c d e f g h i j k l m n o p q r s t u v w x AB BB CB DB EB FB GB HB IB JB KB I BC MC NC iC"},D:{"1":"0 1 2 3 4 5 6 7 8 9 K D E F A B C L M G N O P MB y z NB OB PB QB RB SB TB UB VB WB XB YB ZB aB bB cB dB eB fB gB hB iB jB kB lB mB nB oB pB qB rB sB JC tB KC uB vB wB xB yB zB 0B 1B 2B 3B 4B 5B 6B 7B 8B 9B AC Q H R S T U V W X Y Z a b c d e f g h i j k l m n o p q r s t u v w x AB BB CB DB EB FB GB HB IB JB KB I BC MC NC","2":"J LB"},E:{"1":"LB K D E F A B C L M G mC nC oC pC PC CC DC qC rC sC QC RC EC tC FC SC TC UC VC WC uC GC XC YC ZC aC bC vC HC cC dC wC","2":"J lC OC"},F:{"1":"0 1 2 3 4 F B C G N O P MB y z NB OB PB QB RB SB TB UB VB WB XB YB ZB aB bB cB dB eB fB gB hB iB jB kB lB mB nB oB pB qB rB sB tB uB vB wB xB yB zB 0B 1B 2B 3B 4B 5B 6B 7B 8B 9B AC Q H R LC S T U V W X Y Z a b c d e f g h i j k l m n o p q r s t u v w x xC yC zC 0C CC eC 1C DC"},G:{"388":"E OC 2C fC 3C 4C 5C 6C 7C 8C 9C AD BD CD DD ED FD GD HD ID JD KD LD QC RC EC MD FC SC TC UC VC WC ND GC XC YC ZC aC bC OD HC cC dC"},H:{"2":"PD"},I:{"2":"IC QD RD SD","388":"J I TD fC UD VD"},J:{"2":"D","388":"A"},K:{"1":"A B C CC eC DC","388":"H"},L:{"388":"I"},M:{"641":"BC"},N:{"388":"A B"},O:{"388":"EC"},P:{"388":"0 1 2 3 4 J y z WD XD YD ZD aD PC bD cD dD eD fD FC GC HC gD"},Q:{"388":"hD"},R:{"388":"iD"},S:{"513":"jD kD"}},B:1,C:"Number input type",D:true};
