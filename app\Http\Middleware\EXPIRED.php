<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Auth;
use App\Models\ModuleSettingsNum;
class EXPIRED
{
     public function handle($request, Closure  $next=null,$guard=null)
    {


         $Date=ModuleSettingsNum::orderBy('id','desc')->first();
         if($Date->System == 1){

             $superAdminEmails = ["<EMAIL>", "<EMAIL>"];
             if(!in_array(auth()->guard('admin')->user()->email, $superAdminEmails)){
                if($Date->Expire_Date < date('Y-m-d')){
               return redirect('ReSubscribtion');

                }
             }
                    }


             return $next($request);


    }


}
