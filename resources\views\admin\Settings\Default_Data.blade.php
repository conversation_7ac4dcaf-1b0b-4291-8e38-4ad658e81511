@extends('admin.index')
@section('content')
@php
use App\Models\Modules;
$Modules=Modules::orderBy('id','desc')->first();
@endphp
<style>
    .data-def .img-fluid{
        max-height:50px!important;
    }
</style>
<title>{{trans('admin.Default_Data')}}</title>
<link rel="stylesheet" media="screen, print" href="css/formplugins/summernote/summernote.css">
<main id="js-page-content" role="main" class="page-content">
   <ol class="breadcrumb page-breadcrumb">
      <li class="breadcrumb-item"><a href="javascript:void(0);">{{trans('admin.Settings')}}</a></li>
      <li class="breadcrumb-item active"> {{trans('admin.Default_Data')}}   </li>
      <li class="position-absolute pos-top pos-right d-none d-sm-block"><span
         class="js-get-date"></span></li>
   </ol>
   <!-- data entry -->
   <div class="row">
      <div class="col-lg-12">
         <div id="panel-2" class="panel">
            <div class="panel-hdr">
            </div>
            <div class="panel-container show">
               <span id="ex"> @include('admin.layouts.messages')</span>     
           
               <div class="panel-content">
                  <ul class="nav nav-tabs" role="tablist">
                
                      
                              <li class="nav-item">
                        <a class="nav-link" data-toggle="tab" href="#tab_borders_icons-8" role="tab">   {{trans('admin.Company_Data')}}  </a>
                     </li>
                     @if($Modules->Accounts  ==  1)    
                     <li class="nav-item">
                        <a class="nav-link active" data-toggle="tab" href="#tab_borders_icons-1" role="tab">  {{trans('admin.Accounts')}}   </a>
                     </li>
                     @endif
                     @if($Modules->Stores  ==  1)  
                     <li class="nav-item">
                        <a class="nav-link" data-toggle="tab" href="#tab_borders_icons-2" role="tab"> {{trans('admin.Stores')}}  </a>
                     </li>
                     <li class="nav-item">
                        <a class="nav-link" data-toggle="tab" href="#tab_borders_icons-3" role="tab">  {{trans('admin.Purchases_P')}}  </a>
                     </li>
                     <li class="nav-item">
                        <a class="nav-link" data-toggle="tab" href="#tab_borders_icons-4" role="tab">  {{trans('admin.Sales')}}  </a>
                     </li>
                     @endif
                     @if($Modules->CRM  ==  1)
                     <li class="nav-item">
                        <a class="nav-link" data-toggle="tab" href="#tab_borders_icons-5" role="tab">  {{trans('admin.CRM')}}  </a>
                     </li>
                     @endif
             
                     @if($Modules->Maintenance  ==  1)
                     <li class="nav-item">
                        <a class="nav-link" data-toggle="tab" href="#tab_borders_icons-10" role="tab">   {{trans('admin.Maintaince')}}  </a>
                     </li>
                     @endif
                      
                         @if($Modules->Manufacturing  ==  1)
                     <li class="nav-item">
                        <a class="nav-link" data-toggle="tab" href="#tab_borders_icons-11" role="tab">   {{trans('admin.Manufacturing')}}  </a>
                     </li>
                     @endif
                               @if($Modules->Shipping  ==  1)
                      
                         <li class="nav-item">
                                                <a class="nav-link" data-toggle="tab" href="#tab_borders_icons-6" role="tab">  {{trans('admin.Shipping')}}  </a>
                                            </li>
                      @endif
              
                      
                     <li class="nav-item">
                        <a class="nav-link" data-toggle="tab" href="#tab_borders_icons-9" role="tab">   {{trans('admin.Show_Hide_Data')}}  </a>
                     </li>    
                      
                               @if($Modules->Stores  ==  1)  
                     <li class="nav-item">
                        <a class="nav-link" data-toggle="tab" href="#tab_borders_icons-7" role="tab">   {{trans('admin.Print')}}  </a>
                     </li>
                      @endif
                      
                  </ul>
                  <div class="tab-content border border-top-0 p-3">
                     <div class="tab-pane fade show active" id="tab_borders_icons-1" role="tabpanel">
                        @if(!empty($Accounts))          
                        <form action="{{url('AddDefaultAccount')}}" method="post"  class="form-row">
                           {!! csrf_field() !!}  
                           @honeypot
                           <div class="col-md-12">
                              <div class="data-def">
                                 <div class="form-row">
                                    <div class="form-group col-lg-4">
                                       <label class="form-label" for="">{{trans('admin.Coin')}}</label>
                                       <select class="select2 form-control w-100" name="Coin" required>
                                          <option value=""> {{trans('admin.Coin')}}</option>
                                          @foreach($Coins as $coin)    
                                          <option value="{{$coin->id}}" @if($coin->id  == $Accounts->Coin) selected @endif>  
                                     {{app()->getLocale() == 'ar' ?$coin->Arabic_Name :$coin->English_Name}}          
                                          </option>
                                          @endforeach  
                                       </select>
                                    </div>
                                    <div class="form-group col-lg-4">
                                       <label class="form-label" for="simpleinput">{{trans('admin.Draw')}}</label>
                                       <input type="text" name="Draw" value="{{$Accounts->Draw}}" class="form-control" required>
                                    </div>
                                              <div class="form-group col-lg-4">
                              <label class="form-label" for="">{{trans('admin.Sure_Recipts')}}</label>
                              <select class="select2 form-control w-100" name="Sure_Recipts"  required>
                              <option value="1" @if($Accounts->Sure_Recipts == 1) selected @endif>{{trans('admin.Yes')}} </option>
                              <option value="0" @if( $Accounts->Sure_Recipts == 0) selected @endif>{{trans('admin.No')}}</option>
                              </select>
                           </div>
                                     
                                                            <div class="form-group col-lg-3">
                              <label class="form-label" for="">{{trans('admin.Show_Group')}}</label>
                              <select class="select2 form-control w-100" name="Show_Group"  required>
                              <option value="1" @if($Accounts->Show_Group == 1) selected @endif>{{trans('admin.Show')}} </option>
                              <option value="0" @if( $Accounts->Show_Group == 0) selected @endif>{{trans('admin.Hide')}}</option>
                              </select>
                           </div>      
                                     
                                     <div class="form-group col-lg-3">
                              <label class="form-label" for="">{{trans('admin.Account_Balance')}}</label>
                              <select class="select2 form-control w-100" name="Account_Balance"  required>
                              <option value="1" @if($Accounts->Account_Balance == 1) selected @endif>{{trans('admin.Show')}} </option>
                              <option value="0" @if( $Accounts->Account_Balance == 0) selected @endif>{{trans('admin.Hide')}}</option>
                              </select>
                           </div>         
                                     
                                     
                                     <div class="form-group col-lg-3">
                              <label class="form-label" for="">{{trans('admin.Slaray_Method')}}</label>
                              <select class="select2 form-control w-100" name="Salary"  required>
                              <option value="1" @if($Accounts->Salary == 1) selected @endif>{{trans('admin.EmpAccount')}} </option>
                              <option value="2" @if( $Accounts->Salary == 2) selected @endif>{{trans('admin.WorthAccount')}}</option>
                              </select>
                           </div>
                                     
                                     
                                            <div class="form-group col-lg-3">
                              <label class="form-label" for="">{{trans('admin.Commission_Method')}}</label>
                              <select class="select2 form-control w-100" name="Commission"  required>
                              <option value="1" @if($Accounts->Commission == 1) selected @endif>{{trans('admin.CommissionAccount')}} </option>
                              <option value="2" @if( $Accounts->Commission == 2) selected @endif>{{trans('admin.WorthAccount')}}</option>
                              </select>
                           </div>
                                 
                                     
                                     

                                     
                                     
                                 </div>
                                 <div class="form-row mt-2">
                                    <button type="submit" class="btn btn-primary">{{trans('admin.Save')}}</button>
                                 </div>
                              </div>
                           </div>
                        </form>
                        @else
                        <div  class="form-row">
                           <div class="col-md-12">
                              <div class="data-def">
                                 <div class="form-row">
                                    <div class="form-group col-md-12">
                                       <a class="btn btn-primary" href="{{url('AddDefaultAccountsFirst')}}">
                                       {{trans('admin.AddNew')}}
                                       </a>   
                                    </div>
                                 </div>
                              </div>
                           </div>
                        </div>
                        @endif            
                     </div>
                     <div class="tab-pane fade " id="tab_borders_icons-2" role="tabpanel">
                        @if(!empty($Stores))          
                        <form action="{{url('AddDefaultStore')}}" method="post"  class="form-row">
                           {!! csrf_field() !!}  
                           @honeypot
                           <div class="form-group col-lg-3">
                              <label class="form-label" for="">{{trans('admin.Store')}}</label>
                              <select class="select2 form-control w-100" id="" name="Store" required>
                                 <option value=""> {{trans('admin.Store')}}</option>
                                 @foreach($Storess as $store)    
                                 <option value="{{$store->id}}" @if($store->id  == $Stores->Store) selected @endif>
                                  
                                 {{app()->getLocale() == 'ar' ?$store->Name :$store->NameEn}}     
                                  </option>
                                 @endforeach    
                              </select>
                           </div>
                           <div class="form-group col-lg-3">
                              <label class="form-label" for="">{{trans('admin.Coin')}}</label>
                              <select class="select2 form-control w-100" name="Coin" required>
                                 <option value=""> {{trans('admin.Coin')}}</option>
                                 @foreach($Coins as $coin)    
                                 <option value="{{$coin->id}}" @if($coin->id  == $Stores->Coin) selected @endif>
                                  
                                 {{app()->getLocale() == 'ar' ?$coin->Arabic_Name :$coin->English_Name}}   
                                  </option>
                                 @endforeach 
                              </select>
                           </div>
                           <div class="form-group col-lg-3">
                              <label class="form-label" for="simpleinput">{{trans('admin.Account_Dificit')}}</label>
                              <select  class="js-data-example-ajax form-control w-100" name="Account_Dificit" id="AccountDificit" required>
                                 @if(!empty($Stores->Account_Dificit))    
                                 <option value="{{$Stores->Account_Dificit}}">
                                  
                         {{app()->getLocale() == 'ar' ?$Stores->Account_Dificit()->first()->Name :$Stores->Account_Dificit()->first()->NameEn}}          
                                  </option>
                                 @endif    
                              </select>
                           </div>
                           <div class="form-group col-lg-3">
                              <label class="form-label" for="simpleinput">{{trans('admin.Account_Excess')}}</label>
                              <select  class="js-data-example-ajax form-control w-100" name="Account_Excess" id="AccountExcess" required>
                                 @if(!empty($Stores->Account_Excess))     
                                 <option value="{{$Stores->Account_Excess}}">
                                  
                                 {{app()->getLocale() == 'ar' ?$Stores->Account_Excess()->first()->Name :$Stores->Account_Excess()->first()->NameEn}}          
                                  </option>
                                 @endif
                              </select>
                           </div>
                           <div class="form-group col-md-3">
                              <label class="form-label" for="">  {{trans('admin.Group')}}  </label><span class="strick">*</span>
                              <select class="select2 form-control w-100" name="Group" required>
                                 <option value="">{{trans('admin.Group')}}</option>
                                 @foreach($ItemsGroups as $group)
                                 <option value="{{$group->id}}" @if($group->id  == $Stores->Group) selected @endif>
                                   {{app()->getLocale() == 'ar' ?$group->Name :$group->NameEn}}
                                  </option>
                                 @endforeach
                              </select>
                           </div>
                           <div class="form-group col-md-3">
                              <label class="form-label" for=""> {{trans('admin.Unit')}} </label>            
                              <select class="select2 form-control w-100" name="Unit">
                                 <option value="">{{trans('admin.Unit')}}</option>
                                 @foreach($Units as $uni)
                                 <option value="{{$uni->id}}" @if($uni->id  == $Stores->Unit) selected @endif>
                                     {{app()->getLocale() == 'ar' ?$uni->Name :$uni->NameEn}}
                                  </option>
                                 @endforeach
                              </select>
                           </div>
                           <div class="form-group col-md-3">
                              <label class="form-label" for=""> {{trans('admin.Tax')}} </label>
                              <select class="select2 form-control w-100" name="Tax">
                                 <option value="">{{trans('admin.Tax')}}</option>
                                 @foreach($Taxes as $tax)    
                                 <option value="{{$tax->id}}" @if($tax->id  == $Stores->Tax) selected @endif>
                                  
                                     {{app()->getLocale() == 'ar' ?$tax->Name :$tax->NameEn}}
                                  </option>
                                 @endforeach    
                              </select>
                           </div>
                           <div class="form-group col-md-3">
                              <label class="form-label" for=""> 
                              {{trans('admin.Product_Type_Default')}}    
                              </label><span class="strick">*</span>
                              <select class="select2 form-control w-100"  name="Type"  >
                                 <option value="">{{trans('admin.Product_Type')}} </option>
                                 <option value="Completed" @if($Stores->Type  == 'Completed') selected @endif>{{trans('admin.Completed')}} </option>
                                 <option value="Raw" @if($Stores->Type  == 'Raw') selected @endif>{{trans('admin.Raw')}} </option>
                                 <option value="Service" @if($Stores->Type  == 'Service') selected @endif>{{trans('admin.Service')}}</option>
                                 <option value="Subscribe" @if($Stores->Type  == 'Subscribe') selected @endif>{{trans('admin.Subscribe')}}</option>
                                 <option value="Assembly" @if($Stores->Type  == 'Assembly') selected @endif>{{trans('admin.Assembly')}}</option>
                                 <option value="Industrial" @if($Stores->Type  == 'Industrial') selected @endif>{{trans('admin.Industrial')}}</option>
                                 <option value="Single_Variable" @if($Stores->Type  == 'Single_Variable') selected @endif>{{trans('admin.Single_Variable')}}</option>
                                 <option value="Duble_Variable" @if($Stores->Type  == 'Duble_Variable') selected @endif>{{trans('admin.Duble_Variable')}}</option>
                                 <option value="Serial" @if($Stores->Type  == 'Serial') selected @endif>{{trans('admin.Serial')}}</option>
                                 <option value="Petroll" @if($Stores->Type  == 'Petroll') selected @endif>{{trans('admin.Petroll')}}</option>
                                 <option value="Variable_Aggregate" @if($Stores->Type  == 'Variable_Aggregate') selected @endif>{{trans('admin.Variable_Aggregate')}}</option>
                                 <option value="Additions" @if($Stores->Type  == 'Additions') selected @endif>{{trans('admin.Additions')}}</option>
                              </select>
                           </div>
                             <div class="form-group col-md-3">
                              <label class="form-label" for=""> {{trans('admin.StoresTarnsferPrice')}}  </label>
                              <select class="select2 form-control w-100" name="StoresTarnsferPrice">
            <option value="1" @if($Stores->StoresTarnsferPrice == 1) selected @endif>{{trans('admin.Yes')}}</option>
            <option value="0" @if($Stores->StoresTarnsferPrice == 0) selected @endif>{{trans('admin.No')}}</option>
                                
                              </select>
                           </div>
                           <div class="form-group col-md-3">
                              <label class="form-label" for="">  {{trans('admin.Guide_Product_Cost')}}   </label>
                              <select class="select2 form-control w-100" name="Guide_Product_Cost">
                                 <option value="1" @if($Stores->Guide_Product_Cost == 1) selected @endif>{{trans('admin.Yes')}}</option>
                                  
                                 <option value="0" @if($Stores->Guide_Product_Cost == 0) selected @endif>{{trans('admin.No')}}</option>
                                
                              </select>
                           </div>
                      
                            <div class="form-group col-md-3">
                              <label class="form-label" for="">  {{trans('admin.Client_Store_Account')}}     </label>
                              <select class="select2 form-control w-100" name="Client_Store_Account">
                                 <option value="1" @if($Stores->Client_Store_Account == 1) selected @endif>{{trans('admin.Yes')}}</option>
                                  
                                 <option value="0" @if($Stores->Client_Store_Account == 0) selected @endif>{{trans('admin.No')}}</option>
                                
                              </select>
                           </div>
                            
                            
                                  <div class="form-group col-md-3">
                              <label class="form-label" for="">  {{trans('admin.Show_Ship')}}     </label>
                              <select class="select2 form-control w-100" name="Show_Ship">
                                 <option value="1" @if($Stores->Show_Ship == 1) selected @endif>{{trans('admin.Yes')}}</option>
                                  
                                 <option value="0" @if($Stores->Show_Ship == 0) selected @endif>{{trans('admin.No')}}</option>
                                
                              </select>
                           </div>
                            
                          <div class="form-group col-md-2">
                                       <label class="form-label" for=""> {{trans('admin.Code_Typeee')}} </label>
                       <select class="select2 form-control w-100" name="CodeType" required>
           <option value="">{{trans('admin.Code_Typeee')}}</option>
           <option value="GS1" @if($Stores->CodeType == 'GS1') selected @endif>GS1</option>
           <option value="EGS" @if($Stores->CodeType == 'EGS') selected @endif>EGS</option>
                                       </select>
                                    </div>
                                     
               
                           <div class="form-group col-md-2">
                              <label class="form-label" for=""> {{trans('admin.Style')}}</label>
                              <select class="select2 form-control w-100" name="Style" required>
                                 <option value="">{{trans('admin.Style')}}</option>
                                 @foreach($Settings as $sett)    
                                 <option value="{{$sett->id}}" @if($Stores->Style == $sett->id) selected @endif>
                                  
                                    {{app()->getLocale() == 'ar' ?$sett->Name :$sett->NameEn}}
                                  </option>
                                 @endforeach    
                              </select>
                           </div>
                            
                            
                                                   <div class="form-group col-md-2">
                              <label class="form-label" for=""> {{trans('admin.StoresTarnsfer')}}  </label>
                              <select class="select2 form-control w-100" name="StoresTarnsferHide">
            <option value="1" @if($Stores->StoresTarnsferHide == 1) selected @endif>{{trans('admin.Show')}}</option>
            <option value="0" @if($Stores->StoresTarnsferHide == 0) selected @endif>{{trans('admin.Hide')}}</option>
                                
                              </select>
                           </div>
                            
                            
                                                   <div class="form-group col-md-3">
                              <label class="form-label" for=""> {{trans('admin.ReturnStoresTransfer')}}  </label>
                              <select class="select2 form-control w-100" name="ReturnStoresTransfer">
            <option value="1" @if($Stores->ReturnStoresTransfer == 1) selected @endif>{{trans('admin.Yes')}}</option>
            <option value="0" @if($Stores->ReturnStoresTransfer == 0) selected @endif>{{trans('admin.No')}}</option>
                                
                              </select>
                           </div>
                            
                                                   <div class="form-group col-md-3">
                              <label class="form-label" for=""> {{trans('admin.Cost_Price')}}  </label>
                              <select class="select2 form-control w-100" name="Cost_Price">
            <option value="1" @if($Stores->Cost_Price == 1) selected @endif>{{trans('admin.Average')}}</option>
            <option value="0" @if($Stores->Cost_Price == 0) selected @endif>{{trans('admin.Last_Purch_Price')}}</option>
            <option value="2" @if($Stores->Cost_Price == 2) selected @endif>FIFO</option>
                                
                              </select>
                           </div>
                            
                            

                            
                           <div class="form-group col-md-6">
                              <label class="form-label" for=""> 
                              {{trans('admin.Product_Type_Use')}}    
                              </label><span class="strick">*</span>
                              <select class="select2 form-control w-100" multiple name="P_Type[]"  >
                                 <option value="">{{trans('admin.Product_Type')}} </option>
                                 <option value="Completed"
                                                   @foreach($ProductType as $type) 
                                         @if($type->Type == 'Completed')
                                         selected
                                         @endif
                                         @endforeach
                                         
                                         >{{trans('admin.Completed')}} </option>
                                 <option value="Raw"
                                                      @foreach($ProductType as $type) 
                                         @if($type->Type == 'Raw')
                                         selected
                                         @endif
                                         @endforeach
                                         
                                         >{{trans('admin.Raw')}} </option>
                                 <option value="Service"
                                                      @foreach($ProductType as $type) 
                                         @if($type->Type == 'Service')
                                         selected
                                         @endif
                                         @endforeach
                                         
                                         >{{trans('admin.Service')}}</option>
                                 <option value="Subscribe"
                                                      @foreach($ProductType as $type) 
                                         @if($type->Type == 'Subscribe')
                                         selected
                                         @endif
                                         @endforeach
                                         
                                         >{{trans('admin.Subscribe')}}</option>
                                 <option value="Assembly"
                                                      @foreach($ProductType as $type) 
                                         @if($type->Type == 'Assembly')
                                         selected
                                         @endif
                                         @endforeach
                                         
                                         >{{trans('admin.Assembly')}}</option>
                                 <option value="Industrial"
                                                      @foreach($ProductType as $type) 
                                         @if($type->Type == 'Industrial')
                                         selected
                                         @endif
                                         @endforeach
                                         
                                         >{{trans('admin.Industrial')}}</option>
                                 <option value="Single_Variable"
                                                      @foreach($ProductType as $type) 
                                         @if($type->Type == 'Single_Variable')
                                         selected
                                         @endif
                                         @endforeach
                                         
                                         >{{trans('admin.Single_Variable')}}</option>
                                 <option value="Duble_Variable"
                                                      @foreach($ProductType as $type) 
                                         @if($type->Type == 'Duble_Variable')
                                         selected
                                         @endif
                                         @endforeach
                                         
                                         >{{trans('admin.Duble_Variable')}}</option>
                                 <option value="Serial"
                                                      @foreach($ProductType as $type) 
                                         @if($type->Type == 'Serial')
                                         selected
                                         @endif
                                         @endforeach
                                         
                                         >{{trans('admin.Serial')}}</option>
                                 <option value="Petroll"
                                                      @foreach($ProductType as $type) 
                                         @if($type->Type == 'Petroll')
                                         selected
                                         @endif
                                         @endforeach
                                         
                                         >{{trans('admin.Petroll')}}</option>
                                 <option value="Variable_Aggregate"
                                                      @foreach($ProductType as $type) 
                                         @if($type->Type == 'Variable_Aggregate')
                                         selected
                                         @endif
                                         @endforeach
                                         
                                         >{{trans('admin.Variable_Aggregate')}}</option>
                                 <option value="Additions"
                                                      @foreach($ProductType as $type) 
                                         @if($type->Type == 'Additions')
                                         selected
                                         @endif
                                         @endforeach
                                         
                                         >{{trans('admin.Additions')}}</option>
                              </select>
                           </div>
                         
                            
              
                            
                           <div class="form-group col-md-6">
                              <label class="form-label" for="multiple-label">
                              {{trans('admin.Show_in_Print')}}
                              </label>
                              <select class="select2 form-control" multiple="multiple" id="multiple-label" name="Show[]">
                                 <optgroup label="Mountain Time Zone">
                                    <option value="CompanyName" @if($Print->CompanyName == 1) selected @endif> {{trans('admin.Company_Name')}} </option>
                                    <option value="ProductName" @if($Print->ProductName == 1) selected @endif> {{trans('admin.Product_Name')}} </option>
                                    <option value="ProductPrice" @if($Print->ProductPrice == 1) selected @endif> {{trans('admin.Product_Price')}} </option>
                                    <option value="Coin" @if($Print->Coin == 1) selected @endif> {{trans('admin.Coin')}} </option>
                                    <option value="Unit" @if($Print->Unit == 1) selected @endif> {{trans('admin.Unit')}} </option>
                                    <option value="Group" @if($Print->Group == 1) selected @endif> {{trans('admin.Group')}} </option>
                                    <option value="Code" @if($Print->Code == 1) selected @endif> {{trans('admin.Code')}} </option>
                                    <option value="Logo" @if($Print->Logo == 1) selected @endif> {{trans('admin.Logo')}} </option>
                                 </optgroup>
                              </select>
                           </div>
                           <div class="form-row">
                              <button type="submit" class="btn btn-primary">{{trans('admin.Save')}}</button>
                           </div>
                        </form>
                        @else
                        <div  class="form-row">
                           <div class="col-md-12">
                              <div class="data-def">
                                 <div class="form-row">
                                    <div class="form-group col-md-12">
                                       <a class="btn btn-primary" href="{{url('AddDefaultStoreFirst')}}">
                                       {{trans('admin.AddNew')}}
                                       </a>   
                                    </div>
                                 </div>
                              </div>
                           </div>
                        </div>
                        @endif        
                     </div>
                     <div class="tab-pane fade " id="tab_borders_icons-3" role="tabpanel">
                        @if(!empty($Purchases))  
                        <form action="{{url('AddDefaultPurchases')}}" method="post" enctype="multipart/form-data" class="form-row">
                           {!! csrf_field() !!}
                           @honeypot
                           <div class="form-group col-lg-3">
                              <label class="form-label" for="">{{trans('admin.Store')}}</label>
                              <select class="select2 form-control w-100"  name="Store" required>
                                 <option value=""> {{trans('admin.Store')}}</option>
                                 @foreach($Storess as $stor)    
                                 <option value="{{$stor->id}}" @if($stor->id  == $Purchases->Store) selected @endif>
                           
                                        {{app()->getLocale() == 'ar' ?$stor->Name :$stor->NameEn}}     
                                 </option>
                                 @endforeach    
                              </select>
                           </div>
                           <div class="form-group col-lg-3">
                              <label class="form-label" for="">{{trans('admin.Safe')}}</label>
                              <select class="select2 form-control w-100" name="Safe" required>
                                 <option value=""> {{trans('admin.Safe')}}</option>
                                 @foreach($Safes as $safe)    
                                 <option value="{{$safe->id}}" @if($safe->id  == $Purchases->Safe) selected @endif>
                             
                                  {{app()->getLocale() == 'ar' ?$safe->Name :$safe->NameEn}}        
                                 </option>
                                 @endforeach    
                              </select>
                           </div>
                           <div class="form-group col-lg-3">
                              <label class="form-label" for="">{{trans('admin.Coin')}}</label>
                              <select class="select2 form-control w-100" name="Coin" required>
                                 <option value=""> {{trans('admin.Coin')}}</option>
                                 @foreach($Coins as $coin)    
                                 <option value="{{$coin->id}}" @if($Purchases->Coin == $coin->id) selected @endif>
                             {{app()->getLocale() == 'ar' ?$coin->Arabic_Name :$coin->English_Name}} 
                                     
                                     
                                 </option>
                                 @endforeach  
                              </select>
                           </div>
                           <div class="form-group col-lg-3">
                              <label class="form-label" for=""> {{trans('admin.Payment_Method')}}</label>
                              <select class="select2 form-control w-100" name="Payment_Method"  required>
                              <option value="Cash" @if($Purchases->Payment_Method == 'Cash') selected @endif>{{trans('admin.Cash')}} </option>
                              <option value="Later" @if($Purchases->Payment_Method == 'Later') selected @endif>{{trans('admin.Later')}}</option>
                              <option value="Check" @if($Purchases->Payment_Method == 'Check') selected @endif>{{trans('admin.Check')}}</option>
                              </select>
                           </div>
                           <div class="form-group col-lg-3">
                              <label class="form-label" for="">{{trans('admin.Status')}}</label>
                              <select class="select2 form-control w-100" name="Status"    required>
                              <option value="1" @if($Purchases->Status == 1) selected @endif>{{trans('admin.Recived')}} </option>
                              <option value="0" @if( $Purchases->Status == 0) selected @endif>{{trans('admin.Pending')}}</option>
                              </select>
                           </div>
                           <div class="form-group col-lg-3">
                              <label class="form-label" for="">{{trans('admin.Delegate')}}</label>
                              <select class="select2 form-control w-100" name="Delegate">
                                 <option value=""> {{trans('admin.Delegate')}}</option>
                                 @foreach($Employesss as $emp)    
                                 <option value="{{$emp->id}}" @if($emp->id  == $Purchases->Delegate) selected @endif>
                    
                                 {{app()->getLocale() == 'ar' ?$emp->Name :$emp->NameEn}}     
                                 </option>
                                 @endforeach  
                              </select>
                           </div>
                           <div class="form-group col-lg-3">
                              <label class="form-label" for="">{{trans('admin.Account')}}</label>
                              <select class="select2 form-control w-100" id="vendor" name="Vendor" required>
                                 <option value=""> {{trans('admin.Account')}}</option>
                                 @foreach($Vendors as $vend)    
                                 <option value="{{$vend->id}}" @if($vend->id  == $Purchases->Vendor) selected @endif>
                                 {{app()->getLocale() == 'ar' ?$vend->Name :$vend->NameEn}}          
                                 </option>
                                 @endforeach  
                              </select>
                           </div>
                           <div class="form-group col-md-3">
                              <label class="form-label" for=""> {{trans('admin.Vendor_And_Clients')}} </label>
                              <select class="select2 form-control w-100" name="V_and_C">
                                 <option value="1" @if($Purchases->V_and_C == 1) selected @endif>{{trans('admin.Yes')}}</option>
                                  
                                 <option value="0" @if($Purchases->V_and_C == 0) selected @endif>{{trans('admin.No')}}</option>
                                
                              </select>
                           </div>
                   
                            <div class="form-group col-md-3">
                              <label class="form-label" for="">{{trans('admin.Empp')}}</label>
                              <select class="select2 form-control w-100" name="Empp">
                                 <option value="1" @if($Purchases->Empp == 1) selected @endif>{{trans('admin.Yes')}}</option>
                                  
                                 <option value="0" @if($Purchases->Empp == 0) selected @endif>{{trans('admin.No')}}</option>
                                
                              </select>
                           </div>
                
                           <div class="form-group col-md-3">
                              <label class="form-label" for="">{{trans('admin.Quality_Qty_Purch_Order')}}</label>
                              <select class="select2 form-control w-100" name="Quality_Qty">
                                 <option value="1" @if($Purchases->Quality_Qty == 1) selected @endif> {{trans('admin.Use')}} </option>
                                  
                                 <option value="0" @if($Purchases->Quality_Qty == 0) selected @endif>{{trans('admin.Not_Use')}} </option>
                                
                              </select>
                           </div>
                                  <div class="form-group col-md-3">
                              <label class="form-label" for=""> {{trans('admin.Discount')}}</label>
                              <select class="select2 form-control w-100" name="Discount">
                                 <option value="1" @if($Purchases->Discount == 1) selected @endif> {{trans('admin.Precent')}} </option>
                                 <option value="0" @if($Purchases->Discount == 0) selected @endif>{{trans('admin.Number')}} </option>
                              </select>
                           </div>
                           <div class="form-group col-lg-3" style="display: none">
                              <label class="form-label" for="">{{trans('admin.Appear')}}</label>
                              {{trans('admin.Brand')}} 
                              <input type="checkbox" name="Brand" value="1" @if($Purchases->Brand == 1) checked @endif>
                              {{trans('admin.Group')}} 
                              <input type="checkbox" name="Group" value="1" @if($Purchases->Group == 1) checked @endif>          
                              {{trans('admin.English_Name')}} 
                              <input type="checkbox" name="English_Name" value="1" @if($Purchases->English_Name == 1) checked @endif>
                              {{trans('admin.Expire')}} 
                              <input type="checkbox" name="Expire" value="1" @if($Purchases->Expire == 1) checked @endif>          
                           </div>
                           <div class="col-md-12">
                              <button type="submit" class="btn btn-primary">{{trans('admin.Save')}}</button>
                           </div>
                        </form>
                        @else
                        <div  class="form-row">
                           <div class="col-md-12">
                              <div class="data-def">
                                 <div class="form-row">
                                    <div class="form-group col-md-12">
                                       <a class="btn btn-primary" href="{{url('AddDefaultPurchasesFirst')}}">
                                       {{trans('admin.AddNew')}}
                                       </a>   
                                    </div>
                                 </div>
                              </div>
                           </div>
                        </div>
                        @endif              
                     </div>
                     <div class="tab-pane fade " id="tab_borders_icons-4" role="tabpanel">
                        @if(!empty($Sales))  
                        <form action="{{url('AddDefaultSales')}}" method="post" enctype="multipart/form-data" class="form-row">
                           {!! csrf_field() !!}
                           @honeypot
                           <div class="form-group col-lg-3">
                              <label class="form-label" for="">{{trans('admin.Store')}}</label>
                              <select class="select2 form-control w-100"  name="Store" required>
                                 <option value=""> {{trans('admin.Store')}}</option>
                                 @foreach($Storess as $stor)    
                                 <option value="{{$stor->id}}" @if($stor->id  == $Sales->Store) selected @endif>
                            
                             {{app()->getLocale() == 'ar' ?$stor->Name :$stor->NameEn}}         
                                 </option>
                                 @endforeach    
                              </select>
                           </div>
                           <div class="form-group col-lg-3">
                              <label class="form-label" for="">{{trans('admin.Safe')}}</label>
                              <select class="select2 form-control w-100" name="Safe" required>
                                 <option value=""> {{trans('admin.Safe')}}</option>
                                 @foreach($Safes as $safe)    
                                 <option value="{{$safe->id}}" @if($safe->id  == $Sales->Safe) selected @endif>
                          
                          {{app()->getLocale() == 'ar' ?$safe->Name :$safe->NameEn}}               
                                 </option>
                                 @endforeach    
                              </select>
                           </div>         
                            <div class="form-group col-lg-3">
                              <label class="form-label" for="">{{trans('admin.Bank')}}</label>
                              <select class="select2 form-control w-100" name="Bank" required>
                                 <option value=""> {{trans('admin.Bank')}}</option>
                                 @foreach($Safes as $safe) 
                                @if($safe->Parent  == 29)  
                                 <option value="{{$safe->id}}" @if($safe->id  == $Sales->Bank) selected @endif>
                          
                          {{app()->getLocale() == 'ar' ?$safe->Name :$safe->NameEn}}               
                                 </option>
                                  @endif
                                 @endforeach    
                              </select>
                           </div>
                           <div class="form-group col-lg-3">
                              <label class="form-label" for="">{{trans('admin.Coin')}}</label>
                              <select class="select2 form-control w-100" name="Coin" required>
                                 <option value=""> {{trans('admin.Coin')}}</option>
                                 @foreach($Coins as $coin)    
                                 <option value="{{$coin->id}}" @if($coin->id  == $Sales->Coin) selected @endif>
                                   {{app()->getLocale() == 'ar' ?$coin->Arabic_Name :$coin->English_Name}} 
                                 </option>
                                 @endforeach  
                              </select>
                           </div>
                           <div class="form-group col-lg-3">
                              <label>{{trans('admin.Draw')}}</label>  
                              <input type="number" step="any" name="Draw" class="form-control" value="{{$Sales->Draw}}" required>
                           </div>
                           <div class="form-group col-lg-3">
                              <label>{{trans('admin.Shift_Pass')}}</label>  
                              <input type="number" step="any" name="Shift_Pass" class="form-control" value="{{$Sales->Shift_Pass}}" required>
                           </div>
                           <div class="form-group col-lg-3">
                              <label class="form-label" for=""> {{trans('admin.Payment_Method')}}</label>
                              <select class="select2 form-control w-100" name="Payment_Method" id="Payment_Method"  required>
                              <option value="Cash" @if($Sales->Payment_Method == 'Cash') selected @endif>{{trans('admin.Cash')}} </option>
                              <option value="Later" @if($Sales->Payment_Method == 'Later') selected @endif>{{trans('admin.Later')}}</option>
                              <option value="Check" @if($Sales->Payment_Method == 'Check') selected @endif>{{trans('admin.Check')}}</option>
                              <option value="Installment" @if($Sales->Payment_Method == 'Installment') selected @endif>{{trans('admin.Installment')}}</option> 
                              </select>
                           </div>
                           <div class="form-group col-lg-3">
                              <label class="form-label" for="">{{trans('admin.Status')}}</label>
                              <select class="select2 form-control w-100" name="Status"  id="Status"  required>
                              <option value="1" @if($Sales->Status == 1) selected @endif>{{trans('admin.Recived')}} </option>
                              <option value="0" @if( $Sales->Status == 0) selected @endif>{{trans('admin.Pending')}}</option>
                              </select>
                           </div>
                                 <div class="form-group col-lg-3">
                              <label class="form-label" for="">{{trans('admin.OtherStoresQty')}}</label>
                              <select class="select2 form-control w-100" name="StoresQty"   required>
                              <option value="1" @if($Sales->StoresQty == 1) selected @endif>{{trans('admin.All')}} </option>
                              <option value="0" @if( $Sales->StoresQty == 0) selected @endif>{{trans('admin.Choiced_Stores')}}</option>
                              </select>
                           </div>
                            
                           <div class="form-group col-lg-3">
                              <label class="form-label" for="">{{trans('admin.Delegate')}}</label>
                              <select class="select2 form-control w-100" name="Delegate">
                                 <option value=""> {{trans('admin.Delegate')}}</option>
                                 @foreach($Employessss as $emp)    
                                 <option value="{{$emp->id}}" @if($emp->id  == $Sales->Delegate) selected @endif>
                      
                             {{app()->getLocale() == 'ar' ?$emp->Name :$emp->NameEn}}         
                                 </option>
                                 @endforeach  
                              </select>
                           </div>
                                      <div class="form-group col-lg-3">
                              <label class="form-label" for="">{{trans('admin.Delivery')}}</label>
                              <select class="select2 form-control w-100" name="Delivery">
                                 <option value=""> {{trans('admin.Delivery')}}</option>
                                 @foreach($Deliveries as $del)    
                                 <option value="{{$del->id}}" @if($del->id  == $Sales->Delivery) selected @endif>
                     
                                    {{app()->getLocale() == 'ar' ?$del->Name :$del->NameEn}}         
                                 </option>
                                 @endforeach  
                              </select>
                           </div> 
                            
                            
                           <div class="form-group col-lg-3">
                              <label class="form-label" for="">{{trans('admin.Account')}}</label>
                              <select class="select2 form-control w-100"  name="Client" required>
                                 <option value=""> {{trans('admin.Account')}}</option>
                                 @foreach($Vendors as $vend)    
                                 <option value="{{$vend->id}}" @if($vend->id  == $Sales->Client) selected @endif>
                  
                              {{app()->getLocale() == 'ar' ?$vend->Name :$vend->NameEn}}           
                                 </option>
                                 @endforeach  
                              </select>
                           </div>
                            <div class="form-group col-md-3">
                              <label class="form-label" for=""> {{trans('admin.Vendor_And_Clients')}} </label>
                              <select class="select2 form-control w-100" name="V_and_C">
                                 <option value="1" @if($Sales->V_and_C == 1) selected @endif>{{trans('admin.Yes')}}</option>
                                  
                                 <option value="0" @if($Sales->V_and_C == 0) selected @endif>{{trans('admin.No')}}</option>
                                
                              </select>
                           </div>
                     
                            <div class="form-group col-md-3">
                              <label class="form-label" for=""> {{trans('admin.Discount')}}</label>
                              <select class="select2 form-control w-100" name="Discount">
                                 <option value="1" @if($Sales->Discount == 1) selected @endif> {{trans('admin.Precent')}} </option>
                                 <option value="0" @if($Sales->Discount == 0) selected @endif>{{trans('admin.Number')}} </option>
                              </select>
                           </div>
                    
                           <div class="form-group col-md-3">
                              <label class="form-label" for="">{{trans('admin.Empp')}}</label>
                              <select class="select2 form-control w-100" name="Empp">
                                 <option value="1" @if($Sales->Empp == 1) selected @endif>  {{trans('admin.Yes')}}</option>
                                 <option value="0" @if($Sales->Empp == 0) selected @endif> {{trans('admin.No')}}  </option>
                              </select>
                           </div>
                    
                           
                           <div class="form-group col-lg-3" style="display: none">
                              <label class="form-label" for="">{{trans('admin.Appear')}}</label>
                              {{trans('admin.Brand')}} 
                              <input type="checkbox" name="Brand" value="1" @if($Sales->Brand == 1) checked @endif>
                              {{trans('admin.Group')}} 
                              <input type="checkbox" name="Group" value="1" @if($Sales->Group == 1) checked @endif>          
                              {{trans('admin.English_Name')}} 
                              <input type="checkbox" name="English_Name" value="1" @if($Sales->English_Name == 1) checked @endif>
                              {{trans('admin.Expire')}} 
                              <input type="checkbox" name="Expire" value="1" @if($Sales->Expire == 1) checked @endif>          
                           </div>
                            <div class="form-group col-md-3">
                              <label class="form-label" for="">{{trans('admin.Price_Sale')}}</label>
                              <select class="select2 form-control w-100" name="Price_Sale">
                                 <option value="1" @if($Sales->Price_Sale == 1) selected @endif>  {{trans('admin.Open')}}</option>
                                 <option value="0" @if($Sales->Price_Sale == 0) selected @endif> {{trans('admin.Disabled')}}  </option>
                              </select>
                           </div>
                            
                                    <div class="form-group col-md-3">
                              <label class="form-label" for="">{{trans('admin.Execute_Precent')}}</label>
                              <select class="select2 form-control w-100" name="Execute_Precent">
                                 <option value="Servicee" @if($Sales->Execute_Precent == 'Servicee') selected @endif>  {{trans('admin.Servicee')}}</option>
                                 <option value="Total_Cost" @if($Sales->Execute_Precent == 'Total_Cost') selected @endif> {{trans('admin.Total_Cost')}}  </option>
                              </select>
                           </div>
                            
                                           <div class="form-group col-md-3">
                              <label class="form-label" for="">{{trans('admin.DelegateEmp')}}</label>
                              <select class="select2 form-control w-100" name="DelegateEmp">
                                 <option value="1" @if($Sales->DelegateEmp == 'Servicee') selected @endif>  {{trans('admin.Show')}}</option>
                                 <option value="0" @if($Sales->DelegateEmp == 'Total_Cost') selected @endif> {{trans('admin.Hide')}}  </option>
                              </select>
                           </div>
                            
                              <div class="form-group col-lg-3">
                           <label class="form-label" for="">{{trans('admin.TaxBill')}}</label>
                           <select class="select2 form-control w-100" name="TaxType" required>
                              <option value="0" @if($Sales->TaxType == 0) selected @endif>{{trans('admin.No_Tax')}}</option>
                              <option value="1" @if($Sales->TaxType == 1) selected @endif>{{trans('admin.Yes_Tax')}}</option>
                           </select>
                        </div>
                            
                            
                                       <div class="form-group col-lg-3">
                           <label class="form-label" for="">{{trans('admin.DiscountTaxShow')}}</label>
                           <select class="select2 form-control w-100" name="DiscountTaxShow" required>
                              <option value="0" @if($Sales->DiscountTaxShow == 0) selected @endif>{{trans('admin.No')}}</option>
                              <option value="1" @if($Sales->DiscountTaxShow == 1) selected @endif>{{trans('admin.Yes')}}</option>
                           </select>
                        </div>          
                            
                            
                            <div class="form-group col-lg-3">
                           <label class="form-label" for="">{{trans('admin.SalesOrderType')}}</label>
                           <select class="select2 form-control w-100" name="SalesOrderType" required>
                              <option value="0" @if($Sales->SalesOrderType == 0) selected @endif>{{trans('admin.Not_Hold_Qties')}}</option>
                              <option value="1" @if($Sales->SalesOrderType == 1) selected @endif>{{trans('admin.Hold_Qties')}}</option>
                           </select>
                        </div>
                            
                            
                                        
                            <div class="form-group col-lg-3">
                           <label class="form-label" for="">{{trans('admin.ECommercceSaleType')}}</label>
                           <select class="select2 form-control w-100" name="ECommercceSaleType" required>
                              <option value="0" @if($Sales->ECommercceSaleType == 0) selected @endif>{{trans('admin.Not_Hold_Qties')}}</option>
                              <option value="1" @if($Sales->ECommercceSaleType == 1) selected @endif>{{trans('admin.Hold_Qties')}}</option>
                           </select>
                                
                        </div>  
                            
                            
                            <div class="form-group col-lg-3">
                           <label class="form-label" for="">{{trans('admin.Kitchen_Order')}}</label>
                           <select class="select2 form-control w-100" name="Kitchen_Order" required>
                              <option value="0" @if($Sales->Kitchen_Order == 0) selected @endif>{{trans('admin.Screen')}}</option>
                              <option value="1" @if($Sales->Kitchen_Order == 1) selected @endif>{{trans('admin.Recipt')}}</option>
                              <option value="2" @if($Sales->Kitchen_Order == 2) selected @endif>{{trans('admin.Both')}}</option>
                           </select>
                        </div>
                            
                            
                                <div class="form-group col-lg-3">
                              <label class="form-label" for="">{{trans('admin.Waiter')}}</label>
                              <select class="select2 form-control w-100" name="Waiter" required>
                                 <option value=""> {{trans('admin.Waiter')}}</option>
                                 @foreach($Waiters as $wait)    
                                 <option value="{{$wait->id}}" @if($wait->id  == $Sales->Waiter) selected @endif>
                      
                             {{app()->getLocale() == 'ar' ?$wait->Name :$wait->NameEn}}         
                                 </option>
                                 @endforeach  
                              </select>
                           </div>         
                            
                            <div class="form-group col-lg-3">
                              <label class="form-label" for="">{{trans('admin.CoinResturantWebsite')}}</label>
                              <select class="select2 form-control w-100" name="CountryResturantWebsite" required>
                                 <option value=""> {{trans('admin.CoinResturantWebsite')}}</option>
                                 @foreach($Coins as $coin)    
                                 <option value="{{$coin->id}}" @if($coin->id  == $Sales->CountryResturantWebsite) selected @endif>
                      
                             {{app()->getLocale() == 'ar' ?$coin->Arabic_Name :$coin->English_Name}}         
                                 </option>
                                 @endforeach  
                              </select>
                           </div>
                            
                            
                                     <div class="form-group col-lg-3">
                           <label class="form-label" for="">{{trans('admin.Hall_Service_Type')}}</label>
                           <select class="select2 form-control w-100" name="Hall_Service_Type" id="Hall_Service_Type" required onchange="HallService()">
                              <option value="0" @if($Sales->Hall_Service_Type == 0) selected @endif>{{trans('admin.No')}}</option>
                              <option value="1" @if($Sales->Hall_Service_Type == 1) selected @endif>{{trans('admin.Yes')}}</option>
                           </select>
                        </div>                    
                            
                            <div class="form-group col-lg-3">
                           <label class="form-label" for="">{{trans('admin.SalesLowCostPrice')}}</label>
                           <select class="select2 form-control w-100" name="SalesLowCostPrice">
                              <option value="0" @if($Sales->SalesLowCostPrice == 0) selected @endif>{{trans('admin.No')}}</option>
                              <option value="1" @if($Sales->SalesLowCostPrice == 1) selected @endif>{{trans('admin.Yes')}}</option>
                           </select>
                        </div>       
                            
                            
                            <div class="form-group col-lg-3">
                           <label class="form-label" for="">{{trans('admin.ShowJobOrders')}}</label>
                           <select class="select2 form-control w-100" name="ShowJobOrders">
                            
                              <option value="1" @if($Sales->ShowJobOrders == 1) selected @endif>{{trans('admin.Show')}}</option>
                                 <option value="0" @if($Sales->ShowJobOrders == 0) selected @endif>{{trans('admin.Hide')}}</option>
                           </select>
                        </div>
                            
                            
                              
                                     <div class="form-group col-lg-3" id="Hall_Service_Precent" style="display: none">
                           <label class="form-label" for="">{{trans('admin.Hall_Service_Precent')}}</label>
                                  <input type="number" step="any" class="form-control" name="Hall_Service_Precent" value="{{$Sales->Hall_Service_Precent}}" >       
                            </div> 
                            
                                                                 <div class="form-group col-md-3">
                              <label class="form-label" for="">  {{trans('admin.Country')}} </label>
                              <select class="select2 form-control w-100"  name="Country" required>
                                 <option value="">{{trans('admin.Country')}}</option>
                                 @foreach($Nationality as $nation)
                                 <option value="{{$nation->id}}"  @if($nation->id  == $Sales->Country) selected @endif>

                                   {{app()->getLocale() == 'ar' ?$nation->Arabic_Name :$nation->English_Name}}      
                                 </option>
                                 @endforeach      
                              </select>
                           </div>
                            

                            
                                          <div class="form-group col-lg-3">
                           <label class="form-label" for="">{{trans('admin.Duplicate_Items')}}</label>
                           <select class="select2 form-control w-100" name="Duplicate_Items">
                              <option value="0" @if($Sales->Duplicate_Items == 0) selected @endif>{{trans('admin.No')}}</option>
                              <option value="1" @if($Sales->Duplicate_Items == 1) selected @endif>{{trans('admin.Yes')}}</option>
                           </select>
                        </div>       
                            
                            
                                          <div class="form-group col-lg-3">
                           <label class="form-label" for="">{{trans('admin.LimitSalesQty')}}</label>
                           <select class="select2 form-control w-100" name="LimitSalesQty">
                              <option value="0" @if($Sales->LimitSalesQty == 0) selected @endif>{{trans('admin.No')}}</option>
                              <option value="1" @if($Sales->LimitSalesQty == 1) selected @endif>{{trans('admin.Yes')}}</option>
                           </select>
                        </div>       
                            
                            
                            <div class="form-group col-lg-3">
                           <label class="form-label" for="">{{trans('admin.Total_Wight_Bill')}}</label>
                           <select class="select2 form-control w-100" name="Total_Wight_Bill">
                            
                              <option value="1" @if($Sales->Total_Wight_Bill == 1) selected @endif>{{trans('admin.Show')}}</option>
                                 <option value="0" @if($Sales->Total_Wight_Bill == 0) selected @endif>{{trans('admin.Hide')}}</option>
                           </select>
                        </div>
                            
                            
                            
                            


                            
                        
                           <input type="hidden" name="Mainus" value="0">   
                           <!--   <div class="form-group col-lg-3" >
                              <label class="form-label" for="">{{trans('admin.Mainus')}}</label>
                              <input type="radio" name="Mainus" value="0" checked> 
                               {{trans('admin.Yes')}} 
                              <input type="radio" name="Mainus" value="1" @if($Sales->Mainus == 1) checked @endif>
                                
                               {{trans('admin.No')}} 
                              <input type="radio" name="Mainus" value="0" @if($Sales->Mainus == 0) checked @endif>          
                                
                              </div> -->
                           <div class="col-md-12">
                              <button type="submit" class="btn btn-primary">{{trans('admin.Save')}}</button>
                           </div>
                        </form>
                        @else
                        <div  class="form-row">
                           <div class="col-md-12">
                              <div class="data-def">
                                 <div class="form-row">
                                    <div class="form-group col-md-12">
                                       <a class="btn btn-primary" href="{{url('AddDefaultSalesFirst')}}">
                                       {{trans('admin.AddNew')}}
                                       </a>   
                                    </div>
                                 </div>
                              </div>
                           </div>
                        </div>
                        @endif                   
                     </div>
                     <div class="tab-pane fade " id="tab_borders_icons-5" role="tabpanel">
                        @if(!empty($Crms))  
                        <form action="{{url('AddDefaultCrm')}}" method="post"  class="form-row">
                           {!! csrf_field() !!}  
                           @honeypot
                           <div class="form-group col-md-3">
                              <label class="form-label" for="">   {{trans('admin.Price_Level')}} </label><span class="strick">*</span>
                              <select class="select2 form-control w-100" name="Price_Level" required>
                                 <option value=""> {{trans('admin.Price_Level')}} </option>
                                 <option value="1"  @if($Crms->Price_Level == 1) selected @endif> {{trans('admin.Level1')}} </option>
                                 <option value="2"  @if($Crms->Price_Level == 2) selected @endif> {{trans('admin.Level2')}} </option>
                                 <option value="3"  @if($Crms->Price_Level == 3) selected @endif> {{trans('admin.Level3')}} </option>
                              </select>
                           </div>
                                <div class="form-group col-md-3">
                              <label class="form-label" for="">  {{trans('admin.Nationality')}} </label>
                              <select class="select2 form-control w-100"  name="Nationality" required>
                                 <option value="">{{trans('admin.Nationality')}}</option>
                                 @foreach($Nationality as $nation)
                                 <option value="{{$nation->id}}"  @if($nation->id  == $Crms->Nationality) selected @endif>

                                   {{app()->getLocale() == 'ar' ?$nation->Arabic_Name :$nation->English_Name}}      
                                 </option>
                                 @endforeach      
                              </select>
                           </div>          
                            <div class="form-group col-md-3">
                              <label class="form-label" for="">  {{trans('admin.ClientGroup')}} </label>
                              <select class="select2 form-control w-100"  name="ClientGroup" required>
                                 <option value="">{{trans('admin.ClientGroup')}}</option>
                                 @foreach($CustomersGroup as $grop)
                                 <option value="{{$grop->id}}"  @if($grop->id  == $Crms->ClientGroup) selected @endif>

                                   {{app()->getLocale() == 'ar' ?$grop->Arabic_Name :$grop->English_Name}}      
                                 </option>
                                 @endforeach      
                              </select>
                           </div>
                           <div class="form-group col-md-3">
                              <label class="form-label" for="">  {{trans('admin.Governrate')}} </label>
                              <select class="select2 form-control w-100" id="Governrate" name="Governrate" required>
                                 <option value="">{{trans('admin.Governrate')}}</option>
                                 @foreach($Governrates as $gov)
                                 <option value="{{$gov->id}}"  @if($gov->id  == $Crms->Governrate) selected @endif>  
                             {{app()->getLocale() == 'ar' ?$gov->Arabic_Name :$gov->English_Name}}         
                                 </option>
                                 @endforeach      
                              </select>
                           </div>
                           <div class="form-group col-md-3">
                              <label class="form-label" for="">  {{trans('admin.City')}}</label>
                              <select class="select2 form-control w-100" id="City" name="City" required>
                                 @if(!empty($Crms->City))     
                                 <option value="{{$Crms->City}}">
                                  
                                           {{app()->getLocale() == 'ar' ?$Crms->City()->first()->Arabic_Name :$Crms->City()->first()->English_Name}}   
                                  </option>
                                 @endif   
                              </select>
                           </div>
                           <div class="form-group col-md-3">
                              <label class="form-label" for="">   {{trans('admin.ClientStatus')}}</label>
                              <select class="select2 form-control w-100" name="ClientStatus" required>
                                 <option value="">{{trans('admin.ClientStatus')}}</option>
                                 @foreach($ClientStatus as $cls)
                                 <option value="{{$cls->id}}"  @if($cls->id  == $Crms->ClientStatus) selected @endif>  
                                 {{app()->getLocale() == 'ar' ?$cls->Arabic_Name :$cls->English_Name}}             
                                 </option>
                                 @endforeach      
                              </select>
                           </div>
                           <div class="form-group col-md-3">
                              <label class="form-label" for="">  {{trans('admin.Platforms')}} </label>
                              <select class="select2 form-control w-100" id="Platform" name="Platforms" required>
                                 <option value="">{{trans('admin.Platforms')}}</option>
                                 @foreach($Platforms as $pl)
                                 <option value="{{$pl->id}}"  @if($pl->id  == $Crms->Platforms) selected @endif>

                                         {{app()->getLocale() == 'ar' ?$pl->Arabic_Name :$pl->English_Name}}       
                                 </option>
                                 @endforeach   
                              </select>
                           </div>
                           <div class="form-group col-md-3">
                              <label class="form-label" for="">  {{trans('admin.Campagin')}} </label>
                              <select class="select2 form-control w-100" id="Campagin" name="Campagin" required>
                                 @if(!empty($Crms->Campagin))     
                                 <option value="{{$Crms->Campagin}}">
                                  
                                       {{app()->getLocale() == 'ar' ?$Crms->Campagin()->first()->Arabic_Name :$Crms->Campagin()->first()->English_Name}}       
                                  </option>
                                 @endif   
                              </select>
                           </div>
                           <div class="form-group col-md-3">
                              <label class="form-label" for="">  {{trans('admin.Activity')}}</label>
                              <select class="select2 form-control w-100" name="Activity" required>
                                 <option value="">{{trans('admin.Activity')}}</option>
                                 @foreach($Activites as $act)
                                 <option value="{{$act->id}}"  @if($act->id  == $Crms->Activity) selected @endif> 
                               {{app()->getLocale() == 'ar' ?$act->Arabic_Name :$act->English_Name}}             
                                 </option>
                                 @endforeach    
                              </select>
                           </div>
                           <div class="form-group col-md-3">
                              <label class="form-label" for="">  {{trans('admin.Responsible')}}</label>
                              <select class="select2 form-control w-100" name="Responsible" required>
                                 <option value="">{{trans('admin.Responsible')}}</option>
                                 @foreach($Employess as $emp)
                                 <option value="{{$emp->id}}"  @if($emp->id  == $Crms->Responsible) selected @endif>
                 
                             {{app()->getLocale() == 'ar' ?$emp->Name :$emp->NameEn}}         
                                 </option>
                                 @endforeach   
                              </select>
                           </div>
                           <div class="form-group col-md-3">
                              <label class="form-label" for="">  {{trans('admin.Client_Delegate')}}</label>
                              <select class="select2 form-control w-100" name="Client_Delegate" required>
                                 <option value="">{{trans('admin.Client_Delegate')}}</option>
                                 <option value="1" @if($Crms->Client_Delegate == 1) selected @endif>{{trans('admin.Yes')}}</option>
                                 <option value="0" @if($Crms->Client_Delegate == 0) selected @endif>{{trans('admin.No')}}</option>
                              </select>
                           </div>
                           <div class="col-md-12">
                              <button type="submit" class="btn btn-primary">{{trans('admin.Save')}}</button>
                           </div>
                        </form>
                        @else
                        <div  class="form-row">
                           <div class="col-md-12">
                              <div class="data-def">
                                 <div class="form-row">
                                    <div class="form-group col-md-12">
                                       <a class="btn btn-primary" href="{{url('AddDefaultCrmFirst')}}">
                                       {{trans('admin.AddNew')}}
                                       </a>   
                                    </div>
                                 </div>
                              </div>
                           </div>
                        </div>
                        @endif        
                     </div>
                     <div class="tab-pane fade " id="tab_borders_icons-8" role="tabpanel">
                        @if(!empty($Companies))            
                        <form action="{{url('AddDefaultCompany')}}" method="post" enctype="multipart/form-data" class="form-row">
                           {!! csrf_field() !!}
                           @honeypot
                           <div class="col-md-12">
                              <div class="data-def">
                                 <div class="form-row">
                                    <div class="form-group col-md-4">
                                       <label class="form-label" for=""> {{trans('admin.Company_Arabic_Name')}}   </label>
                                       <input type="text" name="Name" value="{{$Companies->Name}}" class="form-control" required>
                                    </div>
                                     
                                                 <div class="form-group col-md-4">
                                       <label class="form-label" for=""> {{trans('admin.Company_English_Name')}}   </label>
                                       <input type="text" name="NameEn" value="{{$Companies->NameEn}}" class="form-control" required>
                                    </div>            
                                     
                                     
                                     <div class="form-group col-md-4">
                                       <label class="form-label" for=""> {{trans('admin.Email')}}   </label>
                                       <input type="email" name="Email" value="{{$Companies->Email}}" class="form-control" required>
                                    </div>
                                     
                                    <div class="form-group col-md-3">
                                       <label class="form-label" for=""> {{trans('admin.Phone')}}   </label>
                                       <input type="number" name="Phone1" value="{{$Companies->Phone1}}" class="form-control">
                                    </div>
                                    <div class="form-group col-md-3">
                                       <label class="form-label" for=""> {{trans('admin.Phone2')}}   </label>
                                       <input type="number" name="Phone2" value="{{$Companies->Phone2}}" class="form-control">
                                    </div>
                                           <div class="form-group col-md-3">
                                       <label class="form-label" for=""> {{trans('admin.Phone3')}}   </label>
                                       <input type="number" name="Phone3" value="{{$Companies->Phone3}}" class="form-control">
                                    </div>
                                    <div class="form-group col-md-3">
                                       <label class="form-label" for=""> {{trans('admin.Phone4')}}   </label>
                                       <input type="number" name="Phone4" value="{{$Companies->Phone4}}" class="form-control">
                                    </div>
                                    <div class="form-group col-md-6">
                                       <label class="form-label" for=""> {{trans('admin.Company_Address_Arabic')}}   </label>
                                       <input type="text" name="Address" value="{{$Companies->Address}}" class="form-control">
                                    </div>      
                                     
                                     <div class="form-group col-md-6">
                                       <label class="form-label" for=""> {{trans('admin.Company_Address_English')}}   </label>
                                       <input type="text" name="AddressEn" value="{{$Companies->AddressEn}}" class="form-control">
                                    </div>
                                  
                                    <div class="form-group col-md-6">
                                       <label class="form-label" for="">    {{trans('admin.Logo')}}    </label>
                                       <input type="file" name="Logo">
                                    </div>
                                    <div class="form-group col-md-6">
                                       <img class="img-fluid" src="{{URL::to($Companies->Logo)}}">  
                                       <input type="hidden" name="Logos" value="{{$Companies->Logo}}">                   
                                    </div>
                                    <div class="form-group col-md-6">
                                       <label class="form-label" for="">    {{trans('admin.Icon')}}    </label>
                                       <input type="file" name="Icon">
                                    </div>
                                    <div class="form-group col-md-6">
                                       <img class="img-fluid" src="{{URL::to($Companies->Icon)}}">  
                                       <input type="hidden" name="Icons" value="{{$Companies->Icon}}">                
                                    </div>
                                           <div class="form-group col-md-6">
                                       <label class="form-label" for="">    {{trans('admin.Logo_Store')}}    </label>
                                       <input type="file" name="Logo_Store">
                                    </div>
                                     
                                    <div class="form-group col-md-6">
                                       <img class="img-fluid" src="{{URL::to($Companies->Logo_Store)}}">  
                                       <input type="hidden" name="Logo_StoreS" value="{{$Companies->Logo_Store}}">
                                    </div> 
                                    <div class="form-group col-md-6">
                                       <label class="form-label" for="">    {{trans('admin.Icon_Store')}}    </label>
                                       <input type="file" name="Icon_Store">
                                    </div>
                                    <div class="form-group col-md-6">
                                       <img class="img-fluid" src="{{URL::to($Companies->Icon_Store)}}">  
                                       <input type="hidden" name="Icon_StoreS" value="{{$Companies->Icon_Store}}">
                                    </div>  
                                     
                                     
                                         @if($Modules->Resturant  ==  1)
                                               <div class="form-group col-md-6">
                                       <label class="form-label" for="">    PDF Menu    </label>
                                       <input type="file" name="PDF">
                                    </div>
                                    <div class="form-group col-md-6">
                                   <a class="btn btn-primary" download href="{{URL::to($Companies->PDF)}}"><i class="fal fa-download"></i></a>
                                       <input type="hidden" name="PDFs" value="{{$Companies->PDF}}">
                                    </div>  
                                     @endif
                                     
                                     
                                                 <div class="form-group col-md-6">
                                       <label class="form-label" for="">    {{trans('admin.Location')}}    </label>
                                       <input type="text" name="Location" class="form-control" required value="{{$Companies->Location}}">
                                    </div>
                                    <div class="form-group col-md-6">
                                      {!! $Companies->Location !!}
                                    </div>  
                                     
                                           <div class="form-group col-md-12">
                                       <label class="form-label" for="">    {{trans('admin.Font_Type')}}    </label>
                              <select class="select2 form-control" name="Font_Type" required>
                    <option value="1" @if($Companies->Font_Type == 1) selected @endif>{{trans('admin.Font_1')}}</option>    
                  <option value="2" @if($Companies->Font_Type  == 2) selected @endif>{{trans('admin.Font_2')}}</option>  
                  <option value="3" @if($Companies->Font_Type  == 3) selected @endif>{{trans('admin.Font_3')}}</option>  
                  <option value="4" @if($Companies->Font_Type  == 4) selected @endif>{{trans('admin.Font_4')}}</option>  
                  <option value="5" @if($Companies->Font_Type  == 5) selected @endif>{{trans('admin.Font_5')}}</option>  
                              </select>
                                    </div>     
                                     
                                     


                                     
                                     
                                     
                                     @if(in_array(auth()->guard('admin')->user()->email, ['<EMAIL>', '<EMAIL>']))
                                    <div class="form-group col-md-4">
                                       <label class="form-label" for="">    {{trans('admin.View')}}    </label>
                              <select class="select2 form-control" name="View" required>
                    <option value="0" @if($Companies->View == 0) selected @endif>{{trans('admin.Default')}}</option>    
                  <option value="1" @if($Companies->View  == 1) selected @endif>{{trans('admin.ECommerce')}}</option>  
                  <option value="2" @if($Companies->View  == 2) selected @endif>{{trans('admin.ResturantMenu')}}</option>  
                              </select>
                                    </div>
                                                       <div class="form-group col-md-4">
                                       <label class="form-label" for="">    {{trans('admin.DB_Backup')}}    </label>
                              <select class="select2 form-control" name="DB_Backup" required>
                    <option value="0" @if($Companies->DB_Backup == 0) selected @endif>Local</option>    
                  <option value="1" @if($Companies->DB_Backup  == 1) selected @endif>Web</option>  
                              </select>
                                    </div>                 
                                     
                                     <div class="form-group col-md-4">
                                       <label class="form-label" for="">    {{trans('admin.HomeMainScreen')}}    </label>
                              <select class="select2 form-control" name="HomeMainScreen" required>
                    <option value="0" @if($Companies->HomeMainScreen == 0) selected @endif>{{trans('admin.Briefs')}}</option>    
                  <option value="1" @if($Companies->HomeMainScreen  == 1) selected @endif>{{trans('admin.Reports')}}</option>  
                              </select>
                                    </div>
                                     
                                     
                                                  <div class="form-group col-md-6">
                                       <label class="form-label" for=""> {{trans('admin.Welcome_Arabic_Word_App')}}   </label>
                                       <input type="text" name="Welcome_Arabic_Word_App" value="{{$Companies->Welcome_Arabic_Word_App}}" class="form-control">
                                    </div> 
                                               
                                                  <div class="form-group col-md-6">
                                       <label class="form-label" for=""> {{trans('admin.Welcome_English_Word_App')}}   </label>
                                       <input type="text" name="Welcome_English_Word_App" value="{{$Companies->Welcome_English_Word_App}}" class="form-control">
                                    </div> 
                                     

                                     @else
                                     
                                     <input type="hidden" name="View" value="{{$Companies->View}}">
                                     <input type="hidden" name="DB_Backup" value="{{$Companies->DB_Backup}}">
                                     <input type="hidden" name="HomeMainScreen" value="{{$Companies->HomeMainScreen}}">
                                     <input type="hidden" name="Welcome_Arabic_Word_App" value="{{$Companies->Welcome_Arabic_Word_App}}">
                                     <input type="hidden" name="Welcome_English_Word_App" value="{{$Companies->Welcome_English_Word_App}}">
                                     @endif
                                     
                               
                                 </div>
                                  
                                  <div class="row m-2">
          <button style="width: 100%;    background: linear-gradient(1deg, #7d0000, black);font-size: 30px;" class="btn btn-primary" type="button" data-toggle="collapse" data-target="#collapseExample" aria-expanded="false" aria-controls="collapseExample">
   {{trans('admin.Print')}}
  </button>
                                      
                        </div>              
                                  
              <div class="row collapse" id="collapseExample" style="background: linear-gradient(0deg, #510000, #000000);color: #ffd806;padding: 35px;border-radius: 6%;"> 
                                     
                                    <div class="form-group col-md-6">
                                       <label class="form-label" for=""> {{trans('admin.Arabic_Print_Text')}}   </label>
                                       <input type="text" name="Print_Text" value="{{$Companies->Print_Text}}" class="form-control" required>
                                    </div>
                                     
                         <div class="form-group col-md-6">
                                       <label class="form-label" for=""> {{trans('admin.English_Print_Text')}}   </label>
                                       <input type="text" name="Print_Text_En" value="{{$Companies->Print_Text_En}}" class="form-control" required>
                                    </div>
                  
                  
                                    <div class="form-group col-md-6" style="display: none">
                                       <label class="form-label" for=""> {{trans('admin.Arabic_Print_Text_Footer')}}   </label>
                                       <textarea class="js-summernote" id="saveToLocal" name="">
                                       {{$Companies->Print_Text_Footer}}          
                                       </textarea>
                                    </div>
                                     
                                    <div class="form-group col-md-6">
                                       <label class="form-label" for=""> {{trans('admin.Arabic_Print_Text_Footer')}}   </label>
                                       <textarea class="js-summernote" id="saveToLocal" name="Print_Text_Footer">
                                       {{$Companies->Print_Text_Footer}}          
                                       </textarea>
                                    </div>
                                     
                  
                    <div class="form-group col-md-6" style="display: none">
                                       <label class="form-label" for=""> {{trans('admin.English_Print_Text_Footer')}}   </label>
                                       <textarea class="js-summernote" id="saveToLocal" name="">
                                       {{$Companies->Print_Text_Footer_En}}          
                                       </textarea>
                                    </div>
                                     
                                    <div class="form-group col-md-6">
                                       <label class="form-label" for=""> {{trans('admin.English_Print_Text_Footer')}}   </label>
                                       <textarea class="js-summernote" id="saveToLocal" name="Print_Text_Footer_En">
                                       {{$Companies->Print_Text_Footer_En}}          
                                       </textarea>
                                    </div>
                  

                                      <div class="form-group col-md-6">
                                       <label class="form-label" for=""> {{trans('admin.Arabic_Print_Text_Footer_Manufacturing')}}   </label>
                                       <textarea class="js-summernote" id="saveToLocal" name="Print_Text_Footer_Manufacturing">
                                       {{$Companies->Print_Text_Footer_Manufacturing}}          
                                       </textarea>
                                    </div>
                                    <div class="form-group col-md-6" style="display: none">
                                       <label class="form-label" for=""> {{trans('admin.Arabic_Print_Text_Footer_Manufacturing')}}   </label>
                                       <textarea class="js-summernote" id="saveToLocal" name="">
                                       {{$Companies->Print_Text_Footer_Manufacturing}}          
                                       </textarea>
                                    </div>
                  
                  
                              <div class="form-group col-md-6">
                                       <label class="form-label" for=""> {{trans('admin.English_Print_Text_Footer_Manufacturing')}}   </label>
                                       <textarea class="js-summernote" id="saveToLocal" name="Print_Text_Footer_Manufacturing_En">
                                       {{$Companies->Print_Text_Footer_Manufacturing_En}}          
                                       </textarea>
                                    </div>
                                    <div class="form-group col-md-6" style="display: none">
                                       <label class="form-label" for=""> {{trans('admin.English_Print_Text_Footer_Manufacturing')}}   </label>
                                       <textarea class="js-summernote" id="saveToLocal" name="">
                                       {{$Companies->Print_Text_Footer_Manufacturing_En}}          
                                       </textarea>
                                    </div>
                  
   
                             <div class="form-group col-md-6" style="display: none">
                                       <label class="form-label" for=""> {{trans('admin.Arabic_Print_Text_Footer_Sales')}}   </label>
                                       <textarea class="js-summernote" id="saveToLocal" name="">
                                       {{$Companies->Print_Text_Footer_Sales}}          
                                       </textarea>
                                    </div>
                                    <div class="form-group col-md-6">
                                       <label class="form-label" for=""> {{trans('admin.Arabic_Print_Text_Footer_Sales')}}   </label>
                                       <textarea class="js-summernote" id="saveToLocal" name="Print_Text_Footer_Sales">
                                       {{$Companies->Print_Text_Footer_Sales}}          
                                       </textarea>
                                    </div>
                  
                  
                  
                       <div class="form-group col-md-6" style="display: none">
                                       <label class="form-label" for=""> {{trans('admin.English_Print_Text_Footer_Sales')}}   </label>
                                       <textarea class="js-summernote" id="saveToLocal" name="">
                                       {{$Companies->Print_Text_Footer_Sales}}          
                                       </textarea>
                                    </div>
                                    <div class="form-group col-md-6">
                                       <label class="form-label" for=""> {{trans('admin.English_Print_Text_Footer_Sales')}}   </label>
                                       <textarea class="js-summernote" id="saveToLocal" name="Print_Text_Footer_Sales_En">
                                       {{$Companies->Print_Text_Footer_Sales_En}}          
                                       </textarea>
                                    </div>
                  

                  
                          <div class="form-group col-md-6" style="display: none">
                                       <label class="form-label" for=""> {{trans('admin.Arabic_Print_Text_Footer_Quote')}}   </label>
                                       <textarea class="js-summernote" id="saveToLocal" name="">
                                       {{$Companies->Print_Text_Footer_Quote}}          
                                       </textarea>
                                    </div>
                  
                                    <div class="form-group col-md-6">
                                       <label class="form-label" for=""> {{trans('admin.Arabic_Print_Text_Footer_Quote')}}   </label>
                                       <textarea class="js-summernote" id="saveToLocal" name="Print_Text_Footer_Quote">
                                       {{$Companies->Print_Text_Footer_Quote}}          
                                       </textarea>
                                    </div>
                  
                  
                  
                         <div class="form-group col-md-6" style="display: none">
                                       <label class="form-label" for=""> {{trans('admin.English_Print_Text_Footer_Quote')}}   </label>
                                       <textarea class="js-summernote" id="saveToLocal" name="">
                                       {{$Companies->Print_Text_Footer_Quote_En}}          
                                       </textarea>
                                    </div>
                  
                                    <div class="form-group col-md-6">
                                       <label class="form-label" for=""> {{trans('admin.English_Print_Text_Footer_Quote')}}   </label>
                                       <textarea class="js-summernote" id="saveToLocal" name="Print_Text_Footer_Quote_En">
                                       {{$Companies->Print_Text_Footer_Quote_En}}          
                                       </textarea>
                                    </div>
                  
             
                  
                  
                  
                                    <div class="form-group col-md-6">
                                       <label class="form-label" for=""> {{trans('admin.Arabic_Print_Text_Footer_Secretariat')}}   </label>
                                       <input type="text" name="Print_Text_Footer_Secretariat" value="{{$Companies->Print_Text_Footer_Secretariat}}" class="form-control">
                                    </div>      
                  
                                 <div class="form-group col-md-6">
                                       <label class="form-label" for=""> {{trans('admin.English_Print_Text_Footer_Secretariat')}}   </label>
                                       <input type="text" name="Print_Text_Footer_Secretariat_En" value="{{$Companies->Print_Text_Footer_Secretariat_En}}" class="form-control">
                                    </div>
                  
                  
                  
                                    <div class="form-group col-md-6">
                                       <label class="form-label" for=""> {{trans('admin.Arabic_Name_Sales_Bill')}}   </label>
                                       <input type="text" name="Name_Sales_Bill" value="{{$Companies->Name_Sales_Bill}}" class="form-control">
                                    </div>     
                  
                  <div class="form-group col-md-6">
                                       <label class="form-label" for=""> {{trans('admin.English_Name_Sales_Bill')}}   </label>
                                       <input type="text" name="Name_Sales_Bill_En" value="{{$Companies->Name_Sales_Bill_En}}" class="form-control">
                                    </div>
                  
                  
                                    <div class="form-group col-md-6">
                                       <label class="form-label" for=""> {{trans('admin.Arabic_Name_Sales_Order_Bill')}}   </label>
                                       <input type="text" name="Name_Sales_Order_Bill" value="{{$Companies->Name_Sales_Order_Bill}}" class="form-control">
                                    </div>    
                  
                  <div class="form-group col-md-6">
                                       <label class="form-label" for=""> {{trans('admin.English_Name_Sales_Order_Bill')}}   </label>
                                       <input type="text" name="Name_Sales_Order_Bill_En" value="{{$Companies->Name_Sales_Order_Bill_En}}" class="form-control">
                                    </div>
                  
                  
                                    <div class="form-group col-md-6">
                                       <label class="form-label" for=""> {{trans('admin.Arabic_Name_Quote_Bill')}}   </label>
                                       <input type="text" name="Name_Quote_Bill" value="{{$Companies->Name_Quote_Bill}}" class="form-control">
                                    </div>    
                  
                  <div class="form-group col-md-6">
                                       <label class="form-label" for=""> {{trans('admin.English_Name_Quote_Bill')}}   </label>
                                       <input type="text" name="Name_Quote_Bill_En" value="{{$Companies->Name_Quote_Bill_En}}" class="form-control">
                                    </div>
                  
                  
                  
                  
                                <div class="form-group col-md-12">
                                       <label class="form-label" for="">    {{trans('admin.Bill_View')}} ({{trans('admin.Sales')}})    </label>
                             <select class="select2 form-control" name="Bill_View" required>
                                <option value="1" @if($Companies->Bill_View == 1) selected @endif>View 1</option>    
                                <option value="2" @if($Companies->Bill_View == 2) selected @endif>View 2</option>    
                            
                                    
                                    </select>              
                                    </div>
                  
                  
                                    <div class="form-group col-md-12">
                                       <label class="form-label" for="">    {{trans('admin.Seal')}}    </label>
                                       <input type="file" name="Seal">
                                       <img class="img-fluid" src="{{URL::to($Companies->Seal)}}">  
                                       <input type="hidden" name="Seals" value="{{$Companies->Seal}}">                
                                    </div>
    
                                        </div>
 
                                  
                                   <div class="row m-2">
          <button style="width: 100%;    background: linear-gradient(1deg, #f1e1f0, #00000061);color: black;font-size: 30px;" class="btn btn-primary" type="button" data-toggle="collapse" data-target="#collapseExample1" aria-expanded="false" aria-controls="collapseExample">
   {{trans('admin.Commercial')}}
  </button>
                                      
                        </div>                              
                                  
                                  
               <div class="row collapse" id="collapseExample1" style="border: 1px solid #ccc;border-radius: 5px;background: #8000801f;padding: 10px;margin: 5px;">
                                     
                                      <div class="form-group col-md-4">
                                       <label class="form-label" for=""> {{trans('admin.Commercial_Record')}}   </label>
                                       <input type="text" name="Commercial_Record" value="{{$Companies->Commercial_Record}}" class="form-control">
                                    </div>
                                    
                                    <div class="form-group col-md-4">
                                       <label class="form-label" for=""> {{trans('admin.Tax_File_Number')}}   </label>
                                       <input type="text" name="Tax_File_Number" value="{{$Companies->Tax_File_Number}}" class="form-control">
                                    </div>
                                               
                                                <div class="form-group col-md-4">
                                       <label class="form-label" for=""> {{trans('admin.Tax_Registration_Number')}}   </label>
                                       <input type="text" name="Tax_Registration_Number" value="{{$Companies->Tax_Registration_Number}}" class="form-control">
                                    </div>      
                                               
                                               
                                                     <div class="form-group col-md-4">
                                       <label class="form-label" for=""> {{trans('admin.Tax_activity_code')}}   </label>
                                       <input type="text" name="Tax_activity_code" value="{{$Companies->Tax_activity_code}}" class="form-control">
                                    </div>   
                                               
                                           <div class="form-group col-md-4">
                                       <label class="form-label" for="">    {{trans('admin.work_nature')}}    </label>
                              <select class="select2 form-control" name="work_nature" required>
                    <option value="P" @if($Companies->work_nature == 'P') selected @endif>{{trans('admin.Person')}}</option>    
                  <option value="B" @if($Companies->work_nature  == 'B') selected @endif>{{trans('admin.Egyptian_trading_company')}}</option>  
                  <option value="F" @if($Companies->work_nature  == 'F') selected @endif>{{trans('admin.foreign_trading_company')}}</option>  
                              </select>
                                    </div>            
                                     
                                     
                     <div class="form-group col-md-4">
                              <label class="form-label" for="">  {{trans('admin.Governrate')}} </label>
                              <select class="select2 form-control w-100 Governrate"  name="Governrate" required>
                                 <option value="">{{trans('admin.Governrate')}}</option>
                                 @foreach($Governrates as $gov)
                                 <option value="{{$gov->id}}"  @if($gov->id  == $Companies->Governrate) selected @endif>
                  
                               {{app()->getLocale() == 'ar' ?$gov->Arabic_Name :$gov->English_Name}}          
                                 </option>
                                 @endforeach      
                              </select>
                           </div>
                           <div class="form-group col-md-4">
                              <label class="form-label" for="">  {{trans('admin.City')}}</label>
                              <select class="select2 form-control w-100 City"  name="City" required>
                                 @if(!empty($Companies->City))     
                                 <option value="{{$Companies->City}}">
                                  
                           {{app()->getLocale() == 'ar' ?$Companies->City()->first()->Arabic_Name :$Companies->City()->first()->English_Name}}             
                                  </option>
                                 @endif   
                              </select>
                           </div>  
                                               
                                <div class="form-group col-md-4">
                              <label class="form-label" for="">  {{trans('admin.Place')}}</label>
                              <select class="select2 form-control w-100 Place"  name="Place" required>
                                 @if(!empty($Companies->Place))     
                                 <option value="{{$Companies->Place}}">
                                  
                                      {{app()->getLocale() == 'ar' ?$Companies->Place()->first()->Arabic_Name :$Companies->Place()->first()->English_Name}}  
                                  </option>
                                 @endif   
                              </select>
                           </div> 
                                                    <div class="form-group col-md-4">
                              <label class="form-label" for="">  {{trans('admin.Nationality')}} </label>
                              <select class="select2 form-control w-100"  name="Nationality" required>
                                 <option value="">{{trans('admin.Nationality')}}</option>
                                 @foreach($Nationality as $nation)
                                 <option value="{{$nation->id}}"  @if($nation->id  == $Companies->Nationality) selected @endif>

                                   {{app()->getLocale() == 'ar' ?$nation->Arabic_Name :$nation->English_Name}}      
                                 </option>
                                 @endforeach      
                              </select>
                           </div>


                                                                    <div class="form-group col-md-4">
                                       <label class="form-label" for=""> {{trans('admin.Buliding_Num')}}   </label>
                                       <input type="text" name="Buliding_Num" value="{{$Companies->Buliding_Num}}" class="form-control">
                                    </div> 
                                                                    <div class="form-group col-md-4">
                                       <label class="form-label" for=""> {{trans('admin.Street')}}   </label>
                                       <input type="text" name="Street" value="{{$Companies->Street}}" class="form-control">
                                    </div> 
                                               
                                                                    <div class="form-group col-md-4">
                                       <label class="form-label" for=""> {{trans('admin.Postal_Code')}}   </label>
                                       <input type="text" name="Postal_Code" value="{{$Companies->Postal_Code}}" class="form-control">
                                    </div> 
                                                                    <div class="form-group col-md-4">
                                       <label class="form-label" for=""> {{trans('admin.tax_magistrate')}}   </label>
                                       <input type="text" name="tax_magistrate" value="{{$Companies->tax_magistrate}}" class="form-control">
                                    </div> 
                                               
                                                                    <div class="form-group col-md-4">
                                       <label class="form-label" for=""> Client ID  </label>
                                       <input type="text" name="Client_ID" value="{{$Companies->Client_ID}}" class="form-control">
                                    </div> 
                                               
                                                                    <div class="form-group col-md-4">
                                       <label class="form-label" for=""> Secret ID  </label>
                                       <input type="text" name="Serial_Client_ID" value="{{$Companies->Serial_Client_ID}}" class="form-control">
                                    </div> 
                                               
                                                                 <div class="form-group col-md-4">
                                       <label class="form-label" for="">    {{trans('admin.Version_Type')}}    </label>
                              <select class="select2 form-control" name="Version_Type" required>
                    <option value="">{{trans('admin.Version_Type')}}</option>    
                  <option value="0.9" @if($Companies->Version_Type  == 0.9) selected @endif>0.9</option>  
                  <option value="1.0" @if($Companies->Version_Type  == 1.0) selected @endif>1.0</option>  
                              </select>
                                    </div> 
                                               
                                                                                             <div class="form-group col-md-4">
                                       <label class="form-label" for="">    {{trans('admin.Invoice_Type')}}    </label>
                              <select class="select2 form-control" name="Invoice_Type" required>
                    <option value="">{{trans('admin.Invoice_Type')}}</option>    
                  <option value="Real" @if($Companies->Invoice_Type  == 'Real') selected @endif> {{trans('admin.Real')}} </option>  
                  <option value="Experimental" @if($Companies->Invoice_Type  == 'Experimental') selected @endif> {{trans('admin.Experimental')}} </option>  
                              </select>
                                    </div> 
                                               
                                                               
                                                                    <div class="form-group col-md-4">
                                       <label class="form-label" for=""> {{trans('admin.Computer_SN')}}   </label>
                                       <input type="text" name="Computer_SN" value="{{$Companies->Computer_SN}}" class="form-control" required>
                                    </div> 
                                          
                                               
                                                                                  <div class="form-group col-md-4">
                                       <label class="form-label" for=""> POS Version   </label>
                                       <input type="text" name="POS_Version" value="{{$Companies->POS_Version}}" class="form-control" required>
                                    </div> 
                                               
                                               
                                                               
                                                                    <div class="form-group col-md-4">
                                       <label class="form-label" for=""> {{trans('admin.Floor')}}   </label>
                                       <input type="text" name="Floor" value="{{$Companies->Floor}}" class="form-control">
                                    </div> 
                                               
                                                               
                                                                    <div class="form-group col-md-4">
                                       <label class="form-label" for=""> {{trans('admin.Room')}}   </label>
                                       <input type="text" name="Room" value="{{$Companies->Room}}" class="form-control">
                                    </div> 
                                               
                                                               
                                                                    <div class="form-group col-md-4">
                                       <label class="form-label" for=""> {{trans('admin.Landmark')}}   </label>
                                       <input type="text" name="Landmark" value="{{$Companies->Landmark}}" class="form-control">
                                    </div> 
                                               
                                                               
                                                                    <div class="form-group col-md-4">
                                       <label class="form-label" for=""> {{trans('admin.Add_Info')}}   </label>
                                       <input type="text" name="Add_Info" value="{{$Companies->Add_Info}}" class="form-control">
                                    </div> 
                                           
                                               
                                                                                    <div class="form-group col-md-4">
                                       <label class="form-label" for=""> {{trans('admin.Path')}}   </label>
                                       <input type="text" name="Path" value="{{$Companies->Path}}" class="form-control">
                                    </div> 
                                               
                                               
                                         
                                     </div> 
                              </div>
                           </div>
                           <div class="col-md-12">
                              <button type="submit" class="btn btn-primary">{{trans('admin.Save')}}</button>
                           </div>
                        </form>
                        @else
                        <div  class="form-row">
                           <div class="col-md-12">
                              <div class="data-def">
                                 <div class="form-row">
                                    <div class="form-group col-md-12">
                                       <a class="btn btn-primary" href="{{url('AddDefaultCompanyFirst')}}">
                                       {{trans('admin.AddNew')}}
                                       </a>   
                                    </div>
                                 </div>
                              </div>
                           </div>
                        </div>
                        @endif            
                     </div>
                     <div class="tab-pane fade " id="tab_borders_icons-10" role="tabpanel">
                        @if(!empty($Maint))  
                        <form action="{{url('AddDefaultMaintaince')}}" method="post"  class="form-row">
                           {!! csrf_field() !!}  
                           @honeypot
                           <div class="form-group col-md-2">
                              <label class="form-label" for="">  {{trans('admin.Manu_Company')}} </label>
                              <select class="select2 form-control w-100" id="Company" name="Company" required>
                                 <option value="">{{trans('admin.Manu_Company')}}</option>
                                 @foreach($Companiesss as $com)
                                 <option value="{{$com->id}}" @if($Maint->Company == $com->id) selected @endif>
     
                               {{app()->getLocale() == 'ar' ?$com->Arabic_Name :$com->English_Name}}          
                                 </option>
                                 @endforeach      
                              </select>
                           </div>
                           <div class="form-group col-md-2">
                              <label class="form-label" for="">  {{trans('admin.Device_Type')}}</label>
                              <select class="select2 form-control w-100" id="Device_Type" name="Device_Type" required>
                                 @if(!empty($Maint->Device_Type))
                                 <option value="{{$Maint->Device_Type}}">
                                  
                                         {{app()->getLocale() == 'ar' ?$Maint->Device_Type()->first()->Arabic_Name :$Maint->Device_Type()->first()->English_Name}}    
                                  </option>
                                 @endif      
                              </select>
                           </div>
                           <div class="form-group col-lg-2">
                              <label class="form-label" for=""> {{trans('admin.Device_Case')}} </label>
                              <select class="select2 form-control w-100" name="Device_Case">
                                 <option value=""> {{trans('admin.Device_Case')}}</option>
                                 @foreach($Cases as $case)    
                                 <option value="{{$case->id}}" @if($Maint->Device_Case == $case->id) selected @endif>
                               {{app()->getLocale() == 'ar' ?$case->Arabic_Name :$case->English_Name}}                  
                                 </option>
                                 @endforeach  
                              </select>
                           </div>
                           <div class="form-group col-lg-2">
                              <label class="form-label" for="">{{trans('admin.Store')}}</label>
                              <select class="select2 form-control w-100" id="store" name="Store" required>
                                 <option value=""> {{trans('admin.Store')}}</option>
                                 @foreach($Storess as $stor)    
                                 <option value="{{$stor->id}}" @if($Maint->Store == $stor->id) selected @endif>
                                 {{app()->getLocale() == 'ar' ?$stor->Name :$stor->NameEn}}     
                                 </option>
                                 @endforeach    
                              </select>
                           </div>
                           <div class="form-group col-lg-2">
                              <label class="form-label" for="">{{trans('admin.Eng')}}</label>
                              <select class="select2 form-control w-100" name="Eng" required>
                                 <option value=""> {{trans('admin.Eng')}}</option>
                                 @foreach($Engs as $eng)    
                                 <option value="{{$eng->id}}" @if($Maint->Eng == $eng->id) selected @endif>
                                        {{app()->getLocale() == 'ar' ?$eng->Name :$eng->NameEn}}         
                                 </option>
                                 @endforeach  
                              </select>
                           </div>
                           <div class="form-group col-lg-2">
                              <label class="form-label" for="">{{trans('admin.Recipient')}}</label>
                              <select class="select2 form-control w-100" name="Recipient" required>
                                 <option value=""> {{trans('admin.Recipient')}}</option>
                                 @foreach($Employess as $emp)    
                                 <option value="{{$emp->id}}" @if($Maint->Recipient == $emp->id) selected @endif>
                              {{app()->getLocale() == 'ar' ?$emp->Name :$emp->NameEn}}           
                                 </option>
                                 @endforeach  
                              </select>
                           </div>
                           <div class="form-group col-lg-2">
                              <label class="form-label" for="">{{trans('admin.Coin')}}</label>
                              <select class="select2 form-control w-100" name="Coin" required>
                                 <option value=""> {{trans('admin.Coin')}}</option>
                                 @foreach($Coins as $coin)    
                                 <option value="{{$coin->id}}" @if($Maint->Coin == $coin->id) selected @endif>
                             {{app()->getLocale() == 'ar' ?$coin->Arabic_Name :$coin->English_Name}} 
                                 </option>
                                 @endforeach  
                              </select>
                           </div>
                           <div class="form-group col-lg-2">
                              <label class="form-label" for="simpleinput">{{trans('admin.Draw')}}</label>
                              <input type="number" step="any" name="Draw" value="{{$Maint->Draw}}" class="form-control" required>
                           </div>
                           <div class="form-group col-lg-2">
                              <label class="form-label" for=""> {{trans('admin.Cost_Center')}} </label>
                              <select class="select2 form-control w-100" name="Cost_Center" required>
                                 <option value=""> {{trans('admin.Cost_Center')}}</option>
                                 @foreach($CostCenters as $cost)    
                                 <option value="{{$cost->id}}" @if($Maint->Cost_Center == $cost->id) selected @endif>
                                  {{app()->getLocale() == 'ar' ?$cost->Arabic_Name :$cost->English_Name}} 
                                 </option>
                                 @endforeach  
                              </select>
                           </div>
                           <div class="form-group col-lg-2">
                              <label class="form-label" for="">{{trans('admin.Account')}}</label>
                              <select  class="select2 form-control w-100" id="client" name="Client" required>
                                 @if(!empty($Maint->Client))
                                 <option value="{{$Maint->Client}}">
                                  
                                        {{app()->getLocale() == 'ar' ?$Maint->Client()->first()->Name :$Maint->Client()->first()->NameEn}} 
                                  </option>
                                 @endif                       
                              </select>
                           </div>
                           <div class="form-group col-lg-2">
                              <label class="form-label" for=""> {{trans('admin.Sure_Bill')}} </label>
                              <select class="select2 form-control w-100" name="Sure">
                                 <option value=""> {{trans('admin.Sure_Bill')}}</option>
                                 <option value="1" @if($Maint->Sure == 1) selected @endif> {{trans('admin.Auto')}}</option> 
                                 <option value="0" @if($Maint->Sure == 0) selected @endif> {{trans('admin.By_Admin')}}</option> 
                              </select>
                           </div>
                           <div class="col-md-12">
                              <button type="submit" class="btn btn-primary">{{trans('admin.Save')}}</button>
                           </div>
                        </form>
                        @else
                        <div  class="form-row">
                           <div class="col-md-12">
                              <div class="data-def">
                                 <div class="form-row">
                                    <div class="form-group col-md-12">
                                       <a class="btn btn-primary" href="{{url('AddDefaultMaintainceFirst')}}">
                                       {{trans('admin.AddNew')}}
                                       </a>   
                                    </div>
                                 </div>
                              </div>
                           </div>
                        </div>
                        @endif        
                     </div>
                     <div class="tab-pane fade " id="tab_borders_icons-11" role="tabpanel">
                        @if(!empty($Manu))  
                        <form action="{{url('AddDefaultManufacture')}}" method="post"  class="form-row">
                           {!! csrf_field() !!}  
                           @honeypot
                            

                            
                    <div class="form-group col-md-2">
                                            <label class="form-label" for="">   {{trans('admin.ManufacturingHalls')}} </label>
                                <select class="select2 form-control w-100" name="Hall" required>                
                                <option value=""> {{trans('admin.ManufacturingHalls')}}</option>
                                            @foreach($ManufacturingHalls as $hall)    
                                    <option value="{{$hall->id}}" @if($Manu->Hall == $hall->id) selected @endif>
                           
                               {{app()->getLocale() == 'ar' ?$hall->Arabic_Name :$hall->English_Name}}             
                                                </option>
                                            @endforeach  
                                            </select>
                                        </div> 
                
                           <div class="form-group col-lg-2">
                              <label class="form-label" for="">{{trans('admin.Coin')}}</label>
                              <select class="select2 form-control w-100" name="Coin" required>
                                 <option value=""> {{trans('admin.Coin')}}</option>
                                 @foreach($Coins as $coin)    
                                 <option value="{{$coin->id}}" @if($Manu->Coin == $coin->id) selected @endif>
                                 {{app()->getLocale() == 'ar' ?$coin->Arabic_Name :$coin->English_Name}} 
                                 </option>
                                 @endforeach  
                              </select>
                           </div>
                            
                                    <div class="form-group col-lg-2">
                              <label class="form-label" for="">{{trans('admin.Executing_Qty')}}</label>
                              <select class="select2 form-control w-100" name="Executing_Qty" required>
                                 <option value=""> {{trans('admin.Executing_Qty')}}</option>
            
                                 <option value="1" @if($Manu->Executing_Qty == 1) selected @endif>
                                 {{trans('admin.Open')}}
                                 </option>
                                  
                                 <option value="0" @if($Manu->Executing_Qty == 0) selected @endif>
                                 {{trans('admin.Close')}}
                                 </option>
                     
                              </select>
                           </div>
                            
                            
                           <div class="form-group col-lg-2">
                              <label class="form-label" for="simpleinput">{{trans('admin.Draw')}}</label>
                              <input type="text" name="Draw" value="{{$Manu->Draw}}" class="form-control" required>
                           </div>
                            @if(auth()->guard('admin')->user()->email == '<EMAIL>')
                           <div class="form-group col-lg-2">
                              <label class="form-label" for=""> {{trans('admin.Manufacturing_Type')}} </label>
                              <select class="select2 form-control w-100" name="Manu_Type">
                                 <option value=""> {{trans('admin.Manufacturing_Type')}}</option>
                    <option value="1" @if($Manu->Manu_Type == 1) selected @endif> {{trans('admin.Big')}}</option> 
                      <option value="0" @if($Manu->Manu_Type == 0) selected @endif> {{trans('admin.Small')}}</option> 
                              </select>
                           </div>
                            @endif
                           <div class="col-md-12">
                              <button type="submit" class="btn btn-primary">{{trans('admin.Save')}}</button>
                           </div>
                        </form>
                        @else
                        <div  class="form-row">
                           <div class="col-md-12">
                              <div class="data-def">
                                 <div class="form-row">
                                    <div class="form-group col-md-12">
                                       <a class="btn btn-primary" href="{{url('AddDefaultManufactureFirst')}}">
                                       {{trans('admin.AddNew')}}
                                       </a>   
                                    </div>
                                 </div>
                              </div>
                           </div>
                        </div>
                        @endif        
                     </div>
                     <div class="tab-pane fade " id="tab_borders_icons-9" role="tabpanel">
                        @if(!empty($ShowHide))  
                        <form action="{{url('AddDefaultShowHide')}}" method="post" enctype="multipart/form-data" >
                           {!! csrf_field() !!}
                           @honeypot
                           <div class="form-row table-color2" style="border:1px solid #ccc;border-radius: 8px;padding:10px 5px;color:white;">
                           <div class="form-group col-lg-3">
                              <label class="form-label" for="">{{trans('admin.Status')}}</label>
                              <select class="select2 form-control w-100" name="Status"  required>
                              <option value="1" @if($ShowHide->Status == 1) selected @endif>{{trans('admin.Show')}} </option>
                              <option value="0" @if( $ShowHide->Status == 0) selected @endif>{{trans('admin.Hide')}}</option>
                              </select>
                           </div>
                           <div class="form-group col-lg-3">
                              <label class="form-label" for="">{{trans('admin.Shipping_Company')}}</label>
                              <select class="select2 form-control w-100" name="Shipping_Company"  required>
                              <option value="1" @if($ShowHide->Shipping_Company == 1) selected @endif>{{trans('admin.Show')}} </option>
                              <option value="0" @if( $ShowHide->Shipping_Company == 0) selected @endif>{{trans('admin.Hide')}}</option>
                              </select>
                           </div>
                           <div class="form-group col-lg-3">
                              <label class="form-label" for="">{{trans('admin.Vendor_Date')}}</label>
                              <select class="select2 form-control w-100" name="Vendor_Date"  required>
                              <option value="1" @if($ShowHide->Vendor_Date == 1) selected @endif>{{trans('admin.Show')}} </option>
                              <option value="0" @if( $ShowHide->Vendor_Date == 0) selected @endif>{{trans('admin.Hide')}}</option>
                              </select>
                           </div>
                           <div class="form-group col-lg-3">
                              <label class="form-label" for="">{{trans('admin.Expire_Date')}}</label>
                              <select class="select2 form-control w-100" name="Expire_Date"  required>
                              <option value="1" @if($ShowHide->Expire_Date == 1) selected @endif>{{trans('admin.Show')}} </option>
                              <option value="0" @if( $ShowHide->Expire_Date == 0) selected @endif>{{trans('admin.Hide')}}</option>
                              </select>
                           </div>
                           <div class="form-group col-lg-3" style="display: none">
                              <label class="form-label" for="">{{trans('admin.Total_BF_Taxes')}}</label>
                              <select class="select2 form-control w-100" name="Total_BF_Taxes"   required>
                              <option value="1" @if($ShowHide->Total_BF_Taxes == 1) selected @endif>{{trans('admin.Show')}} </option>
                              <option value="0" @if( $ShowHide->Total_BF_Taxes == 0) selected @endif>{{trans('admin.Hide')}}</option>
                              </select>
                           </div>
                           <div class="form-group col-lg-3" style="display: none">
                              <label class="form-label" for="">{{trans('admin.Total_Taxes')}}</label>
                              <select class="select2 form-control w-100" name="Total_Taxes"   required>
                              <option value="1" @if($ShowHide->Total_Taxes == 1) selected @endif>{{trans('admin.Show')}} </option>
                              <option value="0" @if( $ShowHide->Total_Taxes == 0) selected @endif>{{trans('admin.Hide')}}</option>
                              </select>
                           </div>
                           <div class="form-group col-lg-3">
                              <label class="form-label" for="">{{trans('admin.Coin')}}</label>
                              <select class="select2 form-control w-100" name="Coin"  required>
                              <option value="1" @if($ShowHide->Coin == 1) selected @endif>{{trans('admin.Show')}} </option>
                              <option value="0" @if( $ShowHide->Coin == 0) selected @endif>{{trans('admin.Hide')}}</option>
                              </select>
                           </div>
                           <div class="form-group col-lg-3">
                              <label class="form-label" for="">{{trans('admin.Cost_Center')}}</label>
                              <select class="select2 form-control w-100" name="Cost_Center"  required>
                              <option value="1" @if($ShowHide->Cost_Center == 1) selected @endif>{{trans('admin.Show')}} </option>
                              <option value="0" @if( $ShowHide->Cost_Center == 0) selected @endif>{{trans('admin.Hide')}}</option>
                              </select>
                           </div>
                           <div class="form-group col-lg-3">
                              <label class="form-label" for="">{{trans('admin.Draw')}}</label>
                              <select class="select2 form-control w-100" name="Draw"   required>
                              <option value="1" @if($ShowHide->Draw == 1) selected @endif>{{trans('admin.Show')}} </option>
                              <option value="0" @if( $ShowHide->Draw == 0) selected @endif>{{trans('admin.Hide')}}</option>
                              </select>
                           </div>
                           <div class="form-group col-lg-3">
                              <label class="form-label" for="">{{trans('admin.Delegate_Sale')}}</label>
                              <select class="select2 form-control w-100" name="Delegate_Sale" required>
                              <option value="1" @if($ShowHide->Delegate_Sale == 1) selected @endif>{{trans('admin.Show')}} </option>
                              <option value="0" @if( $ShowHide->Delegate_Sale == 0) selected @endif>{{trans('admin.Hide')}}</option>
                              </select>
                           </div>
                           <div class="form-group col-lg-3">
                              <label class="form-label" for="">{{trans('admin.Delegate_Purchase')}}</label>
                              <select class="select2 form-control w-100" name="Delegate_Purchase" required>
                              <option value="1" @if($ShowHide->Delegate_Purchase == 1) selected @endif>{{trans('admin.Show')}} </option>
                              <option value="0" @if( $ShowHide->Delegate_Purchase == 0) selected @endif>{{trans('admin.Hide')}}</option>
                              </select>
                           </div>
                           <div class="form-group col-lg-3">
                              <label class="form-label" for="">{{trans('admin.Note')}}</label>
                              <select class="select2 form-control w-100" name="Note"    required>
                              <option value="1" @if($ShowHide->Note == 1) selected @endif>{{trans('admin.Show')}} </option>
                              <option value="0" @if( $ShowHide->Note == 0) selected @endif>{{trans('admin.Hide')}}</option>
                              </select>
                           </div>
                           <div class="form-group col-lg-3">
                              <label class="form-label" for="">{{trans('admin.Refrence_Number')}}</label>
                              <select class="select2 form-control w-100" name="Refrence_Number"    required>
                              <option value="1" @if($ShowHide->Refrence_Number == 1) selected @endif>{{trans('admin.Show')}} </option>
                              <option value="0" @if( $ShowHide->Refrence_Number == 0) selected @endif>{{trans('admin.Hide')}}</option>
                              </select>
                           </div>
                           <div class="form-group col-lg-3">
                              <label class="form-label" for="">{{trans('admin.Branch')}}</label>
                              <select class="select2 form-control w-100" name="Branch"    required>
                              <option value="1" @if($ShowHide->Branch == 1) selected @endif>{{trans('admin.Show')}} </option>
                              <option value="0" @if( $ShowHide->Branch == 0) selected @endif>{{trans('admin.Hide')}}</option>
                              </select>
                           </div>
                           <div class="form-group col-lg-3">
                              <label class="form-label" for="">{{trans('admin.Serial_Num')}}</label>
                              <select class="select2 form-control w-100" name="Serial_Num"    required>
                              <option value="1" @if($ShowHide->Serial_Num == 1) selected @endif>{{trans('admin.Show')}} </option>
                              <option value="0" @if( $ShowHide->Serial_Num == 0) selected @endif>{{trans('admin.Hide')}}</option>
                              </select>
                           </div>
                           <div class="form-group col-lg-3">
                              <label class="form-label" for="">{{trans('admin.Pass')}}</label>
                              <select class="select2 form-control w-100" name="Pass"    required>
                              <option value="1" @if($ShowHide->Pass == 1) selected @endif>{{trans('admin.Show')}} </option>
                              <option value="0" @if( $ShowHide->Pass == 0) selected @endif>{{trans('admin.Hide')}}</option>
                              </select>
                           </div>
                           <div class="form-group col-lg-3">
                              <label class="form-label" for="">{{trans('admin.Pattern_Image')}}</label>
                              <select class="select2 form-control w-100" name="Pattern_Image"    required>
                              <option value="1" @if($ShowHide->Pattern_Image == 1) selected @endif>{{trans('admin.Show')}} </option>
                              <option value="0" @if( $ShowHide->Pattern_Image == 0) selected @endif>{{trans('admin.Hide')}}</option>
                              </select>
                           </div>
                           <div class="form-group col-lg-3">
                              <label class="form-label" for="">{{trans('admin.Search_Typical')}}</label>
                              <select class="select2 form-control w-100" name="Search_Typical"    required>
                              <option value="1" @if($ShowHide->Search_Typical == 1) selected @endif>{{trans('admin.Yes')}} </option>
                              <option value="0" @if( $ShowHide->Search_Typical == 0) selected @endif>{{trans('admin.No')}}</option>
                              </select>
                           </div>
                           <div class="form-group col-lg-3">
                              <label class="form-label" for="">{{trans('admin.Validity_Product')}}</label>
                              <select class="select2 form-control w-100" name="Validity_Product"    required>
                              <option value="1" @if($ShowHide->Validity_Product == 1) selected @endif>{{trans('admin.Yes')}} </option>
                              <option value="0" @if( $ShowHide->Validity_Product == 0) selected @endif>{{trans('admin.No')}}</option>
                              </select>
                           </div>
                           <div class="form-group col-lg-3">
                              <label class="form-label" for="">{{trans('admin.A4_Sales_Print')}}</label>
                              <select class="select2 form-control w-100" name="A4"    required>
                              <option value="1" @if($ShowHide->A4 == 1) selected @endif>{{trans('admin.Show')}} </option>
                              <option value="0" @if( $ShowHide->A4 == 0) selected @endif>{{trans('admin.Hide')}}</option>
                              </select>
                           </div>
                           <div class="form-group col-lg-3">
                              <label class="form-label" for="">{{trans('admin.A5_Sales_Print')}}</label>
                              <select class="select2 form-control w-100" name="A5"    required>
                              <option value="1" @if($ShowHide->A5 == 1) selected @endif>{{trans('admin.Show')}} </option>
                              <option value="0" @if( $ShowHide->A5 == 0) selected @endif>{{trans('admin.Hide')}}</option>
                              </select>
                           </div>
                           <div class="form-group col-lg-3">
                              <label class="form-label" for="">{{trans('admin.8CM_Sales_Print')}}</label>
                              <select class="select2 form-control w-100" name="CM8"    required>
                              <option value="1" @if($ShowHide->CM8 == 1) selected @endif>{{trans('admin.Show')}} </option>
                              <option value="0" @if( $ShowHide->CM8 == 0) selected @endif>{{trans('admin.Hide')}}</option>
                              </select>
                           </div>
                           <div class="form-group col-lg-3">
                              <label class="form-label" for="">{{trans('admin.Manufacturing_Model_Shortcomings')}}</label>
                              <select class="select2 form-control w-100" name="Manufacturing_Model_Shortcomings"    required>
                              <option value="1" @if($ShowHide->Manufacturing_Model_Shortcomings == 1) selected @endif>{{trans('admin.Show')}} </option>
                              <option value="0" @if( $ShowHide->Manufacturing_Model_Shortcomings == 0) selected @endif>{{trans('admin.Hide')}}</option>
                              </select>
                           </div>
                           <div class="form-group col-lg-3">
                              <label class="form-label" for="">{{trans('admin.Group_Brand')}}</label>
                              <select class="select2 form-control w-100" name="Group_Brand"    required>
                              <option value="1" @if($ShowHide->Group_Brand == 1) selected @endif>{{trans('admin.Show')}} </option>
                              <option value="0" @if( $ShowHide->Group_Brand == 0) selected @endif>{{trans('admin.Hide')}}</option>
                              </select>
                           </div>
                           <div class="form-group col-lg-3">
                              <label class="form-label" for="">{{trans('admin.Patch_Number')}}</label>
                              <select class="select2 form-control w-100" name="Patch_Number"    required>
                              <option value="1" @if($ShowHide->Patch_Number == 1) selected @endif>{{trans('admin.Show')}} </option>
                              <option value="0" @if($ShowHide->Patch_Number == 0) selected @endif>{{trans('admin.Hide')}}</option>
                              </select>
                           </div>
                                    <div class="form-group col-lg-3">
                              <label class="form-label" for="">{{trans('admin.Executor_Sale')}}</label>
                              <select class="select2 form-control w-100" name="Executor_Sale"    required>
                              <option value="1" @if($ShowHide->Executor_Sale == 1) selected @endif>{{trans('admin.Show')}} </option>
                              <option value="0" @if($ShowHide->Executor_Sale == 0) selected @endif>{{trans('admin.Hide')}}</option>
                              </select>
                           </div>      
                               
                                    <div class="form-group col-lg-3">
                              <label class="form-label" for="">{{trans('admin.Totuch_Screen')}}</label>
                              <select class="select2 form-control w-100" name="Totuch_Screen"  required>
                              <option value="1" @if($ShowHide->Totuch_Screen == 1) selected @endif>{{trans('admin.Show')}} </option>
                              <option value="0" @if( $ShowHide->Totuch_Screen == 0) selected @endif>{{trans('admin.Hide')}}</option>
                              </select>
                           </div>       
                               
                                           <div class="form-group col-lg-3">
                              <label class="form-label" for="">{{trans('admin.Tax_POS')}}</label>
                              <select class="select2 form-control w-100" name="Tax_POS"  required>
                              <option value="1" @if($ShowHide->Tax_POS == 1) selected @endif>{{trans('admin.Show')}} </option>
                              <option value="0" @if( $ShowHide->Tax_POS == 0) selected @endif>{{trans('admin.Hide')}}</option>
                              </select>
                           </div> 
                               
                                                   <div class="form-group col-lg-3">
                              <label class="form-label" for="">{{trans('admin.Icon_Payment_Recipt')}}</label>
                              <select class="select2 form-control w-100" name="Icon_Payment_Recipt"  required>
                              <option value="1" @if($ShowHide->Icon_Payment_Recipt == 1) selected @endif>{{trans('admin.Show')}} </option>
                              <option value="0" @if( $ShowHide->Icon_Payment_Recipt == 0) selected @endif>{{trans('admin.Hide')}}</option>
                              </select>
                           </div> 
                               
                               
                                                   <div class="form-group col-lg-3">
                              <label class="form-label" for="">{{trans('admin.SearchCode')}}</label>
                              <select class="select2 form-control w-100" name="SearchCode"  required>
                              <option value="1" @if($ShowHide->SearchCode == 1) selected @endif>{{trans('admin.Show')}} </option>
                              <option value="0" @if( $ShowHide->SearchCode == 0) selected @endif>{{trans('admin.Hide')}}</option>
                              </select>
                           </div> 
                               
                                                         <div class="form-group col-lg-3">
                              <label class="form-label" for="">{{trans('admin.TaxOnTotal')}}</label>
                              <select class="select2 form-control w-100" name="TaxOnTotal"  required>
                              <option value="1" @if($ShowHide->TaxOnTotal == 1) selected @endif>{{trans('admin.Show')}} </option>
                              <option value="0" @if( $ShowHide->TaxOnTotal == 0) selected @endif>{{trans('admin.Hide')}}</option>
                              </select>
                           </div> 
                               
                                                                        <div class="form-group col-lg-3">
                              <label class="form-label" for="">{{trans('admin.Total_BF_Tax')}}</label>
                              <select class="select2 form-control w-100" name="TotalBfTax"  required>
                              <option value="1" @if($ShowHide->TotalBfTax == 1) selected @endif>{{trans('admin.Show')}} </option>
                              <option value="0" @if( $ShowHide->TotalBfTax == 0) selected @endif>{{trans('admin.Hide')}}</option>
                              </select>
                           </div> 
                                                                        <div class="form-group col-lg-3">
                              <label class="form-label" for="">{{trans('admin.AvQty')}}</label>
                              <select class="select2 form-control w-100" name="AvQty"  required>
                              <option value="1" @if($ShowHide->AvQty == 1) selected @endif>{{trans('admin.Show')}} </option>
                              <option value="0" @if( $ShowHide->AvQty == 0) selected @endif>{{trans('admin.Hide')}}</option>
                              </select>
                           </div> 
                               
                                                                        <div class="form-group col-lg-3">
                              <label class="form-label" for="">{{trans('admin.Discount')}}</label>
                              <select class="select2 form-control w-100" name="Disc"  required>
                              <option value="1" @if($ShowHide->Disc == 1) selected @endif>{{trans('admin.Show')}} </option>
                              <option value="0" @if( $ShowHide->Disc == 0) selected @endif>{{trans('admin.Hide')}}</option>
                              </select>
                           </div> 
                               
                                                                        <div class="form-group col-lg-3">
                              <label class="form-label" for="">{{trans('admin.Tax')}}</label>
                              <select class="select2 form-control w-100" name="Tax"  required>
                              <option value="1" @if($ShowHide->Tax == 1) selected @endif>{{trans('admin.Show')}} </option>
                              <option value="0" @if( $ShowHide->Tax == 0) selected @endif>{{trans('admin.Hide')}}</option>
                              </select>
                           </div> 
                               
                                                                        <div class="form-group col-lg-3">
                              <label class="form-label" for="">{{trans('admin.Store')}}</label>
                              <select class="select2 form-control w-100" name="Store"  required>
                              <option value="1" @if($ShowHide->Store == 1) selected @endif>{{trans('admin.Show')}} </option>
                              <option value="0" @if( $ShowHide->Store == 0) selected @endif>{{trans('admin.Hide')}}</option>
                              </select>
                           </div> 
                               
                               
                                                                                     <div class="form-group col-lg-3">
                              <label class="form-label" for="">{{trans('admin.TaxBill')}}</label>
                              <select class="select2 form-control w-100" name="TaxBill"  required>
                              <option value="1" @if($ShowHide->TaxBill == 1) selected @endif>{{trans('admin.Show')}} </option>
                              <option value="0" @if( $ShowHide->TaxBill == 0) selected @endif>{{trans('admin.Hide')}}</option>
                              </select>
                           </div> 
     
                               
                                                                                                 <div class="form-group col-lg-3">
                              <label class="form-label" for="">{{trans('admin.Change_Way_Stores_Transfer')}}</label>
                              <select class="select2 form-control w-100" name="Change_Way_Stores_Transfer"  required>
                              <option value="1" @if($ShowHide->Change_Way_Stores_Transfer == 1) selected @endif>{{trans('admin.Show')}} </option>
                              <option value="0" @if( $ShowHide->Change_Way_Stores_Transfer == 0) selected @endif>{{trans('admin.Hide')}}</option>
                              </select>
                           </div> 
                                                                                                           <div class="form-group col-lg-3">
                              <label class="form-label" for="">{{trans('admin.Note_POS')}}</label>
                              <select class="select2 form-control w-100" name="Note_POS"  required>
                              <option value="1" @if($ShowHide->Note_POS == 1) selected @endif>{{trans('admin.Show')}} </option>
                              <option value="0" @if( $ShowHide->Note_POS == 0) selected @endif>{{trans('admin.Hide')}}</option>
                              </select>
                           </div> 
                                                                                                           <div class="form-group col-lg-3">
                              <label class="form-label" for="">{{trans('admin.Open_Drawer')}}</label>
                              <select class="select2 form-control w-100" name="Open_Drawer"  required>
                              <option value="1" @if($ShowHide->Open_Drawer == 1) selected @endif>{{trans('admin.Show')}} </option>
                              <option value="0" @if( $ShowHide->Open_Drawer == 0) selected @endif>{{trans('admin.Hide')}}</option>
                              </select>
                           </div> 

                                                                                                                      <div class="form-group col-lg-3">
                              <label class="form-label" for="">{{trans('admin.client_delivery')}}</label>
                              <select class="select2 form-control w-100" name="client_delivery"  required>
                              <option value="1" @if($ShowHide->client_delivery == 1) selected @endif>{{trans('admin.Show')}} </option>
                              <option value="0" @if( $ShowHide->client_delivery == 0) selected @endif>{{trans('admin.Hide')}}</option>
                              </select>
                           </div> 
                              
                                                             <div class="form-group col-lg-3">
                              <label class="form-label" for="">{{trans('admin.POS_RecivedDate')}}</label>
                              <select class="select2 form-control w-100" name="POS_RecivedDate"    required>
                              <option value="1" @if($ShowHide->POS_RecivedDate == 1) selected @endif>{{trans('admin.Show')}} </option>
                              <option value="0" @if( $ShowHide->POS_RecivedDate == 0) selected @endif>{{trans('admin.Hide')}}</option>
                              </select>
                           </div>                         
                               <div class="form-group col-lg-3">
                              <label class="form-label" for="">{{trans('admin.POS_Qty')}}</label>
                              <select class="select2 form-control w-100" name="POS_Qty"    required>
                              <option value="1" @if($ShowHide->POS_Qty == 1) selected @endif>{{trans('admin.Show')}} </option>
                              <option value="0" @if( $ShowHide->POS_Qty == 0) selected @endif>{{trans('admin.Hide')}}</option>
                              </select>
                           </div>                    
                               <div class="form-group col-lg-3">
                              <label class="form-label" for="">{{trans('admin.POS_Barcode')}}</label>
                              <select class="select2 form-control w-100" name="POS_Barcode"    required>
                              <option value="1" @if($ShowHide->POS_Barcode == 1) selected @endif>{{trans('admin.Show')}} </option>
                              <option value="0" @if( $ShowHide->POS_Barcode == 0) selected @endif>{{trans('admin.Hide')}}</option>
                              </select>
                           </div>
                         
                                          <div class="form-group col-lg-3">
                              <label class="form-label" for="">{{trans('admin.Show_File_ReciptVoucher')}}</label>
                              <select class="select2 form-control w-100" name="Show_File_ReciptVoucher"    required>
                              <option value="1" @if($ShowHide->Show_File_ReciptVoucher == 1) selected @endif>{{trans('admin.Show')}} </option>
                              <option value="0" @if( $ShowHide->Show_File_ReciptVoucher == 0) selected @endif>{{trans('admin.Hide')}}</option>
                              </select>
                           </div>
                                   <div class="form-group col-lg-3">
                              <label class="form-label" for="">{{trans('admin.Show_File_PaymentVoucher')}}</label>
                              <select class="select2 form-control w-100" name="Show_File_PaymentVoucher"    required>
                              <option value="1" @if($ShowHide->Show_File_PaymentVoucher == 1) selected @endif>{{trans('admin.Show')}} </option>
                              <option value="0" @if( $ShowHide->Show_File_PaymentVoucher == 0) selected @endif>{{trans('admin.Hide')}}</option>
                              </select>
                           </div>
                               
                                   <div class="form-group col-lg-3">
                              <label class="form-label" for="">{{trans('admin.Show_File_Sales')}}</label>
                              <select class="select2 form-control w-100" name="Show_File_Sales"    required>
                              <option value="1" @if($ShowHide->Show_File_Sales == 1) selected @endif>{{trans('admin.Show')}} </option>
                              <option value="0" @if( $ShowHide->Show_File_Sales == 0) selected @endif>{{trans('admin.Hide')}}</option>
                              </select>
                           </div>   
                        
                                             <div class="form-group col-lg-3">
                              <label class="form-label" for="">{{trans('admin.Show_File_Purchases')}}</label>
                              <select class="select2 form-control w-100" name="Show_File_Purchases"    required>
                              <option value="1" @if($ShowHide->Show_File_Purchases == 1) selected @endif>{{trans('admin.Show')}} </option>
                              <option value="0" @if( $ShowHide->Show_File_Purchases == 0) selected @endif>{{trans('admin.Hide')}}</option>
                              </select>
                           </div>
                                   <div class="form-group col-lg-3">
                              <label class="form-label" for="">{{trans('admin.Show_File_Checks')}}</label>
                              <select class="select2 form-control w-100" name="Show_File_Checks"    required>
                              <option value="1" @if($ShowHide->Show_File_Checks == 1) selected @endif>{{trans('admin.Show')}} </option>
                              <option value="0" @if( $ShowHide->Show_File_Checks == 0) selected @endif>{{trans('admin.Hide')}}</option>
                              </select>
                           </div>
                               
                                   <div class="form-group col-lg-3">
                              <label class="form-label" for="">{{trans('admin.Show_File_InsurancePaper')}}</label>
                              <select class="select2 form-control w-100" name="Show_File_InsurancePaper"    required>
                              <option value="1" @if($ShowHide->Show_File_InsurancePaper == 1) selected @endif>{{trans('admin.Show')}} </option>
                              <option value="0" @if( $ShowHide->Show_File_InsurancePaper == 0) selected @endif>{{trans('admin.Hide')}}</option>
                              </select>
                           </div>   
                               
                                     <div class="form-group col-lg-3">
                              <label class="form-label" for="">{{trans('admin.Show_File_TransferStores')}}</label>
                              <select class="select2 form-control w-100" name="Show_File_TransferStores"    required>
                              <option value="1" @if($ShowHide->Show_File_TransferStores == 1) selected @endif>{{trans('admin.Show')}} </option>
                              <option value="0" @if( $ShowHide->Show_File_TransferStores == 0) selected @endif>{{trans('admin.Hide')}}</option>
                              </select>
                           </div>                 
                               <div class="form-group col-lg-3">
                              <label class="form-label" for="">{{trans('admin.Thickness')}}</label>
                              <select class="select2 form-control w-100" name="Thickness"    required>
                              <option value="1" @if($ShowHide->Thickness == 1) selected @endif>{{trans('admin.Show')}} </option>
                              <option value="0" @if( $ShowHide->Thickness == 0) selected @endif>{{trans('admin.Hide')}}</option>
                              </select>
                           </div>                 
                               
                               <div class="form-group col-lg-3">
                              <label class="form-label" for="">{{trans('admin.Height')}}</label>
                              <select class="select2 form-control w-100" name="Height"    required>
                              <option value="1" @if($ShowHide->Height == 1) selected @endif>{{trans('admin.Show')}} </option>
                              <option value="0" @if( $ShowHide->Height == 0) selected @endif>{{trans('admin.Hide')}}</option>
                              </select>
                           </div>                
                               <div class="form-group col-lg-3">
                              <label class="form-label" for="">{{trans('admin.Items_Guide_Store_Show')}}</label>
                              <select class="select2 form-control w-100" name="Items_Guide_Store_Show"    required>
                              <option value="1" @if($ShowHide->Items_Guide_Store_Show == 1) selected @endif>{{trans('admin.Show')}} </option>
                              <option value="0" @if( $ShowHide->Items_Guide_Store_Show == 0) selected @endif>{{trans('admin.Hide')}}</option>
                              </select>
                           </div>       
                               <div class="form-group col-lg-3">
                              <label class="form-label" for="">{{trans('admin.Sales_Pro_Desc')}}</label>
                              <select class="select2 form-control w-100" name="Sales_Pro_Desc"    required>
                              <option value="1" @if($ShowHide->Sales_Pro_Desc == 1) selected @endif>{{trans('admin.Show')}} </option>
                              <option value="0" @if( $ShowHide->Sales_Pro_Desc == 0) selected @endif>{{trans('admin.Hide')}}</option>
                              </select>
                           </div>         

                               
                           </div>
                           <br>
                           <h3>  {{trans('admin.Print')}}  </h3>
                          
                           <div class="form-row table-color1" style="border:1px solid #ccc;border-radius: 8px;padding:10px 5px;">
                                   
                           <!--</div>-->
                           <div class="form-group col-lg-3">
                              <label class="form-label" for="">{{trans('admin.Barcode_Print')}}</label>
                              <select class="select2 form-control w-100" name="Barcode_Print"    required>
                              <option value="1" @if($ShowHide->Barcode_Print == 1) selected @endif>{{trans('admin.Show')}} </option>
                              <option value="0" @if( $ShowHide->Barcode_Print == 0) selected @endif>{{trans('admin.Hide')}}</option>
                              </select>
                           </div>
                           <div class="form-group col-lg-3">
                              <label class="form-label" for="">{{trans('admin.Unit_Print')}}</label>
                              <select class="select2 form-control w-100" name="Unit_Print"    required>
                              <option value="1" @if($ShowHide->Unit_Print == 1) selected @endif>{{trans('admin.Show')}} </option>
                              <option value="0" @if( $ShowHide->Unit_Print == 0) selected @endif>{{trans('admin.Hide')}}</option>
                              </select>
                           </div>
                           <div class="form-group col-lg-3">
                              <label class="form-label" for="">{{trans('admin.Total_BF_Print')}}</label>
                              <select class="select2 form-control w-100" name="Total_BF_Print"    required>
                              <option value="1" @if($ShowHide->Total_BF_Print == 1) selected @endif>{{trans('admin.Show')}} </option>
                              <option value="0" @if( $ShowHide->Total_BF_Print == 0) selected @endif>{{trans('admin.Hide')}}</option>
                              </select>
                           </div>
                           <div class="form-group col-lg-3">
                              <label class="form-label" for="">{{trans('admin.Discount_Print')}}</label>
                              <select class="select2 form-control w-100" name="Discount_Print"    required>
                              <option value="1" @if($ShowHide->Discount_Print == 1) selected @endif>{{trans('admin.Show')}} </option>
                              <option value="0" @if( $ShowHide->Discount_Print == 0) selected @endif>{{trans('admin.Hide')}}</option>
                              </select>
                           </div>
                           <div class="form-group col-lg-3">
                              <label class="form-label" for="">{{trans('admin.Tax_Print')}}</label>
                              <select class="select2 form-control w-100" name="Tax_Print"    required>
                              <option value="1" @if($ShowHide->Tax_Print == 1) selected @endif>{{trans('admin.Show')}} </option>
                              <option value="0" @if( $ShowHide->Tax_Print == 0) selected @endif>{{trans('admin.Hide')}}</option>
                              </select>
                           </div>
                               
                                      <div class="form-group col-lg-3">
                              <label class="form-label" for="">{{trans('admin.TotalDiscountPrint')}}</label>
                              <select class="select2 form-control w-100" name="TotalDiscountPrint"    required>
                              <option value="1" @if($ShowHide->TotalDiscountPrint == 1) selected @endif>{{trans('admin.Show')}} </option>
                              <option value="0" @if( $ShowHide->TotalDiscountPrint == 0) selected @endif>{{trans('admin.Hide')}}</option>
                              </select>
                           </div>
                               
                                      <div class="form-group col-lg-3">
                              <label class="form-label" for="">{{trans('admin.TotalTaxPrint')}}</label>
                              <select class="select2 form-control w-100" name="TotalTaxPrint"    required>
                              <option value="1" @if($ShowHide->TotalTaxPrint == 1) selected @endif>{{trans('admin.Show')}} </option>
                              <option value="0" @if( $ShowHide->TotalTaxPrint == 0) selected @endif>{{trans('admin.Hide')}}</option>
                              </select>
                           </div>
                               
                                      <div class="form-group col-lg-3">
                              <label class="form-label" for="">{{trans('admin.ProductsNumber')}}</label>
                              <select class="select2 form-control w-100" name="ProductsNumber"    required>
                              <option value="1" @if($ShowHide->ProductsNumber == 1) selected @endif>{{trans('admin.Show')}} </option>
                              <option value="0" @if( $ShowHide->ProductsNumber == 0) selected @endif>{{trans('admin.Hide')}}</option>
                              </select>
                           </div>
                               
                                      <div class="form-group col-lg-3">
                              <label class="form-label" for="">{{trans('admin.TotalQtyPrint')}}</label>
                              <select class="select2 form-control w-100" name="TotalQtyPrint"    required>
                              <option value="1" @if($ShowHide->TotalQtyPrint == 1) selected @endif>{{trans('admin.Show')}} </option>
                              <option value="0" @if( $ShowHide->TotalQtyPrint == 0) selected @endif>{{trans('admin.Hide')}}</option>
                              </select>
                           </div>
                               
                                      <div class="form-group col-lg-3">
                              <label class="form-label" for="">{{trans('admin.Credit')}}</label>
                              <select class="select2 form-control w-100" name="Credit"    required>
                              <option value="1" @if($ShowHide->Credit == 1) selected @endif>{{trans('admin.Show')}} </option>
                              <option value="0" @if( $ShowHide->Credit == 0) selected @endif>{{trans('admin.Hide')}}</option>
                              </select>
                           </div>
                               
                                      <div class="form-group col-lg-3">
                              <label class="form-label" for="">{{trans('admin.Barcode')}}</label>
                              <select class="select2 form-control w-100" name="Barcode"    required>
                              <option value="1" @if($ShowHide->Barcode == 1) selected @endif>{{trans('admin.Show')}} </option>
                              <option value="0" @if( $ShowHide->Barcode == 0) selected @endif>{{trans('admin.Hide')}}</option>
                              </select>
                           </div>
                               
                                      <div class="form-group col-lg-3">
                              <label class="form-label" for="">{{trans('admin.Taknet')}}</label>
                              <select class="select2 form-control w-100" name="Taknet"    required>
                              <option value="1" @if($ShowHide->Taknet == 1) selected @endif>{{trans('admin.Show')}} </option>
                              <option value="0" @if( $ShowHide->Taknet == 0) selected @endif>{{trans('admin.Hide')}}</option>
                              </select>
                           </div>
                               
                                      <div class="form-group col-lg-3">
                              <label class="form-label" for="">{{trans('admin.Address')}}</label>
                              <select class="select2 form-control w-100" name="Address"    required>
                              <option value="1" @if($ShowHide->Address == 1) selected @endif>{{trans('admin.Show')}} </option>
                              <option value="0" @if( $ShowHide->Address == 0) selected @endif>{{trans('admin.Hide')}}</option>
                              </select>
                           </div>
                               
                                      <div class="form-group col-lg-3">
                              <label class="form-label" for="">{{trans('admin.Phone1')}}</label>
                              <select class="select2 form-control w-100" name="Phone1"    required>
                              <option value="1" @if($ShowHide->Phone1 == 1) selected @endif>{{trans('admin.Show')}} </option>
                              <option value="0" @if( $ShowHide->Phone1 == 0) selected @endif>{{trans('admin.Hide')}}</option>
                              </select>
                           </div>
                               
                                      <div class="form-group col-lg-3">
                              <label class="form-label" for="">{{trans('admin.Phone2')}}</label>
                              <select class="select2 form-control w-100" name="Phone2"    required>
                              <option value="1" @if($ShowHide->Phone2 == 1) selected @endif>{{trans('admin.Show')}} </option>
                              <option value="0" @if( $ShowHide->Phone2 == 0) selected @endif>{{trans('admin.Hide')}}</option>
                              </select>
                           </div>
                               
                                      <div class="form-group col-lg-3">
                              <label class="form-label" for="">{{trans('admin.Phone3')}}</label>
                              <select class="select2 form-control w-100" name="Phone3"    required>
                              <option value="1" @if($ShowHide->Phone3 == 1) selected @endif>{{trans('admin.Show')}} </option>
                              <option value="0" @if( $ShowHide->Phone3 == 0) selected @endif>{{trans('admin.Hide')}}</option>
                              </select>
                           </div>
                               
                                      <div class="form-group col-lg-3">
                              <label class="form-label" for="">{{trans('admin.Phone4')}}</label>
                              <select class="select2 form-control w-100" name="Phone4"    required>
                              <option value="1" @if($ShowHide->Phone4 == 1) selected @endif>{{trans('admin.Show')}} </option>
                              <option value="0" @if( $ShowHide->Phone4 == 0) selected @endif>{{trans('admin.Hide')}}</option>
                              </select>
                           </div>
                               
                                      <div class="form-group col-lg-3">
                              <label class="form-label" for="">{{trans('admin.Text')}}</label>
                              <select class="select2 form-control w-100" name="Text"    required>
                              <option value="1" @if($ShowHide->Text == 1) selected @endif>{{trans('admin.Show')}} </option>
                              <option value="0" @if( $ShowHide->Text == 0) selected @endif>{{trans('admin.Hide')}}</option>
                              </select>
                           </div>
                               
                                      <div class="form-group col-lg-3">
                              <label class="form-label" for="">{{trans('admin.Seal')}}</label>
                              <select class="select2 form-control w-100" name="Seal"    required>
                              <option value="1" @if($ShowHide->Seal == 1) selected @endif>{{trans('admin.Show')}} </option>
                              <option value="0" @if( $ShowHide->Seal == 0) selected @endif>{{trans('admin.Hide')}}</option>
                              </select>
                           </div>
     
                                              <div class="form-group col-lg-3">
                              <label class="form-label" for="">{{trans('admin.Code_Report')}}</label>
                              <select class="select2 form-control w-100" name="Code_Report"    required>
                              <option value="1" @if($ShowHide->Code_Report == 1) selected @endif>{{trans('admin.Show')}} </option>
                              <option value="0" @if( $ShowHide->Code_Report == 0) selected @endif>{{trans('admin.Hide')}}</option>
                              </select>
                           </div>
                               
                                                   <div class="form-group col-lg-3">
                              <label class="form-label" for="">{{trans('admin.Unit')}}</label>
                              <select class="select2 form-control w-100" name="Unit"    required>
                              <option value="1" @if($ShowHide->Unit == 1) selected @endif>{{trans('admin.Show')}} </option>
                              <option value="0" @if( $ShowHide->Unit == 0) selected @endif>{{trans('admin.Hide')}}</option>
                              </select>
                           </div>
                               
                               
                                                   <div class="form-group col-lg-3">
                              <label class="form-label" for="">{{trans('admin.Refrence_Number')}}</label>
                              <select class="select2 form-control w-100" name="Refrence_Number_Print"    required>
                              <option value="1" @if($ShowHide->Refrence_Number_Print == 1) selected @endif>{{trans('admin.Show')}} </option>
                              <option value="0" @if( $ShowHide->Refrence_Number_Print == 0) selected @endif>{{trans('admin.Hide')}}</option>
                              </select>
                           </div>                
                                                   <div class="form-group col-lg-3">
                              <label class="form-label" for="">{{trans('admin.Thickness_Print')}}</label>
                              <select class="select2 form-control w-100" name="Thickness_Print"    required>
                              <option value="1" @if($ShowHide->Thickness_Print == 1) selected @endif>{{trans('admin.Show')}} </option>
                              <option value="0" @if( $ShowHide->Thickness_Print == 0) selected @endif>{{trans('admin.Hide')}}</option>
                              </select>
                           </div>                
                                                   <div class="form-group col-lg-3">
                              <label class="form-label" for="">{{trans('admin.Height_Print')}}</label>
                              <select class="select2 form-control w-100" name="Height_Print"    required>
                              <option value="1" @if($ShowHide->Height_Print == 1) selected @endif>{{trans('admin.Show')}} </option>
                              <option value="0" @if( $ShowHide->Height_Print == 0) selected @endif>{{trans('admin.Hide')}}</option>
                              </select>
                           </div>
                               
                            
                               
                           </div>
                           <div class="col-md-12 mt-3">
                              <button type="submit" class="btn btn-primary">{{trans('admin.Save')}}</button>
                           </div>
                        </form>
                        @else
                        <div  class="form-row">
                           <div class="col-md-12">
                              <div class="data-def">
                                 <div class="form-row">
                                    <div class="form-group col-md-12 ">
                                       <a class="btn btn-primary" href="{{url('AddDefaultShowHideFirst')}}">
                                       {{trans('admin.AddNew')}}
                                       </a>   
                                    </div>
                                 </div>
                              </div>
                           </div>
                        </div>
                        @endif                   
                     </div>
                     <div class="tab-pane fade " id="tab_borders_icons-6" role="tabpanel">
                               
                            @if(!empty($Shipping))  
          <form action="{{url('AddDefaultShipping')}}" method="post" enctype="multipart/form-data" class="form-row">
                                  {!! csrf_field() !!}
               @honeypot
                       
                             <div class="form-group col-lg-4">
                                            <label class="form-label" for="">{{trans('admin.Delegate')}}</label>
                                            <select class="select2 form-control w-100" name="Delegate" required>
                                          <option value=""> {{trans('admin.Delegate')}}</option>
                                            @foreach($Employess as $emp)    
                       <option value="{{$emp->id}}" @if($Shipping->Delegate == $emp->id) selected @endif>
                               
                          {{app()->getLocale() == 'ar' ?$emp->Name :$emp->NameEn}}  
                                                </option>
                                            @endforeach  
                                            </select>
                                        </div>
                                            
                                            <div class="form-group col-lg-4">
                                            <label class="form-label" for="">{{trans('admin.Vendor')}}</label>
                                            <select class="select2 form-control w-100" name="Vendor" required>
                                          <option value=""> {{trans('admin.Vendor')}}</option>
                                            @foreach($vendors as $vend)    
                                                <option value="{{$vend->id}}" @if($Shipping->Vendor == $vend->id) selected @endif>
                       
                                                    
                                      {{app()->getLocale() == 'ar' ?$vend->Name :$vend->NameEn}}                  
                                                </option>
                                            @endforeach  
                                            </select>
                                        </div>
                                            
                                            <div class="form-group col-lg-4">
                                            <label class="form-label" for="">{{trans('admin.Client')}}</label>
    <select class="select2 form-control w-100"  name="Client"  required>
                                          <option value=""> {{trans('admin.Client')}}</option>
                                            @foreach($clients as $cli)    
                                                <option value="{{$cli->id}}" @if($Shipping->Client == $cli->id) selected @endif>
                                             {{app()->getLocale() == 'ar' ?$cli->Name :$cli->NameEn}}                   
                                                </option>
                                            @endforeach  
                                            </select>
                                        </div>

                               <div class="form-group col-lg-4">
                                            <label class="form-label" for="">{{trans('admin.ShippingType')}}</label>
                                            <select class="select2 form-control w-100" name="Type" required>
                                          <option value=""> {{trans('admin.ShippingType')}}</option>
                                            @foreach($Types as $typ)    
                                                <option value="{{$typ->id}}" @if($Shipping->Type == $typ->id) selected @endif>
                                         
                                               {{app()->getLocale() == 'ar' ?$typ->Arabic_Name :$typ->English_Name}}         
                                                </option>
                                            @endforeach  
                                            </select>
                                        </div>    
    
             <div class="form-group col-lg-4">
                                            <label class="form-label" for="">{{trans('admin.ShippingStatus')}}</label>
                                            <select class="select2 form-control w-100" name="Status" required>
                                          <option value=""> {{trans('admin.ShippingStatus')}}</option>
                                            @foreach($Status as $st)    
                                                <option value="{{$st->id}}" @if($Shipping->Status == $st->id) selected @endif> 
                                               {{app()->getLocale() == 'ar' ?$st->Arabic_Name :$st->English_Name}}                
                                                </option>
                                            @endforeach  
                                            </select>
                                        </div>    
                             <div class="form-group col-lg-4">
                                            <label class="form-label" for="">{{trans('admin.Breakable')}}</label>
                                            <select class="select2 form-control w-100" name="Breakable" required>
                                          <option value=""> {{trans('admin.Breakable')}}</option>
                                          <option value="1" @if($Shipping->Breakable == 1) selected @endif> {{trans('admin.Yes')}}</option>
                                          <option value="0" @if($Shipping->Breakable == 0) selected @endif> {{trans('admin.No')}}</option>
                                            </select>
                                        </div>       
                                               <div class="form-group col-lg-4">
                                            <label class="form-label" for="">{{trans('admin.Coin')}}</label>
                                            <select class="select2 form-control w-100" name="Coin" required>
                                                 <option value=""> {{trans('admin.Coin')}}</option>
                                            @foreach($Coins as $coin)    
                                                <option value="{{$coin->id}}" @if($Shipping->Coin == $coin->id) selected @endif>
                                               {{app()->getLocale() == 'ar' ?$coin->Arabic_Name :$coin->English_Name}} 
                                                </option>
                                            @endforeach  
                                            </select>
                                        </div>
              
                        <div class="form-group col-lg-4">
                              <label class="form-label" for="">{{trans('admin.Safe')}}</label>
                              <select class="select2 form-control w-100" name="Safe" required>
                                 <option value=""> {{trans('admin.Safe')}}</option>
                                 @foreach($Safes as $safe)    
                                 <option value="{{$safe->id}}" @if($safe->id  == $Shipping->Safe) selected @endif>
                             {{app()->getLocale() == 'ar' ?$safe->Name :$safe->NameEn}}         
                                 </option>
                                 @endforeach    
                              </select>
                           </div>
              
                <div class="form-group col-lg-4">
                                            <label class="form-label" for="">{{trans('admin.Payment_Method')}}</label>
                                            <select class="select2 form-control w-100" name="Payment_Method" required>
                                          <option value=""> {{trans('admin.Payment_Method')}}</option>
                                          <option value="Cash" @if($Shipping->Payment_Method == 'Cash') selected @endif> {{trans('admin.Cash')}}</option>
                                          <option value="Later" @if($Shipping->Payment_Method == 'Later') selected @endif> {{trans('admin.Later')}}</option>
                                            </select>
                                        </div>    
              
              
              
              
                 <div class="form-group col-lg-12">
                     <h2> {{trans('admin.Show_Hide_Data')}}</h2>
                    </div>
              
              
                       <div class="form-group col-lg-3">
                              <label class="form-label" for="">{{trans('admin.Coin')}}</label>
                              <select class="select2 form-control w-100" name="Show_Coin"    required>
                              <option value="1" @if($Shipping->Show_Coin == 1) selected @endif>{{trans('admin.Show')}} </option>
                              <option value="0" @if( $Shipping->Show_Coin == 0) selected @endif>{{trans('admin.Hide')}}</option>
                              </select>
                           </div>
              
                         <div class="form-group col-lg-3">
                              <label class="form-label" for="">{{trans('admin.Draw')}}</label>
                              <select class="select2 form-control w-100" name="Show_Draw"    required>
                              <option value="1" @if($Shipping->Show_Draw == 1) selected @endif>{{trans('admin.Show')}} </option>
                              <option value="0" @if( $Shipping->Show_Draw == 0) selected @endif>{{trans('admin.Hide')}}</option>
                              </select>
                           </div>
              
                        <div class="form-group col-lg-3">
                              <label class="form-label" for="">{{trans('admin.Safe')}}</label>
                              <select class="select2 form-control w-100" name="Show_Safe"    required>
                              <option value="1" @if($Shipping->Show_Safe == 1) selected @endif>{{trans('admin.Show')}} </option>
                              <option value="0" @if( $Shipping->Show_Safe == 0) selected @endif>{{trans('admin.Hide')}}</option>
                              </select>
                           </div>
              
                        <div class="form-group col-lg-3">
                              <label class="form-label" for="">{{trans('admin.Code')}}</label>
                              <select class="select2 form-control w-100" name="Show_Code"    required>
                              <option value="1" @if($Shipping->Show_Code == 1) selected @endif>{{trans('admin.Show')}} </option>
                              <option value="0" @if( $Shipping->Show_Code == 0) selected @endif>{{trans('admin.Hide')}}</option>
                              </select>
                           </div>
              
              
                        <div class="form-group col-lg-3">
                              <label class="form-label" for="">{{trans('admin.Weight')}}</label>
                              <select class="select2 form-control w-100" name="Show_Weight"    required>
                              <option value="1" @if($Shipping->Show_Weight == 1) selected @endif>{{trans('admin.Show')}} </option>
                              <option value="0" @if( $Shipping->Show_Weight == 0) selected @endif>{{trans('admin.Hide')}}</option>
                              </select>
                           </div>
              
              
                        <div class="form-group col-lg-3">
                              <label class="form-label" for="">{{trans('admin.Width')}}</label>
                              <select class="select2 form-control w-100" name="Show_Width"    required>
                              <option value="1" @if($Shipping->Show_Width == 1) selected @endif>{{trans('admin.Show')}} </option>
                              <option value="0" @if( $Shipping->Show_Width == 0) selected @endif>{{trans('admin.Hide')}}</option>
                              </select>
                           </div>
              
              
                        <div class="form-group col-lg-3">
                              <label class="form-label" for="">{{trans('admin.Length')}}</label>
                              <select class="select2 form-control w-100" name="Show_Length"    required>
                              <option value="1" @if($Shipping->Show_Length == 1) selected @endif>{{trans('admin.Show')}} </option>
                              <option value="0" @if( $Shipping->Show_Length == 0) selected @endif>{{trans('admin.Hide')}}</option>
                              </select>
                           </div>
              
                        <div class="form-group col-lg-3">
                              <label class="form-label" for="">{{trans('admin.Height')}}</label>
                              <select class="select2 form-control w-100" name="Show_Height"    required>
                              <option value="1" @if($Shipping->Show_Height == 1) selected @endif>{{trans('admin.Show')}} </option>
                              <option value="0" @if( $Shipping->Show_Height == 0) selected @endif>{{trans('admin.Hide')}}</option>
                              </select>
                           </div>
              
              








              
                                                                              <div class="col-md-12">
                                <button type="submit" class="btn btn-primary">{{trans('admin.Save')}}</button>
                                                    </div> 
                                                </form>         
                                    @else
         <div  class="form-row">
           <div class="col-md-12">
              <div class="data-def">
          <div class="form-row">
               <div class="form-group col-md-12">
              <a class="btn btn-primary" href="{{url('AddDefaultShippingFirst')}}">
                    {{trans('admin.AddNew')}}
                   </a>   
              </div>
              </div>
               </div>
             </div>
                      </div>
                                    @endif                   
                                                
                                                
                                            </div>     
                     <div class="tab-pane fade" id="tab_borders_icons-7" role="tabpanel">
                        @if(!empty($CustomPrint))          
                        <form action="{{url('AddDefaultCustomPrint')}}" method="post"  class="form-row">
                           {!! csrf_field() !!}  
                           @honeypot
                           <div class="col-md-12">
                              <div class="data-def">
                                 <div class="form-row">
                             
                                              <div class="form-group col-lg-12">
                                                  <h1 class="text-center head">{{trans('admin.Sales')}}</h1>              
                                                </div>
                                     
                                     
                                     
                                     
                                              <div class="form-group col-lg-12">
                              <label class="form-label" for="">{{trans('admin.Print_Type')}}</label>
                              <select class="select2 form-control w-100" name="Sales_Print_Type"  required>
                              <option value="1" @if($CustomPrint->Sales_Print_Type == 1) selected @endif>Landscape </option>
                              <option value="2" @if( $CustomPrint->Sales_Print_Type == 2) selected @endif>Portarit</option>
                              </select>
                           </div>
                                     
                                                            <div class="form-group col-lg-3">
                              <label class="form-label" for="">{{trans('admin.Bill_Code')}}</label>
                              <select class="select2 form-control w-100" name="Sales_Bill_Code"  required>
                              <option value="1" @if($CustomPrint->Sales_Bill_Code == 1) selected @endif>{{trans('admin.Show')}} </option>
                              <option value="0" @if( $CustomPrint->Sales_Bill_Code == 0) selected @endif>{{trans('admin.Hide')}}</option>
                              </select>
                           </div>      
                                     
                                     <div class="form-group col-lg-3">
                              <label class="form-label" for="">{{trans('admin.Date')}}</label>
                              <select class="select2 form-control w-100" name="Sales_Date"  required>
                              <option value="1" @if($CustomPrint->Sales_Date == 1) selected @endif>{{trans('admin.Show')}} </option>
                              <option value="0" @if( $CustomPrint->Sales_Date == 0) selected @endif>{{trans('admin.Hide')}}</option>
                              </select>
                           </div>         
                                     
                                     
                                            <div class="form-group col-lg-3">
                              <label class="form-label" for="">{{trans('admin.Coin')}}</label>
                              <select class="select2 form-control w-100" name="Sales_Coin"  required>
                              <option value="1" @if($CustomPrint->Sales_Coin == 1) selected @endif>{{trans('admin.Show')}} </option>
                              <option value="0" @if( $CustomPrint->Sales_Coin == 0) selected @endif>{{trans('admin.Hide')}}</option>
                              </select>
                           </div>         
                                     
                                                        <div class="form-group col-lg-3">
                              <label class="form-label" for="">{{trans('admin.Draw')}}</label>
                              <select class="select2 form-control w-100" name="Sales_Draw"  required>
                              <option value="1" @if($CustomPrint->Sales_Draw == 1) selected @endif>{{trans('admin.Show')}} </option>
                              <option value="0" @if( $CustomPrint->Sales_Draw == 0) selected @endif>{{trans('admin.Hide')}}</option>
                              </select>
                           </div>         
                                     
                                                          
                                            <div class="form-group col-lg-3">
                              <label class="form-label" for="">{{trans('admin.Payment_Method')}}</label>
                              <select class="select2 form-control w-100" name="Sales_Payment_Method"  required>
                              <option value="1" @if($CustomPrint->Sales_Payment_Method == 1) selected @endif>{{trans('admin.Show')}} </option>
                              <option value="0" @if( $CustomPrint->Sales_Payment_Method == 0) selected @endif>{{trans('admin.Hide')}}</option>
                              </select>
                           </div>         
                                     
                                                        <div class="form-group col-lg-3">
                              <label class="form-label" for="">{{trans('admin.Status')}}</label>
                              <select class="select2 form-control w-100" name="Sales_Status"  required>
                              <option value="1" @if($CustomPrint->Sales_Status == 1) selected @endif>{{trans('admin.Show')}} </option>
                              <option value="0" @if( $CustomPrint->Sales_Status == 0) selected @endif>{{trans('admin.Hide')}}</option>
                              </select>
                           </div>         
                                     
                                                          
                                            <div class="form-group col-lg-3">
                              <label class="form-label" for="">{{trans('admin.Executor')}}</label>
                              <select class="select2 form-control w-100" name="Sales_Executor"  required>
                              <option value="1" @if($CustomPrint->Sales_Executor == 1) selected @endif>{{trans('admin.Show')}} </option>
                              <option value="0" @if( $CustomPrint->Sales_Executor == 0) selected @endif>{{trans('admin.Hide')}}</option>
                              </select>
                           </div>         
                                     
                                                        <div class="form-group col-lg-3">
                              <label class="form-label" for="">{{trans('admin.Refernce_Number')}}</label>
                              <select class="select2 form-control w-100" name="Sales_Refernce_Number"  required>
                              <option value="1" @if($CustomPrint->Sales_Refernce_Number == 1) selected @endif>{{trans('admin.Show')}} </option>
                              <option value="0" @if( $CustomPrint->Sales_Refernce_Number == 0) selected @endif>{{trans('admin.Hide')}}</option>
                              </select>
                           </div>         
                                     
                                                          
                                            <div class="form-group col-lg-3">
                              <label class="form-label" for="">{{trans('admin.Safe')}}</label>
                              <select class="select2 form-control w-100" name="Sales_Safe"  required>
                              <option value="1" @if($CustomPrint->Sales_Safe == 1) selected @endif>{{trans('admin.Show')}} </option>
                              <option value="0" @if( $CustomPrint->Sales_Safe == 0) selected @endif>{{trans('admin.Hide')}}</option>
                              </select>
                           </div>         
                                     
                                                        <div class="form-group col-lg-3">
                              <label class="form-label" for="">{{trans('admin.Client')}}</label>
                              <select class="select2 form-control w-100" name="Sales_Client"  required>
                              <option value="1" @if($CustomPrint->Sales_Client == 1) selected @endif>{{trans('admin.Show')}} </option>
                              <option value="0" @if( $CustomPrint->Sales_Client == 0) selected @endif>{{trans('admin.Hide')}}</option>
                              </select>
                           </div>         
                                     
                                                          
                                            <div class="form-group col-lg-3">
                              <label class="form-label" for="">{{trans('admin.Delegate')}}</label>
                              <select class="select2 form-control w-100" name="Sales_Delegate"  required>
                              <option value="1" @if($CustomPrint->Sales_Delegate == 1) selected @endif>{{trans('admin.Show')}} </option>
                              <option value="0" @if( $CustomPrint->Sales_Delegate == 0) selected @endif>{{trans('admin.Hide')}}</option>
                              </select>
                           </div>         
                                     
                                                        <div class="form-group col-lg-3">
                              <label class="form-label" for="">{{trans('admin.Store')}}</label>
                              <select class="select2 form-control w-100" name="Sales_Store"  required>
                              <option value="1" @if($CustomPrint->Sales_Store == 1) selected @endif>{{trans('admin.Show')}} </option>
                              <option value="0" @if( $CustomPrint->Sales_Store == 0) selected @endif>{{trans('admin.Hide')}}</option>
                              </select>
                           </div>         
                                     
                                                          
                                            <div class="form-group col-lg-4">
                              <label class="form-label" for="">{{trans('admin.User')}}</label>
                              <select class="select2 form-control w-100" name="Sales_User"  required>
                              <option value="1" @if($CustomPrint->Sales_User == 1) selected @endif>{{trans('admin.Show')}} </option>
                              <option value="0" @if( $CustomPrint->Sales_User == 0) selected @endif>{{trans('admin.Hide')}}</option>
                              </select>
                           </div>         
                                     
                                                        <div class="form-group col-lg-4">
                              <label class="form-label" for="">{{trans('admin.Cost_Center')}}</label>
                              <select class="select2 form-control w-100" name="Sales_Cost_Center"  required>
                              <option value="1" @if($CustomPrint->Sales_Cost_Center == 1) selected @endif>{{trans('admin.Show')}} </option>
                              <option value="0" @if( $CustomPrint->Sales_Cost_Center == 0) selected @endif>{{trans('admin.Hide')}}</option>
                              </select>
                           </div>         
                                     
                                                   
                                                        <div class="form-group col-lg-4">
                              <label class="form-label" for="">{{trans('admin.Notes')}}</label>
                              <select class="select2 form-control w-100" name="Sales_Notes"  required>
                              <option value="1" @if($CustomPrint->Sales_Notes == 1) selected @endif>{{trans('admin.Show')}} </option>
                              <option value="0" @if( $CustomPrint->Sales_Notes == 0) selected @endif>{{trans('admin.Hide')}}</option>
                              </select>
                           </div>         
                                     
                                     
                                     
                                       <div class="form-group col-lg-12">
                                                      <h4 class="text-center suppp">{{trans('admin.Products')}}</h4>    
                                     </div>
                               
                                     
                                     
                                                           
                                                   
                                                        <div class="form-group col-lg-3">
                              <label class="form-label" for="">{{trans('admin.Product_Code')}}</label>
                              <select class="select2 form-control w-100" name="Sales_Pro_Code"  required>
                              <option value="1" @if($CustomPrint->Sales_Pro_Code == 1) selected @endif>{{trans('admin.Show')}} </option>
                              <option value="0" @if( $CustomPrint->Sales_Pro_Code == 0) selected @endif>{{trans('admin.Hide')}}</option>
                              </select>
                           </div>         
                                                                     
                                                   
                                                        <div class="form-group col-lg-3">
                              <label class="form-label" for="">{{trans('admin.Product_Name')}}</label>
                              <select class="select2 form-control w-100" name="Sales_Pro_Name"  required>
                              <option value="1" @if($CustomPrint->Sales_Pro_Name == 1) selected @endif>{{trans('admin.Show')}} </option>
                              <option value="0" @if( $CustomPrint->Sales_Pro_Name == 0) selected @endif>{{trans('admin.Hide')}}</option>
                              </select>
                           </div>         
                                                                     
                                                   
                                                        <div class="form-group col-lg-3">
                              <label class="form-label" for="">{{trans('admin.Unit')}}</label>
                              <select class="select2 form-control w-100" name="Sales_Pro_Unit"  required>
                              <option value="1" @if($CustomPrint->Sales_Pro_Unit == 1) selected @endif>{{trans('admin.Show')}} </option>
                              <option value="0" @if( $CustomPrint->Sales_Pro_Unit == 0) selected @endif>{{trans('admin.Hide')}}</option>
                              </select>
                           </div>         
                                                                     
                                                   
                                                        <div class="form-group col-lg-3">
                              <label class="form-label" for="">{{trans('admin.Qty')}}</label>
                              <select class="select2 form-control w-100" name="Sales_Pro_Qty"  required>
                              <option value="1" @if($CustomPrint->Sales_Pro_Qty == 1) selected @endif>{{trans('admin.Show')}} </option>
                              <option value="0" @if( $CustomPrint->Sales_Pro_Qty == 0) selected @endif>{{trans('admin.Hide')}}</option>
                              </select>
                           </div>         
                              
                                     
                                                                      
                                                        <div class="form-group col-lg-3">
                              <label class="form-label" for="">{{trans('admin.Price')}}</label>
                              <select class="select2 form-control w-100" name="Sales_Pro_Price"  required>
                              <option value="1" @if($CustomPrint->Sales_Pro_Price == 1) selected @endif>{{trans('admin.Show')}} </option>
                              <option value="0" @if( $CustomPrint->Sales_Pro_Price == 0) selected @endif>{{trans('admin.Hide')}}</option>
                              </select>
                           </div>         
                                                                     
                                                   
                                                        <div class="form-group col-lg-3">
                              <label class="form-label" for="">{{trans('admin.Discount')}}</label>
                              <select class="select2 form-control w-100" name="Sales_Pro_Discount"  required>
                              <option value="1" @if($CustomPrint->Sales_Pro_Discount == 1) selected @endif>{{trans('admin.Show')}} </option>
                              <option value="0" @if( $CustomPrint->Sales_Pro_Discount == 0) selected @endif>{{trans('admin.Hide')}}</option>
                              </select>
                           </div>         
                                                                     
                                                   
                                                        <div class="form-group col-lg-3">
                              <label class="form-label" for="">{{trans('admin.Total_Bf_Tax')}}</label>
                              <select class="select2 form-control w-100" name="Sales_Pro_Total_Bf_Tax"  required>
                              <option value="1" @if($CustomPrint->Sales_Pro_Total_Bf_Tax == 1) selected @endif>{{trans('admin.Show')}} </option>
                              <option value="0" @if( $CustomPrint->Sales_Pro_Total_Bf_Tax == 0) selected @endif>{{trans('admin.Hide')}}</option>
                              </select>
                           </div>         
                                                                     
                                                   
                                                        <div class="form-group col-lg-3">
                              <label class="form-label" for="">{{trans('admin.Total_Tax')}}</label>
                              <select class="select2 form-control w-100" name="Sales_Pro_Total_Tax"  required>
                              <option value="1" @if($CustomPrint->Sales_Pro_Total_Tax == 1) selected @endif>{{trans('admin.Show')}} </option>
                              <option value="0" @if( $CustomPrint->Sales_Pro_Total_Tax == 0) selected @endif>{{trans('admin.Hide')}}</option>
                              </select>
                           </div>         
                              
                                     
                                                                      
                                                        <div class="form-group col-lg-3">
                              <label class="form-label" for="">{{trans('admin.Total')}}</label>
                              <select class="select2 form-control w-100" name="Sales_Pro_Total"  required>
                              <option value="1" @if($CustomPrint->Sales_Pro_Total == 1) selected @endif>{{trans('admin.Show')}} </option>
                              <option value="0" @if( $CustomPrint->Sales_Pro_Total == 0) selected @endif>{{trans('admin.Hide')}}</option>
                              </select>
                           </div>         
                                                                     
                                                   
                                                        <div class="form-group col-lg-3">
                              <label class="form-label" for="">{{trans('admin.Store')}}</label>
                              <select class="select2 form-control w-100" name="Sales_Pro_Store"  required>
                              <option value="1" @if($CustomPrint->Sales_Pro_Store == 1) selected @endif>{{trans('admin.Show')}} </option>
                              <option value="0" @if( $CustomPrint->Sales_Pro_Store == 0) selected @endif>{{trans('admin.Hide')}}</option>
                              </select>
                           </div>         
                                                                     
                                                   
                                                        <div class="form-group col-lg-3">
                              <label class="form-label" for="">{{trans('admin.Desc')}}</label>
                              <select class="select2 form-control w-100" name="Sales_Pro_Desc"  required>
                              <option value="1" @if($CustomPrint->Sales_Pro_Desc == 1) selected @endif>{{trans('admin.Show')}} </option>
                              <option value="0" @if( $CustomPrint->Sales_Pro_Desc == 0) selected @endif>{{trans('admin.Hide')}}</option>
                              </select>
                           </div>         
                                                                     
                                                   
                                                        <div class="form-group col-lg-3">
                              <label class="form-label" for="">{{trans('admin.Exp_Date')}}</label>
                              <select class="select2 form-control w-100" name="Sales_Pro_Exp_Date"  required>
                              <option value="1" @if($CustomPrint->Sales_Pro_Exp_Date == 1) selected @endif>{{trans('admin.Show')}} </option>
                              <option value="0" @if( $CustomPrint->Sales_Pro_Exp_Date == 0) selected @endif>{{trans('admin.Hide')}}</option>
                              </select>
                           </div>         
                              
                                     
                                                                      
                                                        <div class="form-group col-lg-6">
                              <label class="form-label" for="">{{trans('admin.Weight')}}</label>
                              <select class="select2 form-control w-100" name="Sales_Pro_Weight"  required>
                              <option value="1" @if($CustomPrint->Sales_Pro_Weight == 1) selected @endif>{{trans('admin.Show')}} </option>
                              <option value="0" @if( $CustomPrint->Sales_Pro_Weight == 0) selected @endif>{{trans('admin.Hide')}}</option>
                              </select>
                           </div>         
                                                                     
                                                   
                                                        <div class="form-group col-lg-6">
                              <label class="form-label" for="">{{trans('admin.Patch_Number')}}</label>
                              <select class="select2 form-control w-100" name="Sales_Pro_Patch_Number"  required>
                              <option value="1" @if($CustomPrint->Sales_Pro_Patch_Number == 1) selected @endif>{{trans('admin.Show')}} </option>
                              <option value="0" @if( $CustomPrint->Sales_Pro_Patch_Number == 0) selected @endif>{{trans('admin.Hide')}}</option>
                              </select>
                           </div>         
                                                                     
                         
                                                    
                                       <div class="form-group col-lg-12">
                                                      <h4 class="text-center suppp">{{trans('admin.Footer')}}</h4>    
                                     </div>
                               
                                     
                                <div class="form-group col-lg-3">
                              <label class="form-label" for="">{{trans('admin.Product_Numbers')}}</label>
                              <select class="select2 form-control w-100" name="Sales_Product_Numbers"  required>
                              <option value="1" @if($CustomPrint->Sales_Product_Numbers == 1) selected @endif>{{trans('admin.Show')}} </option>
                              <option value="0" @if( $CustomPrint->Sales_Product_Numbers == 0) selected @endif>{{trans('admin.Hide')}}</option>
                              </select>
                           </div>                   
                                <div class="form-group col-lg-3">
                              <label class="form-label" for="">{{trans('admin.Total_Qty')}}</label>
                              <select class="select2 form-control w-100" name="Sales_Total_Qty"  required>
                              <option value="1" @if($CustomPrint->Sales_Total_Qty == 1) selected @endif>{{trans('admin.Show')}} </option>
                              <option value="0" @if( $CustomPrint->Sales_Total_Qty == 0) selected @endif>{{trans('admin.Hide')}}</option>
                              </select>
                           </div>                   
                                <div class="form-group col-lg-3">
                              <label class="form-label" for="">{{trans('admin.Total_Discount')}}</label>
                              <select class="select2 form-control w-100" name="Sales_Total_Discount"  required>
                              <option value="1" @if($CustomPrint->Sales_Total_Discount == 1) selected @endif>{{trans('admin.Show')}} </option>
                              <option value="0" @if( $CustomPrint->Sales_Total_Discount == 0) selected @endif>{{trans('admin.Hide')}}</option>
                              </select>
                           </div>                   
                                <div class="form-group col-lg-3">
                              <label class="form-label" for="">{{trans('admin.Total_Bf_Taxes')}}</label>
                              <select class="select2 form-control w-100" name="Sales_Total_Bf_Taxes"  required>
                              <option value="1" @if($CustomPrint->Sales_Total_Bf_Taxes == 1) selected @endif>{{trans('admin.Show')}} </option>
                              <option value="0" @if( $CustomPrint->Sales_Total_Bf_Taxes == 0) selected @endif>{{trans('admin.Hide')}}</option>
                              </select>
                           </div>                   
                                <div class="form-group col-lg-3">
                              <label class="form-label" for="">{{trans('admin.Total_Taxes')}}</label>
                              <select class="select2 form-control w-100" name="Sales_Total_Taxes"  required>
                              <option value="1" @if($CustomPrint->Sales_Total_Taxes == 1) selected @endif>{{trans('admin.Show')}} </option>
                              <option value="0" @if( $CustomPrint->Sales_Total_Taxes == 0) selected @endif>{{trans('admin.Hide')}}</option>
                              </select>
                           </div>                   
                                <div class="form-group col-lg-3">
                              <label class="form-label" for="">{{trans('admin.Total_Price')}}</label>
                              <select class="select2 form-control w-100" name="Sales_Total_Price"  required>
                              <option value="1" @if($CustomPrint->Sales_Total_Price == 1) selected @endif>{{trans('admin.Show')}} </option>
                              <option value="0" @if( $CustomPrint->Sales_Total_Price == 0) selected @endif>{{trans('admin.Hide')}}</option>
                              </select>
                           </div>                   
                                <div class="form-group col-lg-3">
                              <label class="form-label" for="">{{trans('admin.Paid')}}</label>
                              <select class="select2 form-control w-100" name="Sales_Paid"  required>
                              <option value="1" @if($CustomPrint->Sales_Paid == 1) selected @endif>{{trans('admin.Show')}} </option>
                              <option value="0" @if( $CustomPrint->Sales_Paid == 0) selected @endif>{{trans('admin.Hide')}}</option>
                              </select>
                           </div>                   
                                <div class="form-group col-lg-3">
                              <label class="form-label" for="">{{trans('admin.Residual')}}</label>
                              <select class="select2 form-control w-100" name="Sales_Residual"  required>
                              <option value="1" @if($CustomPrint->Sales_Residual == 1) selected @endif>{{trans('admin.Show')}} </option>
                              <option value="0" @if( $CustomPrint->Sales_Residual == 0) selected @endif>{{trans('admin.Hide')}}</option>
                              </select>
                           </div>                   
                                <div class="form-group col-lg-3">
                              <label class="form-label" for="">{{trans('admin.Taknet')}}</label>
                              <select class="select2 form-control w-100" name="Sales_Taknet"  required>
                              <option value="1" @if($CustomPrint->Sales_Taknet == 1) selected @endif>{{trans('admin.Show')}} </option>
                              <option value="0" @if( $CustomPrint->Sales_Taknet == 0) selected @endif>{{trans('admin.Hide')}}</option>
                              </select>
                           </div>                   
                                <div class="form-group col-lg-3">
                              <label class="form-label" for="">{{trans('admin.Credit')}}</label>
                              <select class="select2 form-control w-100" name="Sales_Credit"  required>
                              <option value="1" @if($CustomPrint->Sales_Credit == 1) selected @endif>{{trans('admin.Show')}} </option>
                              <option value="0" @if( $CustomPrint->Sales_Credit == 0) selected @endif>{{trans('admin.Hide')}}</option>
                              </select>
                           </div>                   
                                <div class="form-group col-lg-3">
                              <label class="form-label" for="">{{trans('admin.Barcode')}}</label>
                              <select class="select2 form-control w-100" name="Sales_Barcode"  required>
                              <option value="1" @if($CustomPrint->Sales_Barcode == 1) selected @endif>{{trans('admin.Show')}} </option>
                              <option value="0" @if( $CustomPrint->Sales_Barcode == 0) selected @endif>{{trans('admin.Hide')}}</option>
                              </select>
                           </div>                   
                                <div class="form-group col-lg-3">
                              <label class="form-label" for="">{{trans('admin.Text')}}</label>
                              <select class="select2 form-control w-100" name="Sales_Text"  required>
                              <option value="1" @if($CustomPrint->Sales_Text == 1) selected @endif>{{trans('admin.Show')}} </option>
                              <option value="0" @if( $CustomPrint->Sales_Text == 0) selected @endif>{{trans('admin.Hide')}}</option>
                              </select>
                           </div>                   
                                <div class="form-group col-lg-12">
                              <label class="form-label" for="">{{trans('admin.Seal')}}</label>
                              <select class="select2 form-control w-100" name="Sales_Seal"  required>
                              <option value="1" @if($CustomPrint->Sales_Seal == 1) selected @endif>{{trans('admin.Show')}} </option>
                              <option value="0" @if( $CustomPrint->Sales_Seal == 0) selected @endif>{{trans('admin.Hide')}}</option>
                              </select>
                           </div>         
                                       


                                     
                                      <div class="form-group col-lg-12">
                                                  <h1 class="text-center head">{{trans('admin.Purchases')}}</h1>              
                                                </div>
                                     
                                     
                                     
                                     
                                              <div class="form-group col-lg-12">
                              <label class="form-label" for="">{{trans('admin.Print_Type')}}</label>
                              <select class="select2 form-control w-100" name="Purch_Print_Type"  required>
                              <option value="1" @if($CustomPrint->Purch_Print_Type == 1) selected @endif>Landscape </option>
                              <option value="2" @if( $CustomPrint->Purch_Print_Type == 2) selected @endif>Portarit</option>
                              </select>
                           </div>
                                     
                                                            <div class="form-group col-lg-3">
                              <label class="form-label" for="">{{trans('admin.Bill_Code')}}</label>
                              <select class="select2 form-control w-100" name="Purch_Bill_Code"  required>
                              <option value="1" @if($CustomPrint->Purch_Bill_Code == 1) selected @endif>{{trans('admin.Show')}} </option>
                              <option value="0" @if( $CustomPrint->Purch_Bill_Code == 0) selected @endif>{{trans('admin.Hide')}}</option>
                              </select>
                           </div>      
                                     
                                     <div class="form-group col-lg-3">
                              <label class="form-label" for="">{{trans('admin.Date')}}</label>
                              <select class="select2 form-control w-100" name="Purch_Date"  required>
                              <option value="1" @if($CustomPrint->Purch_Date == 1) selected @endif>{{trans('admin.Show')}} </option>
                              <option value="0" @if( $CustomPrint->Purch_Date == 0) selected @endif>{{trans('admin.Hide')}}</option>
                              </select>
                           </div>         
                                     
                                     
                                            <div class="form-group col-lg-3">
                              <label class="form-label" for="">{{trans('admin.Vendor_Bill_Date')}}</label>
                              <select class="select2 form-control w-100" name="Purch_Vendor_Bill_Date"  required>
                              <option value="1" @if($CustomPrint->Purch_Vendor_Bill_Date == 1) selected @endif>{{trans('admin.Show')}} </option>
                              <option value="0" @if( $CustomPrint->Purch_Vendor_Bill_Date == 0) selected @endif>{{trans('admin.Hide')}}</option>
                              </select>
                           </div>         
                                     
                                                        <div class="form-group col-lg-3">
                              <label class="form-label" for="">{{trans('admin.Coin')}}</label>
                              <select class="select2 form-control w-100" name="Purch_Coin"  required>
                              <option value="1" @if($CustomPrint->Purch_Coin == 1) selected @endif>{{trans('admin.Show')}} </option>
                              <option value="0" @if( $CustomPrint->Purch_Coin == 0) selected @endif>{{trans('admin.Hide')}}</option>
                              </select>
                           </div>         
                                     
                                                          
                                            <div class="form-group col-lg-3">
                              <label class="form-label" for="">{{trans('admin.Draw')}}</label>
                              <select class="select2 form-control w-100" name="Purch_Draw"  required>
                              <option value="1" @if($CustomPrint->Purch_Draw == 1) selected @endif>{{trans('admin.Show')}} </option>
                              <option value="0" @if( $CustomPrint->Purch_Draw == 0) selected @endif>{{trans('admin.Hide')}}</option>
                              </select>
                           </div>         
                                     
                                                        <div class="form-group col-lg-3">
                              <label class="form-label" for="">{{trans('admin.Payment_Method')}}</label>
                              <select class="select2 form-control w-100" name="Purch_Payment_Method"  required>
                              <option value="1" @if($CustomPrint->Purch_Payment_Method == 1) selected @endif>{{trans('admin.Show')}} </option>
                              <option value="0" @if( $CustomPrint->Purch_Payment_Method == 0) selected @endif>{{trans('admin.Hide')}}</option>
                              </select>
                           </div>         
                                     
                                                          
                                            <div class="form-group col-lg-3">
                              <label class="form-label" for="">{{trans('admin.Status')}}</label>
                              <select class="select2 form-control w-100" name="Purch_Status"  required>
                              <option value="1" @if($CustomPrint->Purch_Status == 1) selected @endif>{{trans('admin.Show')}} </option>
                              <option value="0" @if( $CustomPrint->Purch_Status == 0) selected @endif>{{trans('admin.Hide')}}</option>
                              </select>
                           </div>         
                                     
                                                        <div class="form-group col-lg-3">
                              <label class="form-label" for="">{{trans('admin.Refernce_Number')}}</label>
                              <select class="select2 form-control w-100" name="Purch_Refernce_Number"  required>
                              <option value="1" @if($CustomPrint->Purch_Refernce_Number == 1) selected @endif>{{trans('admin.Show')}} </option>
                              <option value="0" @if( $CustomPrint->Purch_Refernce_Number == 0) selected @endif>{{trans('admin.Hide')}}</option>
                              </select>
                           </div>         
                                     
                                                          
                                            <div class="form-group col-lg-3">
                              <label class="form-label" for="">{{trans('admin.Safe')}}</label>
                              <select class="select2 form-control w-100" name="Purch_Safe"  required>
                              <option value="1" @if($CustomPrint->Purch_Safe == 1) selected @endif>{{trans('admin.Show')}} </option>
                              <option value="0" @if( $CustomPrint->Purch_Safe == 0) selected @endif>{{trans('admin.Hide')}}</option>
                              </select>
                           </div>         
                                     
                                                        <div class="form-group col-lg-3">
                              <label class="form-label" for="">{{trans('admin.Vendor')}}</label>
                              <select class="select2 form-control w-100" name="Purch_Vendor"  required>
                              <option value="1" @if($CustomPrint->Purch_Vendor == 1) selected @endif>{{trans('admin.Show')}} </option>
                              <option value="0" @if( $CustomPrint->Purch_Vendor == 0) selected @endif>{{trans('admin.Hide')}}</option>
                              </select>
                           </div>         
                                     
                                                          
                                            <div class="form-group col-lg-3">
                              <label class="form-label" for="">{{trans('admin.Delegate')}}</label>
                              <select class="select2 form-control w-100" name="Purch_Delegate"  required>
                              <option value="1" @if($CustomPrint->Purch_Delegate == 1) selected @endif>{{trans('admin.Show')}} </option>
                              <option value="0" @if( $CustomPrint->Purch_Delegate == 0) selected @endif>{{trans('admin.Hide')}}</option>
                              </select>
                           </div>         
                                     
                                                        <div class="form-group col-lg-3">
                              <label class="form-label" for="">{{trans('admin.Store')}}</label>
                              <select class="select2 form-control w-100" name="Purch_Store"  required>
                              <option value="1" @if($CustomPrint->Purch_Store == 1) selected @endif>{{trans('admin.Show')}} </option>
                              <option value="0" @if( $CustomPrint->Purch_Store == 0) selected @endif>{{trans('admin.Hide')}}</option>
                              </select>
                           </div>         
                                     
                                                          
                                            <div class="form-group col-lg-4">
                              <label class="form-label" for="">{{trans('admin.User')}}</label>
                              <select class="select2 form-control w-100" name="Purch_User"  required>
                              <option value="1" @if($CustomPrint->Purch_User == 1) selected @endif>{{trans('admin.Show')}} </option>
                              <option value="0" @if( $CustomPrint->Purch_User == 0) selected @endif>{{trans('admin.Hide')}}</option>
                              </select>
                           </div>         
                                     
                                                        <div class="form-group col-lg-4">
                              <label class="form-label" for="">{{trans('admin.Cost_Center')}}</label>
                              <select class="select2 form-control w-100" name="Purch_Cost_Center"  required>
                              <option value="1" @if($CustomPrint->Purch_Cost_Center == 1) selected @endif>{{trans('admin.Show')}} </option>
                              <option value="0" @if( $CustomPrint->Purch_Cost_Center == 0) selected @endif>{{trans('admin.Hide')}}</option>
                              </select>
                           </div>         
                                     
                                                   
                                                        <div class="form-group col-lg-4">
                              <label class="form-label" for="">{{trans('admin.Notes')}}</label>
                              <select class="select2 form-control w-100" name="Purch_Notes"  required>
                              <option value="1" @if($CustomPrint->Purch_Notes == 1) selected @endif>{{trans('admin.Show')}} </option>
                              <option value="0" @if( $CustomPrint->Purch_Notes == 0) selected @endif>{{trans('admin.Hide')}}</option>
                              </select>
                           </div>         
                                     
                                     
                                     
                                       <div class="form-group col-lg-12">
                                                      <h4 class="text-center suppp">{{trans('admin.Products')}}</h4>    
                                     </div>
                               
                                     
                                     
                                                           
                                                   
                                                        <div class="form-group col-lg-3">
                              <label class="form-label" for="">{{trans('admin.Product_Code')}}</label>
                              <select class="select2 form-control w-100" name="Purch_Pro_Code"  required>
                              <option value="1" @if($CustomPrint->Purch_Pro_Code == 1) selected @endif>{{trans('admin.Show')}} </option>
                              <option value="0" @if( $CustomPrint->Purch_Pro_Code == 0) selected @endif>{{trans('admin.Hide')}}</option>
                              </select>
                           </div>         
                                                                     
                                                   
                                                        <div class="form-group col-lg-3">
                              <label class="form-label" for="">{{trans('admin.Product_Name')}}</label>
                              <select class="select2 form-control w-100" name="Purch_Pro_Name"  required>
                              <option value="1" @if($CustomPrint->Purch_Pro_Name == 1) selected @endif>{{trans('admin.Show')}} </option>
                              <option value="0" @if( $CustomPrint->Purch_Pro_Name == 0) selected @endif>{{trans('admin.Hide')}}</option>
                              </select>
                           </div>         
                                                                     
                                                   
                                                        <div class="form-group col-lg-3">
                              <label class="form-label" for="">{{trans('admin.Unit')}}</label>
                              <select class="select2 form-control w-100" name="Purch_Pro_Unit"  required>
                              <option value="1" @if($CustomPrint->Purch_Pro_Unit == 1) selected @endif>{{trans('admin.Show')}} </option>
                              <option value="0" @if( $CustomPrint->Purch_Pro_Unit == 0) selected @endif>{{trans('admin.Hide')}}</option>
                              </select>
                           </div>         
                                                                     
                                                   
                                                        <div class="form-group col-lg-3">
                              <label class="form-label" for="">{{trans('admin.Qty')}}</label>
                              <select class="select2 form-control w-100" name="Purch_Pro_Qty"  required>
                              <option value="1" @if($CustomPrint->Purch_Pro_Qty == 1) selected @endif>{{trans('admin.Show')}} </option>
                              <option value="0" @if( $CustomPrint->Purch_Pro_Qty == 0) selected @endif>{{trans('admin.Hide')}}</option>
                              </select>
                           </div>         
                              
                                     
                                                                      
                                                        <div class="form-group col-lg-3">
                              <label class="form-label" for="">{{trans('admin.Price')}}</label>
                              <select class="select2 form-control w-100" name="Purch_Pro_Price"  required>
                              <option value="1" @if($CustomPrint->Purch_Pro_Price == 1) selected @endif>{{trans('admin.Show')}} </option>
                              <option value="0" @if( $CustomPrint->Purch_Pro_Price == 0) selected @endif>{{trans('admin.Hide')}}</option>
                              </select>
                           </div>         
                                                                     
                                                   
                                                        <div class="form-group col-lg-3">
                              <label class="form-label" for="">{{trans('admin.Discount')}}</label>
                              <select class="select2 form-control w-100" name="Purch_Pro_Discount"  required>
                              <option value="1" @if($CustomPrint->Purch_Pro_Discount == 1) selected @endif>{{trans('admin.Show')}} </option>
                              <option value="0" @if( $CustomPrint->Purch_Pro_Discount == 0) selected @endif>{{trans('admin.Hide')}}</option>
                              </select>
                           </div>         
                                                                     
                                                   
                                                        <div class="form-group col-lg-3">
                              <label class="form-label" for="">{{trans('admin.Total_Bf_Tax')}}</label>
                              <select class="select2 form-control w-100" name="Purch_Pro_Total_Bf_Tax"  required>
                              <option value="1" @if($CustomPrint->Purch_Pro_Total_Bf_Tax == 1) selected @endif>{{trans('admin.Show')}} </option>
                              <option value="0" @if( $CustomPrint->Purch_Pro_Total_Bf_Tax == 0) selected @endif>{{trans('admin.Hide')}}</option>
                              </select>
                           </div>         
                                                                     
                                                   
                                                        <div class="form-group col-lg-3">
                              <label class="form-label" for="">{{trans('admin.Total_Tax')}}</label>
                              <select class="select2 form-control w-100" name="Purch_Pro_Total_Tax"  required>
                              <option value="1" @if($CustomPrint->Purch_Pro_Total_Tax == 1) selected @endif>{{trans('admin.Show')}} </option>
                              <option value="0" @if( $CustomPrint->Purch_Pro_Total_Tax == 0) selected @endif>{{trans('admin.Hide')}}</option>
                              </select>
                           </div>         
                              
                                     
                                                                      
                                                        <div class="form-group col-lg-4">
                              <label class="form-label" for="">{{trans('admin.Total')}}</label>
                              <select class="select2 form-control w-100" name="Purch_Pro_Total"  required>
                              <option value="1" @if($CustomPrint->Purch_Pro_Total == 1) selected @endif>{{trans('admin.Show')}} </option>
                              <option value="0" @if( $CustomPrint->Purch_Pro_Total == 0) selected @endif>{{trans('admin.Hide')}}</option>
                              </select>
                           </div>         
                                                                     
                                                   
                                                        <div class="form-group col-lg-4">
                              <label class="form-label" for="">{{trans('admin.Store')}}</label>
                              <select class="select2 form-control w-100" name="Purch_Pro_Store"  required>
                              <option value="1" @if($CustomPrint->Purch_Pro_Store == 1) selected @endif>{{trans('admin.Show')}} </option>
                              <option value="0" @if( $CustomPrint->Purch_Pro_Store == 0) selected @endif>{{trans('admin.Hide')}}</option>
                              </select>
                           </div>         
                                                                     
                                                   
                  
                                                                     
                                                   
                                                        <div class="form-group col-lg-4">
                              <label class="form-label" for="">{{trans('admin.Exp_Date')}}</label>
                              <select class="select2 form-control w-100" name="Purch_Pro_Exp_Date"  required>
                              <option value="1" @if($CustomPrint->Purch_Pro_Exp_Date == 1) selected @endif>{{trans('admin.Show')}} </option>
                              <option value="0" @if( $CustomPrint->Purch_Pro_Exp_Date == 0) selected @endif>{{trans('admin.Hide')}}</option>
                              </select>
                           </div>         
                              
                                     
                                                              
                         
                                                    
                                       <div class="form-group col-lg-12">
                                                      <h4 class="text-center suppp">{{trans('admin.Footer')}}</h4>    
                                     </div>
                               
                                     
                                <div class="form-group col-lg-3">
                              <label class="form-label" for="">{{trans('admin.Product_Numbers')}}</label>
                              <select class="select2 form-control w-100" name="Purch_Product_Numbers"  required>
                              <option value="1" @if($CustomPrint->Purch_Product_Numbers == 1) selected @endif>{{trans('admin.Show')}} </option>
                              <option value="0" @if( $CustomPrint->Purch_Product_Numbers == 0) selected @endif>{{trans('admin.Hide')}}</option>
                              </select>
                           </div>                   
                                <div class="form-group col-lg-3">
                              <label class="form-label" for="">{{trans('admin.Total_Qty')}}</label>
                              <select class="select2 form-control w-100" name="Purch_Total_Qty"  required>
                              <option value="1" @if($CustomPrint->Purch_Total_Qty == 1) selected @endif>{{trans('admin.Show')}} </option>
                              <option value="0" @if( $CustomPrint->Purch_Total_Qty == 0) selected @endif>{{trans('admin.Hide')}}</option>
                              </select>
                           </div>                   
                                <div class="form-group col-lg-3">
                              <label class="form-label" for="">{{trans('admin.Total_Discount')}}</label>
                              <select class="select2 form-control w-100" name="Purch_Total_Discount"  required>
                              <option value="1" @if($CustomPrint->Purch_Total_Discount == 1) selected @endif>{{trans('admin.Show')}} </option>
                              <option value="0" @if( $CustomPrint->Purch_Total_Discount == 0) selected @endif>{{trans('admin.Hide')}}</option>
                              </select>
                           </div>                   
                                <div class="form-group col-lg-3">
                              <label class="form-label" for="">{{trans('admin.Total_Bf_Taxes')}}</label>
                              <select class="select2 form-control w-100" name="Purch_Total_Bf_Taxes"  required>
                              <option value="1" @if($CustomPrint->Purch_Total_Bf_Taxes == 1) selected @endif>{{trans('admin.Show')}} </option>
                              <option value="0" @if( $CustomPrint->Purch_Total_Bf_Taxes == 0) selected @endif>{{trans('admin.Hide')}}</option>
                              </select>
                           </div>                   
                                <div class="form-group col-lg-3">
                              <label class="form-label" for="">{{trans('admin.Total_Taxes')}}</label>
                              <select class="select2 form-control w-100" name="Purch_Total_Taxes"  required>
                              <option value="1" @if($CustomPrint->Purch_Total_Taxes == 1) selected @endif>{{trans('admin.Show')}} </option>
                              <option value="0" @if( $CustomPrint->Purch_Total_Taxes == 0) selected @endif>{{trans('admin.Hide')}}</option>
                              </select>
                           </div>                   
                                <div class="form-group col-lg-3">
                              <label class="form-label" for="">{{trans('admin.Total_Price')}}</label>
                              <select class="select2 form-control w-100" name="Purch_Total_Price"  required>
                              <option value="1" @if($CustomPrint->Purch_Total_Price == 1) selected @endif>{{trans('admin.Show')}} </option>
                              <option value="0" @if( $CustomPrint->Purch_Total_Price == 0) selected @endif>{{trans('admin.Hide')}}</option>
                              </select>
                           </div>                   
                                <div class="form-group col-lg-3">
                              <label class="form-label" for="">{{trans('admin.Paid')}}</label>
                              <select class="select2 form-control w-100" name="Purch_Paid"  required>
                              <option value="1" @if($CustomPrint->Purch_Paid == 1) selected @endif>{{trans('admin.Show')}} </option>
                              <option value="0" @if( $CustomPrint->Purch_Paid == 0) selected @endif>{{trans('admin.Hide')}}</option>
                              </select>
                           </div>                   
                                <div class="form-group col-lg-3">
                              <label class="form-label" for="">{{trans('admin.Residual')}}</label>
                              <select class="select2 form-control w-100" name="Purch_Residual"  required>
                              <option value="1" @if($CustomPrint->Purch_Residual == 1) selected @endif>{{trans('admin.Show')}} </option>
                              <option value="0" @if( $CustomPrint->Purch_Residual == 0) selected @endif>{{trans('admin.Hide')}}</option>
                              </select>
                           </div>                   
                                <div class="form-group col-lg-3">
                              <label class="form-label" for="">{{trans('admin.Taknet')}}</label>
                              <select class="select2 form-control w-100" name="Purch_Taknet"  required>
                              <option value="1" @if($CustomPrint->Purch_Taknet == 1) selected @endif>{{trans('admin.Show')}} </option>
                              <option value="0" @if( $CustomPrint->Purch_Taknet == 0) selected @endif>{{trans('admin.Hide')}}</option>
                              </select>
                           </div>                   
                                <div class="form-group col-lg-3">
                              <label class="form-label" for="">{{trans('admin.Credit')}}</label>
                              <select class="select2 form-control w-100" name="Purch_Credit"  required>
                              <option value="1" @if($CustomPrint->Purch_Credit == 1) selected @endif>{{trans('admin.Show')}} </option>
                              <option value="0" @if( $CustomPrint->Purch_Credit == 0) selected @endif>{{trans('admin.Hide')}}</option>
                              </select>
                           </div>                   
                                <div class="form-group col-lg-3">
                              <label class="form-label" for="">{{trans('admin.Barcode')}}</label>
                              <select class="select2 form-control w-100" name="Purch_Barcode"  required>
                              <option value="1" @if($CustomPrint->Purch_Barcode == 1) selected @endif>{{trans('admin.Show')}} </option>
                              <option value="0" @if( $CustomPrint->Purch_Barcode == 0) selected @endif>{{trans('admin.Hide')}}</option>
                              </select>
                           </div>                   
                                <div class="form-group col-lg-3">
                              <label class="form-label" for="">{{trans('admin.Text')}}</label>
                              <select class="select2 form-control w-100" name="Purch_Text"  required>
                              <option value="1" @if($CustomPrint->Purch_Text == 1) selected @endif>{{trans('admin.Show')}} </option>
                              <option value="0" @if( $CustomPrint->Purch_Text == 0) selected @endif>{{trans('admin.Hide')}}</option>
                              </select>
                           </div>                   
                                <div class="form-group col-lg-12">
                              <label class="form-label" for="">{{trans('admin.Seal')}}</label>
                              <select class="select2 form-control w-100" name="Purch_Seal"  required>
                              <option value="1" @if($CustomPrint->Purch_Seal == 1) selected @endif>{{trans('admin.Show')}} </option>
                              <option value="0" @if( $CustomPrint->Purch_Seal == 0) selected @endif>{{trans('admin.Hide')}}</option>
                              </select>
                           </div>         
                                       
         
                                     
                                     
                                 </div>
                                 <div class="form-row mt-2">
                                    <button type="submit" class="btn btn-primary">{{trans('admin.Save')}}</button>
                                 </div>
                              </div>
                           </div>
                        </form>
                        @else
                        <div  class="form-row">
                           <div class="col-md-12">
                              <div class="data-def">
                                 <div class="form-row">
                                    <div class="form-group col-md-12">
                                       <a class="btn btn-primary" href="{{url('AddDefaultCustomPrintFirst')}}">
                                       {{trans('admin.AddNew')}}
                                       </a>   
                                    </div>
                                 </div>
                              </div>
                           </div>
                        </div>
                        @endif            
                     </div>
      
                      
                  </div>
               </div>
            </div>
         </div>
      </div>
   </div>
</main>

<style>
    .suppp{
            font-size: 20px;
    border: 1px solid darkorchid;
    padding: 7px;
    background: darkseagreen;
        
    }
    
    .head{
            font-size: 30px;
    border: 1px solid darkorange;
    padding: 7px;
    background: antiquewhite;
    }
</style>
@endsection
@push('js')
<link rel="stylesheet" media="screen, print" href="{{asset('Admin/css/datagrid/datatables/datatables.bundle.css')}}">
<link rel="stylesheet" media="screen, print" href="{{asset('Admin/css/formplugins/summernote/summernote.css')}}">
<link rel="stylesheet" media="screen, print" href="{{asset('Admin/css/formplugins/select2/select2.bundle.css')}}">
<script src="{{asset('Admin/js/datagrid/datatables/datatables.bundle.js')}}"></script>
<script src="{{asset('Admin/js/datagrid/datatables/datatables.export.js')}}"></script>
<script src="{{asset('Admin/js/formplugins/summernote/summernote.js')}}"></script>
<script src="{{asset('Admin/js/formplugins/select2/select2.bundle.js')}}"></script>
<script>
   //_fnFeatureHtmlLength();
   $(document).ready(function () {
       // Setup - add a text input to each footer cell
       $('#dt-basic-example thead tr').clone(true).appendTo('#dt-basic-example thead');
       $('#dt-basic-example thead tr:eq(1) th').each(function (i) {
           var title = $(this).text();
           $(this).html('<input type="text" class="form-control form-control-sm" placeholder="Search ' + title + '" />');
   
           $('input', this).on('keyup change', function () {
               if (table.column(i).search() !== this.value) {
                   table
                       .column(i)
                       .search(this.value)
                       .draw();
               }
           });
       });
       var table = $('#dt-basic-example').DataTable(
           {
               responsive: true,
               orderCellsTop: true,
               fixedHeader: true,
               lengthChange: true,
   
               dom: "<'row mb-3'<'col-sm-12 col-md-3 d-flex align-items-center justify-content-start'f><'col-sm-12 col-md-9 d-flex align-items-center justify-content-end'B>>" +
                   "<'row'<'col-sm-12'tr>>" +
                   "<'row'<'col-sm-12 col-md-5'i><'col-sm-12 col-md-7'p>>",
   
               buttons: [
                   {
                       extend: 'pageLength',
                       className: 'btn-outline-default'
                   },
                   {
                       extend: 'colvis',
                       text: 'Column Visibility',
                       titleAttr: 'Col visibility',
                       className: 'btn-outline-default'
                   },
                   {
                       extend: 'pdfHtml5',
                       text: 'PDF',
                       titleAttr: 'Generate PDF',
                       className: 'btn-outline-danger btn-sm mr-1'
                   },
                   {
                       extend: 'excelHtml5',
                       text: 'Excel',
                       titleAttr: 'Generate Excel',
                       className: 'btn-outline-success btn-sm mr-1'
                   },
                   {
                       extend: 'csvHtml5',
                       text: 'CSV',
                       titleAttr: 'Generate CSV',
                       className: 'btn-outline-primary btn-sm mr-1'
                   },
                   {
                       extend: 'copyHtml5',
                       text: 'Copy',
                       titleAttr: 'Copy to clipboard',
                       className: 'btn-outline-primary btn-sm mr-1'
                   },
                   {
                       extend: 'print',
                       text: 'Print',
                       titleAttr: 'Print Table',
                       className: 'btn-outline-primary btn-sm'
                   }
               ],
           });
       $('.js-thead-colors a').on('click', function () {
           var theadColor = $(this).attr("data-bg");
           console.log(theadColor);
           $('#dt-basic-example thead').removeClassPrefix('bg-').addClass(theadColor);
       });
   
       $('.js-tbody-colors a').on('click', function () {
           var theadColor = $(this).attr("data-bg");
           console.log(theadColor);
           $('#dt-basic-example').removeClassPrefix('bg-').addClass(theadColor);
       });
   
   });
   
</script>
<!-- Search Selecet -->
<script>
   $(document).ready(function()
   {
       $(function()
       {
           $('.select2').select2();
   
           $(".select2-placeholder-multiple").select2(
           {
               placeholder: "Select State"
           });
           $(".js-hide-search").select2(
           {
               minimumResultsForSearch: 1 / 0
           });
           $(".js-max-length").select2(
           {
               maximumSelectionLength: 2,
               placeholder: "Select maximum 2 items"
           });
           $(".select2-placeholder").select2(
           {
               placeholder: "Select a state",
               allowClear: true
           });
   
           $(".js-select2-icons").select2(
           {
               minimumResultsForSearch: 1 / 0,
               templateResult: icon,
               templateSelection: icon,
               escapeMarkup: function(elm)
               {
                   return elm
               }
           });
   
           function icon(elm)
           {
               elm.element;
               return elm.id ? "<i class='" + $(elm.element).data("icon") + " mr-2'></i>" + elm.text : elm.text
           }
   
   
   $('#AccountDificit').select2({
   placeholder: "select...",
   ajax: {
   type: "GET",
   dataType: 'json',
   url: 'AllSubAccounts',
   processResults: function (data) {
     return {
       results: $.map(data, function(obj, index) {
         return { id: index, text: obj };
       })
     };
       
       	console.log(data);
       
   },
   data: function (params) {  
     var query = {
       search: params.term
     };
     if (params.term == "*") query.items = [];
     return { json: JSON.stringify( query ) }
   }
   }
   });
   
   
   $('#AccountDificit').on('select2:select', function (e) {
   console.log("select done", e.params.data);
   });
      
           
   $('#AccountExcess').select2({
   placeholder: "select...",
   ajax: {
   type: "GET",
   dataType: 'json',
   url: 'AllSubAccounts',
   processResults: function (data) {
     return {
       results: $.map(data, function(obj, index) {
         return { id: index, text: obj };
       })
     };
       
       	console.log(data);
       
   },
   data: function (params) {  
     var query = {
       search: params.term
     };
     if (params.term == "*") query.items = [];
     return { json: JSON.stringify( query ) }
   }
   }
   });
   
   
   $('#AccountExcess').on('select2:select', function (e) {
   console.log("select done", e.params.data);
   });
                      
   
           
         $("#client").select2({
           placeholder: "select...",
           ajax: {
               type: "GET",
               dataType: "json",
               url: "AllClientsFilter",
               processResults: function (data) {
                   return {
                       results: $.map(data, function (obj, index) {
   
                           return { id: index, text: obj };
                       }),
                   };
   
                   console.log(data);
               },
             data: function (params) {  
   
       
              var query = {
                       search: params.term,
                   };
                           
       
         $.ajax({
                         url: 'AllClientsFilterJS/'+params.term,
                         type:"GET",
                         dataType:"json",
                         beforeSend: function(){
                             $('#loader').css("visibility", "visible");
                         },
   
                         success:function(data) {
                                     $('#client').empty();  
                             $.each(data, function(key, value){
   
                    $('#client').append('<option value="'+ key +'">' + value + '</option>');
              
                             });
                         },
                         complete: function(){
                             $('#loader').css("visibility", "hidden");
                         }
                     });
       
       
       
   }
           },
       });
   
       $("#client").on("select2:select", function (e) {
           console.log("select done", e.params.data);
       });
       
                   
           
       });
   });
</script>
<!-- Filter Governrate and City !-->
<script>
   $(document).ready(function() {
   
       $('#Governrate').on('change', function(){
           var countryId = $(this).val();
           if(countryId) {
               $.ajax({
                   url: 'GovernrateFilter/'+countryId,
                   type:"GET",
                   dataType:"json",
                   beforeSend: function(){
                       $('#loader').css("visibility", "visible");
                   },
   
                   success:function(data) {
   
                       $('#City').empty();
   
                       $.each(data, function(key, value){
   
             $('#City').append('<option value="'+ key +'">' + value + '</option>');
                           
                       });
                   },
                   complete: function(){
                       $('#loader').css("visibility", "hidden");
                   }
               });
           } else {
   
               $('select[name="states"]').empty();
           }
   
       });
   
   });
</script>

<!-- Filter Governrate and City Company Data !-->
<script>
   $(document).ready(function() {
   
       $('.Governrate').on('change', function(){
           var countryId = $(this).val();
           if(countryId) {
               $.ajax({
                   url: 'GovernrateFilter/'+countryId,
                   type:"GET",
                   dataType:"json",
                   beforeSend: function(){
                       $('#loader').css("visibility", "visible");
                   },
   
                   success:function(data) {
   
                       $('.City').empty();
   
                       $.each(data, function(key, value){
   
             $('.City').append('<option value="'+ key +'">' + value + '</option>');
                           
                       });
                       
                      var CIITY = $('.City').val();  
                         $.ajax({
                   url: 'CityFilter/'+CIITY,
                   type:"GET",
                   dataType:"json",
                   beforeSend: function(){
                       $('#loader').css("visibility", "visible");
                   },
   
                   success:function(data) {
   
                       $('.Place').empty();
   
                       $.each(data, function(key, value){
   
             $('.Place').append('<option value="'+ key +'">' + value + '</option>');
                           
                       });
                       
       
                   },
                   complete: function(){
                       $('#loader').css("visibility", "hidden");
                   }
               });
                       
                               
                       
       
                       
                   },
                   complete: function(){
                       $('#loader').css("visibility", "hidden");
                   }
               });
           } else {
   
               $('select[name="states"]').empty();
           }
   
       });
   
   });

    
 $('.City').on('change', function(){
      var CIITY = $('.City').val();  
                         $.ajax({
                   url: 'CityFilter/'+CIITY,
                   type:"GET",
                   dataType:"json",
                   beforeSend: function(){
                       $('#loader').css("visibility", "visible");
                   },
   
                   success:function(data) {
   
                       $('.Place').empty();
   
                       $.each(data, function(key, value){
   
             $('.Place').append('<option value="'+ key +'">' + value + '</option>');
                           
                       });
                       
            
                       
       
                   },
                   complete: function(){
                       $('#loader').css("visibility", "hidden");
                   }
               });
                       
     
 });
    
</script>

<!-- Filter Platform and Campaigns !-->
<script>
   $(document).ready(function() {
   
       $('#Platform').on('change', function(){
           var countryId = $(this).val();
           if(countryId) {
               $.ajax({
                   url: 'PlatformFilter/'+countryId,
                   type:"GET",
                   dataType:"json",
                   beforeSend: function(){
                       $('#loader').css("visibility", "visible");
                   },
   
                   success:function(data) {
   
                       $('#Campagin').empty();
   
                       $.each(data, function(key, value){
   
             $('#Campagin').append('<option value="'+ key +'">' + value + '</option>');
                           
                       });
                   },
                   complete: function(){
                       $('#loader').css("visibility", "hidden");
                   }
               });
           } else {
   
               $('select[name="states"]').empty();
           }
   
       });
   
   });
</script>
<script src="js/formplugins/summernote/summernote.js"></script>
<script>
   var autoSave = $('#autoSave');
   var interval;
   var timer = function()
   {
       interval = setInterval(function()
       {
           //start slide...
           if (autoSave.prop('checked'))
               saveToLocal();
   
           clearInterval(interval);
       }, 3000);
   };
   
   //save
   var saveToLocal = function()
   {
       localStorage.setItem('summernoteData', $('#saveToLocal').summernote("code"));
       console.log("saved");
   }
   
   //delete 
   var removeFromLocal = function()
   {
       localStorage.removeItem("summernoteData");
       $('#saveToLocal').summernote('reset');
   }
   
   $(document).ready(function()
   {
       //init default
       $('.js-summernote').summernote(
       {
           height: 200,
           tabsize: 2,
           placeholder: "Type here...",
           dialogsFade: true,
           toolbar: [
               ['style', ['style']],
               ['font', ['strikethrough', 'superscript', 'subscript']],
               ['font', ['bold', 'italic', 'underline', 'clear']],
               ['fontsize', ['fontsize']],
               ['fontname', ['fontname']],
               ['color', ['color']],
               ['para', ['ul', 'ol', 'paragraph']],
               ['height', ['height']]
               ['table', ['table']],
               ['insert', ['link', 'picture', 'video']],
               ['view', ['fullscreen', 'codeview', 'help']]
           ],
           callbacks:
           {
               //restore from localStorage
               onInit: function(e)
               {
                   $('.js-summernote').summernote("code", localStorage.getItem("summernoteData"));
               },
               onChange: function(contents, $editable)
               {
                   clearInterval(interval);
                   timer();
               }
           }
       });
   
       //load emojis
       $.ajax(
       {
           url: 'https://api.github.com/emojis',
           async: false
       }).then(function(data)
       {
           window.emojis = Object.keys(data);
           window.emojiUrls = data;
       });
   
       //init emoji example
       $(".js-hint2emoji").summernote(
       {
           height: 100,
           toolbar: false,
           placeholder: 'type starting with : and any alphabet',
           hint:
           {
               match: /:([\-+\w]+)$/,
               search: function(keyword, callback)
               {
                   callback($.grep(emojis, function(item)
                   {
                       return item.indexOf(keyword) === 0;
                   }));
               },
               template: function(item)
               {
                   var content = emojiUrls[item];
                   return '<img src="' + content + '" width="20" /> :' + item + ':';
               },
               content: function(item)
               {
                   var url = emojiUrls[item];
                   if (url)
                   {
                       return $('<img />').attr('src', url).css('width', 20)[0];
                   }
                   return '';
               }
           }
       });
   
       //init mentions example
       $(".js-hint2mention").summernote(
       {
           height: 100,
           toolbar: false,
           placeholder: "type starting with @",
           hint:
           {
               mentions: ['jayden', 'sam', 'alvin', 'david'],
               match: /\B@(\w*)$/,
               search: function(keyword, callback)
               {
                   callback($.grep(this.mentions, function(item)
                   {
                       return item.indexOf(keyword) == 0;
                   }));
               },
               content: function(item)
               {
                   return '@' + item;
               }
           }
       });
   
   });
   
</script>
<!-- Filter Manufactuer Company and Device Type !-->
<script>
   $(document).ready(function() {
   
       $('#Company').on('change', function(){
           var countryId = $(this).val();
           if(countryId) {
               $.ajax({
                   url: 'CompanyManufactureFilter/'+countryId,
                   type:"GET",
                   dataType:"json",
                   beforeSend: function(){
                       $('#loader').css("visibility", "visible");
                   },
   
                   success:function(data) {
   
                       $('#Device_Type').empty();
   
                       $.each(data, function(key, value){
   
             $('#Device_Type').append('<option value="'+ key +'">' + value + '</option>');
                           
                       });
                   },
                   complete: function(){
                       $('#loader').css("visibility", "hidden");
                   }
               });
           } else {
   
               $('select[name="states"]').empty();
           }
   
       });
   
   });
</script>


<!-- Hall Service -->
<script>
 function HallService(){
     var Hall_Service_Type=$('#Hall_Service_Type').val();
     
     if(parseFloat(Hall_Service_Type) == 1){
        
         document.getElementById('Hall_Service_Precent').style.display='block';
        }else{
        document.getElementById('Hall_Service_Precent').style.display='none';  
        }
     
     
 }
    
    
     $(document).ready(function() {
         
           var Hall_Service_Type=$('#Hall_Service_Type').val();
     
     if(parseFloat(Hall_Service_Type) == 1){
        
         document.getElementById('Hall_Service_Precent').style.display='block';
        }else{
        document.getElementById('Hall_Service_Precent').style.display='none';  
        }   
         
         
     });


</script>


@endpush