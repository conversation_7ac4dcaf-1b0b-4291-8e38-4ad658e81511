module.exports={A:{D:{"1":"5 6 7 8 9 iB jB kB lB mB nB oB pB qB rB sB JC tB KC uB vB wB xB yB zB 0B 1B 2B 3B 4B 5B 6B 7B 8B 9B AC Q H R S T U V W X Y Z a b c d e f g h i j k l m n o p q r s t u v w x AB BB CB DB EB FB GB HB IB JB KB I BC MC NC","2":"0 1 2 3 4 J LB K D E F A B C L M G N O P MB y z NB OB PB QB RB SB TB UB VB WB XB YB ZB aB bB cB dB eB fB gB hB"},L:{"1":"I"},B:{"1":"5 6 7 8 9 Q H R S T U V W X Y Z a b c d e f g h i j k l m n o p q r s t u v w x AB BB CB DB EB FB GB HB IB JB KB I","2":"C L M G N O P"},C:{"1":"5 6 7 8 9 kB lB mB nB oB pB qB rB sB JC tB KC uB vB wB xB yB zB 0B 1B 2B 3B 4B 5B 6B 7B 8B 9B AC Q H R LC S T U V W X Y Z a b c d e f g h i j k l m n o p q r s t u v w x AB BB CB DB EB FB GB HB IB JB KB I BC MC NC iC","2":"hC IC J LB K D E F A B C L M G N jC kC","33":"0 1 2 3 4 O P MB y z NB OB PB QB RB SB TB UB VB WB XB YB ZB aB bB cB dB eB fB gB hB iB jB"},M:{"1":"BC"},A:{"2":"K D E F A B gC"},F:{"1":"VB WB XB YB ZB aB bB cB dB eB fB gB hB iB jB kB lB mB nB oB pB qB rB sB tB uB vB wB xB yB zB 0B 1B 2B 3B 4B 5B 6B 7B 8B 9B AC Q H R LC S T U V W X Y Z a b c d e f g h i j k l m n o p q r s t u v w x","2":"0 1 2 3 4 F B C G N O P MB y z NB OB PB QB RB SB TB UB xC yC zC 0C CC eC 1C DC"},K:{"1":"H","2":"A B C CC eC DC"},E:{"1":"B C L M G CC DC qC rC sC QC RC EC tC FC SC TC UC VC WC uC GC XC YC ZC aC bC vC HC cC dC","2":"J LB K lC OC mC nC wC","33":"D E F A oC pC PC"},G:{"1":"BD CD DD ED FD GD HD ID JD KD LD QC RC EC MD FC SC TC UC VC WC ND GC XC YC ZC aC bC OD HC cC dC","2":"OC 2C fC 3C 4C","33":"E 5C 6C 7C 8C 9C AD"},P:{"1":"0 1 2 3 4 y z WD XD YD ZD aD PC bD cD dD eD fD FC GC HC gD","2":"J"},I:{"1":"I","2":"IC J QD RD SD TD fC UD VD"}},B:6,C:"isolate-override from unicode-bidi",D:undefined};
