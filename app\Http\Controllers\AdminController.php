<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Admin;
use App\Models\UsersMoves;
use App\Models\AccountsDefaultData;
use App\Models\CrmDefaultData;
use App\Models\StoresDefaultData;
use App\Models\PurchasesDefaultData;
use App\Models\SalesDefaultData;
use App\Models\CompanyData;
use App\Models\Coins;
use App\Models\Measuerments;
use App\Models\Taxes;
use App\Models\ItemsGroups;
use App\Models\Stores;
use App\Models\QR;
use App\Models\ProductTypeDefault;
use App\Models\Governrate;
use App\Models\CostCenter;
use App\Models\City;
use App\Models\ShippingDefault;
use App\Models\ReportsSettings;
use App\Models\ShippingType;
use App\Models\ShippingStatus;
use App\Models\Activites;
use App\Models\ClientStatus;
use App\Models\Platforms;
use App\Models\Campaigns;
use App\Models\Employess;
use App\Models\Vendors;
use App\Models\Modules;
use App\Models\ShippingCompany;
use App\Models\Customers;
use App\Models\AcccountingManual;
use App\Models\ShowPrintDefault;
use App\Models\BarcodeSettings;
use App\Models\DefaultDataShowHide;
use App\Models\MaintainceDefaultData;
use App\Models\ManufactureCompany;
use App\Models\DesviceCases;
use App\Models\Products;
use App\Models\ManufacturingHalls;
use App\Models\ProductUnits;
use App\Models\Brands;
use App\Models\Countris;
use App\Models\ModuleSettingsNum;
use App\Models\Transltor;
use App\Models\Tenant;
use App\Models\ManufacturingDefaultData;
use DB ;
use Str ;
use App\Mail\AdminResetPassword;
use Carbon\Carbon;
use Mail;
use Auth;
use URL;
use SpamProtector;
use Storage;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;
use App\Models\Quote;
use App\Models\ProductsQty;
use App\Models\ProductsQuote;
use App\Models\SalesOrder;
use App\Models\Sales;
use App\Models\ProductSalesOrder;
use App\Models\ProductSales;
use App\Models\StoreCountSales;
use App\Models\Installment;
use App\Models\InstallmentDates;
use App\Models\ProductMoves;
use App\Models\Journalizing;
use App\Models\JournalizingDetails;
use App\Models\GeneralDaily;
use App\Models\RecivedSales;
use App\Models\RecivedSalesProducts;
use App\Models\ReturnSales;
use App\Models\ReturnSalesProducts;
use App\Models\IncomChecks;
use App\Models\ProductsPurchases;
use App\Models\ProductsStartPeriods;
use App\Models\ProductsStoresTransfers;
use App\Models\Shifts;
use App\Models\QuoteImage;
use App\Models\ProductQuoteImage;
use App\Models\PaymentVoucher;
use App\Models\PaymentVoucherDetails;
use App\Models\ReciptVoucher;
use App\Models\ReciptVoucherDetails;
use App\Models\SalesSubscribes;
use App\Models\ProductSalesSubscribes;
use App\Models\StoresMoves;
use App\Models\StartPeriods;
use App\Models\Inventory;
use App\Models\ProductInventory;
use App\Models\Settlement;
use App\Models\ProductSettlement;
use App\Models\StorsTransfers;
use App\Models\Consists;
use App\Models\ProductsConsists;
use App\Models\PurchasesOrder;
use App\Models\ProductsPurchasesOrder;
use App\Models\Purchases;
use App\Models\Shortcomings;
use App\Models\ProductsShortcomings;
use App\Models\ReciptMaintaince;
use App\Models\ProductMaintaincBill;
use App\Models\MaintaincBill;
use App\Models\ReturnMaintainceBill;
use App\Models\ProductsReturnMaintainceBill;
use App\Models\ManufacturingModel;
use App\Models\IncomManufacturingModel;
use App\Models\OutcomManufacturingModel;
use App\Models\ExecutingReceiving;
use App\Models\ManuStoreCount;
use App\Models\ManufacturingOrder;
use App\Models\ExaminationsTypes;
use App\Models\ProductsManufacturingOrder;
use App\Models\ProductsExecutingReceiving;
use App\Models\ManufacturingRequest;
use App\Models\ProductsManufacturingRequest;
use App\Models\ManufacturingExecution;
use App\Models\Quality;
use App\Models\QualityDetails;
use App\Models\ProductManufacturingExecution;
use App\Models\ReciptsType;
use App\Models\BonesType;
use App\Models\CompanyCars;
use App\Models\CountersType;
use App\Models\PurchasePetrol;
use App\Models\ProductsPurchasePetrol;
use App\Models\ReciptsSalesPetrol;
use App\Models\ClientSalesPetrol;
use App\Models\CarsSalesPetrol;
use App\Models\BonesSalesPetrol;
use App\Models\WorkersSalesPetrol;
use App\Models\SalesPetrol;
use App\Models\SecretariatStores;
use App\Models\SecretariatQty;
use App\Models\SecretariatImportGoods;
use App\Models\ProductsSecretariatImportGoods;
use App\Models\SecretariatExportGoods;
use App\Models\ProductsSecretariatExportGoods;
use App\Models\ShippingOrder;
use App\Models\Rooms;
use App\Models\RoomsType;
use App\Models\Reservations;
use App\Models\RoomReservations;
use App\Models\Interviews;
use App\Models\Projects;
use App\Models\ProjectTeam;
use App\Models\Missions;
use App\Models\OpeningEntries;
use App\Models\OpeningEntriesDetails;
use App\Models\AssetsExpenses;
use App\Models\Assets;
use App\Models\InsurancePaper;
use App\Models\AssemblyProducts;
use App\Models\LoginSlider;
use App\Models\PhpSerial;
use App\Models\ResturantDefaultData;
use App\Models\ReturnPurch;
use App\Models\ExportChecks;
use App\Models\Packages;
use App\Models\PackPrem;
use App\Models\Event;
use App\Models\Notifications;
use App\Models\CustomersGroup;
use App\Models\Intro;
use App\Models\ChatIssue;
use App\Models\Issues;
use App\Models\RabihEducation;
use App\Models\CustomPrint;
use Artisan;
use Calendar;


class AdminController extends Controller
{

function __construct()
{

$this->middleware('permission:المستخدمين', ['only' => ['AdminsPage','AddAdmin','EditAdmin','DeleteAdmin']]);
$this->middleware('permission:الترجمه', ['only' => ['TranslatePage']]);
$this->middleware('permission:البيانات الافتراضيه', ['only' => ['Default_DataPage','AddDefaultCompanyFirst','AddDefaultCompany','AddDefaultAccountsFirst','AddDefaultAccount','AddDefaultStoreFirst','AddDefaultStore','AddDefaultCrmFirst','AddDefaultCrm','AddDefaultPurchasesFirst','AddDefaultPurchases','AddDefaultSalesFirst','AddDefaultSales']]);
$this->middleware('permission:حذف الحركات', ['only' => ['DeleteMoves']]);

}

         public function LoginPage(Request $request){

          //   $Admin=Admin::all();


         //    if(count($Admin) == 0){
                         //      $host = $request->getHost();

               //  Artisan::call('tenants:seed', [

 //   '--class' => 'DatabaseSeeder'
                //    ]);

         //    }


        return view('admin.Login');

    }

         public function Login(){

        $data= $this->validate(request(),[
             'email'=>'required|email',
             'password'=>'required|min:6|max:50',
               ],[
         ]);

      $rememberme = request('rememberme') == 1?true:false;

       if(auth()->guard('admin')->attempt(['email'=>request('email'),'password'=>request('password')],$rememberme)){

           $data['User']=auth()->guard('admin')->user()->id;
           $data['Date']=date('Y-m-d');
           $data['Time']=date("h:i:s a", time());
           $data['Screen']='تسجيل الدخول';
           $data['ScreenEn']='Login';
           $data['Type']='تسجيل الدخول';
           $data['TypeEn']='Login';
           $data['Explain']='تسجيل الدخول';
           $data['ExplainEn']='Login';

           UsersMoves::create($data);

          return redirect('OstAdmin');

       }else{
          session()->flash('error',trans('admin.incorrect_information_login'));
       	 return redirect('AdminLogin');
       }



    }

         public function Logout(){
    auth()->guard('admin')->logout();
	return redirect('AdminLogin');

 }

         public function forgotpasswordPage(){
        return view('admin.forgotpassword');

    }

         public function forgotpassword(){

                if(SpamProtector::isSpamEmail(request('email')))
                {
  return back();
}


                  $data= $this->validate(request(),[
             'email'=>'required|email',
               ],[
         ]);

         $admin = Admin::where('email',request('email'))->first();
               if(!empty($admin)){

                   $token = app('auth.password.broker')->createToken($admin);
                   $data = DB::table('password_resets')->insert([
                   	  'email'  => $admin->email,
                   	  'token'  => $token,
                   	  'created_at'  => Carbon::now(),

                   ]);




                   Mail::to($admin->email)->send(new AdminResetPassword(['data'=>$admin,'token'=>$token]));
                   session()->flash('success',trans('admin.Reset_Password'));
                     return back();

               }else{

                session()->flash('success',trans('admin.WrongEmail'));


               }

               return back();

        }

         public function reset_password($token){


             $check_token = DB::table('password_resets')->where('token',$token)->where('created_at','>',Carbon::now()->subHours(2) ) ->first();

              if(!empty($check_token)){
             return view('admin.reset_password',['data'=>$check_token]);

             }else{

             	return redirect('AdminLogin');
             }
             }

         public function reset_password_final($token){

                  $this->validate(request(),[
                  'password' => 'required|confirmed',
                  'password_confirmation' => 'required',
                      ],[
                        'password.required' => trans('admin.passwordRequired'),
                        'password_confirmation.required' => trans('admin.password_confirmationRequired'),

                  ]);
                  $check_token = DB::table('password_resets')->where('token',$token)->where('created_at','>',Carbon::now()->subHours(2) ) ->first();
                   if(!empty($check_token)){
                   	$admin = Admin::where('email', $check_token->email)->update([
                   		'email' => $check_token->email,
                   		'password' =>bcrypt(request('password'))

                   	]);
                  DB::table('password_resets')->where('email',request('email'))->delete();
                auth()->guard('admin')->attempt(['email'=>$check_token->email,'password'=>request('password')],true);
                 return redirect('OstAdmin');

             }else{

             	return redirect('AdminLogin');
             }

               }

//DeleteBackup
     public function DeleteBackup(){

       Storage::delete(request('X'));
        session()->flash('error',trans('admin.Deleted'));
        return back();

           }


    //Admin

        public function AdminsPage(){

            $superAdminEmails = ['<EMAIL>', '<EMAIL>'];
            if(!in_array(auth()->guard('admin')->user()->email, $superAdminEmails)){
        $items=Admin::where('hidden',0)->get();
            }else{

          $items=Admin::get();

            }



               $Stores=Stores::all();
               $Packages=Packages::all();

            $Safes = AcccountingManual::
             where('Type',1)
              ->where('Parent',28)
              ->orWhere('Parent',29)
              ->get();

             $superAdminEmails = ['<EMAIL>', '<EMAIL>'];
             if(!in_array(auth()->guard('admin')->user()->email, $superAdminEmails)){

          if(app()->getLocale() == 'ar' ){
         $roles = Role::where('name','!=','Owner')->pluck('name','name');
            }else{

              $roles = Role::where('name','!=','Owner')->pluck('nameEn','name');
          }
             }else{

            if(app()->getLocale() == 'ar' ){
         $roles = Role::pluck('name','name')->all();
            }else{

              $roles = Role::pluck('nameEn','name')->all();
            }

        }


         return view('admin.Admins',['items'=>$items,'Stores'=>$Stores,'Safes'=>$Safes,'roles'=>$roles,'Packages'=>$Packages]);
    }

         public function ReSubscribtion(){

         return view('ReSubscribtion');
    }

     public function AddAdmin(){




                         $Module=ModuleSettingsNum::orderBy('id','desc')->first();
         if($Module->Users_Select == 1){

             $count=Admin::where('hidden',0)->count();

             if($Module->Users_Num <= $count){

             session()->flash('error',trans('admin.Alert_Maximum_Add'));
             return back();
             }


         }

        $data= $this->validate(request(),[
             'name'=>'required',
             'email'=>'required|email|unique:admins',
             'password'=>'required|min:6',
             'roles_name' => 'required',
             'image'=>'image|mimes:jpeg,png,jpg|max:2048',
               ],[
            'name.required' => trans('admin.nameRequired'),
            'email.required' => trans('admin.emailRequired'),
            'email.email' =>trans('admin.emailEmail'),
            'email.unique' =>trans('admin.emailUnique'),
            'password.required' =>trans('admin.passwordRequired'),
            'password.min' => trans('admin.passwordmin_6'),

         ]);

          $image=request()->file('image');
          if($image){
            $image_name=Str::random(20);
            $ext=strtolower($image->getClientOriginalExtension());
            $image_full_name=$image_name .'.' . $ext ;
            $upload_path='AdminsImages/';
            $image_url=$upload_path.$image_full_name;
            $success=$image->move($upload_path,$image_full_name);
                   }


             if(!empty($image_url)){

                 $data['image']=$image_url;

             }else{
                 $data['image']=null;
             }

         if(request('Type') == 'Admin'){
          $data['emp']=0;
          $data['ship']=0;
          $data['vend']=0;
          $data['cli']=0;
          $data['account']=0;

         }elseif(request('Type') == 'Emp'){

         $data['emp']=request('emp');
         $data['ship']=0;
         $data['vend']=0;
         $data['cli']=0;
         $cust=Employess::find(request('emp'));
            $data['account']=$cust->Account;


         }elseif(request('Type') == 'Ship'){

          $data['emp']=0;
         $data['ship']=request('ship');
         $data['vend']=0;
         $data['cli']=0;

           $cust=ShippingCompany::find(request('ship'));
            $data['account']=$cust->Account;

         }elseif(request('Type') == 'Vend'){

                   $data['emp']=0;
         $data['ship']=0;
         $data['vend']=request('vend');
         $data['cli']=0;

         $cust=Vendors::find(request('vend'));
            $data['account']=$cust->Account;


         }elseif(request('Type') == 'Client'){

         $data['emp']=0;
         $data['ship']=0;
         $data['vend']=0;
         $data['cli']=request('cli');

         $cust=Customers::find(request('cli'));
            $data['account']=$cust->Account;

         }


         $data['name']=request('name');


            if(!empty(request('nameEn'))){
         $data['nameEn']=request('nameEn');
          }else{
        $data['nameEn']=request('name');
          }
         $data['email']=request('email');
         $data['phone']=request('phone');
         $data['password']=bcrypt(request('password'));
         $data['hidden']=0;
         $data['status']=0;
         $data['safe']=request('safe');
         $data['store']=request('store');
         $data['type']=request('Type');
         $data['roles_name']=request('roles_name');
         $data['price_sale']=request('price_sale');
         $data['discount']=request('discount');
         $data['price_1']=request('price_1');
         $data['price_2']=request('price_2');
         $data['price_3']=request('price_3');
         $data['pos_pay']=request('pos_pay');
         $data['executor']=request('executor');
         $data['cost_price']=request('cost_price');
         $data['pos_stores']=request('pos_stores');
         $data['pos_hold']=request('pos_hold');
         $data['cost_price_purch']=request('cost_price_purch');
         $data['cost_price_sales']=request('cost_price_sales');
         $data['manu_order_precent']=request('manu_order_precent');
         $data['pos_product']=request('pos_product');
         $data['Cash']=request('Cash');
         $data['Later']=request('Later');
         $data['Check']=request('Check');
         $data['Installment']=request('Installment');
         $data['Cash_Visa']=request('Cash_Visa');
         $data['Cash_Collection']=request('Cash_Collection');
         $data['Delivery']=request('Delivery');
         $data['Date']=request('Date');
         $data['ticket_price']=request('ticket_price');
         $data['ticket_discount']=request('ticket_discount');
         $data['InstallmentCompanies']=request('InstallmentCompanies');
         $data['package']=request('package');
         $data['job_order_price']=request('job_order_price');
         Admin::create($data);

         $user=Admin::orderBy('id','desc')->first();

         $user->assignRole(request('roles_name'));


             session()->flash('success',trans('admin.NewAddAdmin'));
             return redirect('Admins');

    }

     public function EditAdmin($id){

            $data= $this->validate(request(),[
             'name'=>'required',
             'email'=>'required|email|unique:admins,email,'.$id,
             'password'=>'sometimes|nullable|min:6',
             'image'=>'sometimes|nullable|image|mimes:jpeg,png,jpg|max:2048',

              'roles_name' => 'required',
                  ],[
            'name.required' => trans('admin.nameRequired'),
            'email.required' => trans('admin.emailRequired'),
            'email.email' =>trans('admin.emailEmail'),
            'email.unique' =>trans('admin.emailUnique'),
            'password.required' =>trans('admin.passwordRequired'),
            'password.min' => trans('admin.passwordmin_6'),


         ]);

           $image=request()->file('image');

          if($image){

            $image_name=Str::random(20);
            $ext=strtolower($image->getClientOriginalExtension());
            $image_full_name=$image_name .'.' . $ext ;
            $upload_path='AdminsImages/';
            $image_url=$upload_path.$image_full_name;
            $success=$image->move($upload_path,$image_full_name);
        }

        if(empty($image_url)){$data['image']=request('images') ; }else{$data['image']=$image_url;}

             if(request()->has('password')){

              $data['password']  =bcrypt(request('password'));

            }

            if(empty (request('password'))){

             $data['password']  = request('passwords');

        }



                  if(request('Type') == 'Admin'){
          $data['emp']=0;
          $data['ship']=0;
          $data['vend']=0;
          $data['cli']=0;
          $data['account']=0;

         }elseif(request('Type') == 'Emp'){

         $data['emp']=request('emp');
         $data['ship']=0;
         $data['vend']=0;
         $data['cli']=0;
         $cust=Employess::find(request('emp'));
            $data['account']=$cust->Account;


         }elseif(request('Type') == 'Ship'){

          $data['emp']=0;
         $data['ship']=request('ship');
         $data['vend']=0;
         $data['cli']=0;

         $cust=ShippingCompany::find(request('ship'));
            $data['account']=$cust->Account;

         }elseif(request('Type') == 'Vend'){

                   $data['emp']=0;
         $data['ship']=0;
         $data['vend']=request('vend');
         $data['cli']=0;

         $cust=Vendors::find(request('vend'));
            $data['account']=$cust->Account;


         }elseif(request('Type') == 'Client'){

         $data['emp']=0;
         $data['ship']=0;
         $data['vend']=0;
         $data['cli']=request('cli');

         $cust=Customers::find(request('cli'));
            $data['account']=$cust->Account;

         }

            $data['nameEn']=request('nameEn');

            $data['phone']=request('phone');
            $data['hidden']=request('hidden');
            $data['status']=request('status');
            $data['safe']=request('safe');
            $data['store']=request('store');
            $data['type']=request('Type');
            $data['price_sale']=request('price_sale');
            $data['discount']=request('discount');
            $data['price_1']=request('price_1');
            $data['price_2']=request('price_2');
            $data['price_3']=request('price_3');
            $data['pos_pay']=request('pos_pay');
            $data['executor']=request('executor');
            $data['cost_price']=request('cost_price');
            $data['pos_stores']=request('pos_stores');
            $data['pos_hold']=request('pos_hold');
            $data['roles_name']=request('roles_name');
            $data['cost_price_purch']=request('cost_price_purch');
            $data['cost_price_sales']=request('cost_price_sales');
            $data['manu_order_precent']=request('manu_order_precent');
           $data['pos_product']=request('pos_product');
           $data['Cash']=request('Cash');
           $data['Later']=request('Later');
           $data['Check']=request('Check');
           $data['Installment']=request('Installment');
           $data['Cash_Visa']=request('Cash_Visa');
           $data['Cash_Collection']=request('Cash_Collection');
           $data['Delivery']=request('Delivery');
           $data['Date']=request('Date');
           $data['ticket_price']=request('ticket_price');
           $data['ticket_discount']=request('ticket_discount');
           $data['InstallmentCompanies']=request('InstallmentCompanies');
           $data['package']=request('package');
           $data['job_order_price']=request('job_order_price');
           Admin::where('id',$id)->update($data);


             $user=Admin::find($id);



        $user->removeRole(request('roles'));
        $user->assignRole(request('roles_name'));

            session()->flash('success',trans('admin.Updated'));
            return back();


     }

     public function DeletePackage($id){

        $del=Packages::find($id);
        $del->delete();
        session()->flash('error',trans('admin.Deleted'));
        return redirect('Admins');

           }
         public function DeleteAdmin($id){

        $del=Admin::find($id);
        $del->delete();
        session()->flash('error',trans('admin.Deleted'));
        return redirect('Admins');

           }

     public function Profile(){
            $item=Admin::find(auth()->guard('admin')->user()->id);
            return view('admin.Profile',['item'=> $item]);

           }

    public function UpdateAdminProfile($id){
         if(SpamProtector::isSpamEmail(request('email')))
{
  return back();
}
            $data= $this->validate(request(),[
             'name'=>'required',
             'email'=>'required|email|unique:admins,email,'.$id,
             'password'=>'sometimes|nullable|min:6',
             'image'=>'sometimes|nullable|image|mimes:jpeg,png,jpg|max:2048',

                  ],[
            'name.required' => trans('admin.nameRequired'),
            'email.required' => trans('admin.emailRequired'),
            'email.email' =>trans('admin.emailEmail'),
            'email.unique' =>trans('admin.emailUnique'),
            'password.required' =>trans('admin.passwordRequired'),
            'password.min' => trans('admin.passwordmin_6'),


         ]);

           $image=request()->file('image');

          if($image){

            $image_name=Str::random(20);
            $ext=strtolower($image->getClientOriginalExtension());
            $image_full_name=$image_name .'.' . $ext ;
            $upload_path='AdminsImages/';
            $image_url=$upload_path.$image_full_name;
            $success=$image->move($upload_path,$image_full_name);
        }

        if(empty($image_url)){$data['image']=request('images') ; }else{$data['image']=$image_url;}

             if(request()->has('password')){

              $data['password']  =bcrypt(request('password'));

            }

            if(empty (request('password'))){

             $data['password']  = request('passwords');

        }



                  if(request('Type') == 'Admin'){
          $data['emp']=0;
          $data['ship']=0;
          $data['vend']=0;
          $data['cli']=0;
          $data['account']=0;

         }elseif(request('Type') == 'Emp'){

         $data['emp']=request('emp');
         $data['ship']=0;
         $data['vend']=0;
         $data['cli']=0;
         $cust=Employess::find(request('emp'));
            $data['account']=$cust->Account;


         }elseif(request('Type') == 'Ship'){

          $data['emp']=0;
         $data['ship']=request('ship');
         $data['vend']=0;
         $data['cli']=0;

         $cust=ShippingCompany::find(request('ship'));
            $data['account']=$cust->Account;

         }elseif(request('Type') == 'Vend'){

                   $data['emp']=0;
         $data['ship']=0;
         $data['vend']=request('vend');
         $data['cli']=0;

         $cust=Vendors::find(request('vend'));
            $data['account']=$cust->Account;


         }elseif(request('Type') == 'Client'){

         $data['emp']=0;
         $data['ship']=0;
         $data['vend']=0;
         $data['cli']=request('cli');

         $cust=Customers::find(request('cli'));
            $data['account']=$cust->Account;

         }

            $data['nameEn']=request('nameEn');
            $data['phone']=request('phone');
            $data['hidden']=request('hidden');
            $data['status']=request('status');
            $data['safe']=request('safe');
            $data['store']=request('store');
            $data['type']=request('Type');
         $data['roles_name']=request('roles_name');


           Admin::where('id',$id)->update($data);


             $user=Admin::find($id);


            session()->flash('success',trans('admin.Updated'));
            return back();


     }

         public function UnHide($id){

        Admin::where('id',$id)->update(['hidden'=>1]);

        session()->flash('success',trans('admin.Updated'));
        return redirect('Admins');

           }

             public function Hide($id){

        Admin::where('id',$id)->update(['hidden'=>0]);

        session()->flash('success',trans('admin.Updated'));
        return redirect('Admins');

           }


    //Calendar
         public function CalendarMeet(){

             $Months=date("Y-m-d", strtotime("-3 Months"));

             if(auth()->guard('admin')->user()->emp == 0){
           $events = Event::where('End_Date','>=',$Months)->get();
             }else{

             $events = Event::where('End_Date','>=',$Months)->where('Emp',auth()->guard('admin')->user()->emp)->get();
             }
     $event_list = [];
     foreach ($events as $key => $event) {
         if(app()->getLocale() == 'ar'){
         $NAme=$event->Event_Ar_Name;
         }else{
         $NAme=$event->Event_En_Name;
         }


         if(!empty($event->Emp)){

                      if(app()->getLocale() == 'ar'){

                          if(!empty($event->Emp()->first()->Name)){
                           $EmpName=$event->Emp()->first()->Name;
                          }else{
                              $EmpName='';
                          }

         }else{
                    if(!empty($event->Emp()->first()->NameEn)){
                           $EmpName=$event->Emp()->first()->NameEn;
                          }else{
                              $EmpName='';
                          }
         }



         }else{

              $EmpName='';

         }


             if(!empty($event->Client)){

                      if(app()->getLocale() == 'ar'){

                                     if(!empty($event->Client()->first()->Name)){
                       $AccName=$event->Client()->first()->Name;
                          }else{
                              $AccName='';
                          }


         }else{
                                 if(!empty($event->Client()->first()->NameEn)){
                       $AccName=$event->Client()->first()->NameEn;
                          }else{
                              $AccName='';
                          }

         }



         }else{

              $AccName='';

         }


                    if(!empty($event->Product)){

                      if(app()->getLocale() == 'ar'){

                                                      if(!empty($event->Product()->first()->P_Ar_Name)){
                       $ProName=$event->Product()->first()->P_Ar_Name;
                          }else{
                              $ProName='';
                          }



         }else{
                                                        if(!empty($event->Product()->first()->P_En_Name)){
                       $ProName=$event->Product()->first()->P_En_Name;
                          }else{
                              $ProName='';
                          }

         }



         }else{

              $ProName='';

         }





     $event_list[] = Calendar::event(
                $NAme.' '.'('.$event->Type_Code .')'.' '.'('. $EmpName .')'.' '.'('. $AccName .')'.' '.'('. $ProName .')',
                true,
                new \DateTime($event->Start_Date),
                new \DateTime($event->End_Date.' +1 day')
            );
     }
     $calendar_details = Calendar::addEvents($event_list);


         return view('admin.CalendarMeet',['calendar_details'=>$calendar_details]);
    }

//AllNotifucations
     public function AllNotifucations(){

            if(auth()->guard('admin')->user()->emp == 0){
        $Notifications=Notifications::orderBy('id','desc')->paginate(30);
            }else{

         $Notifications=Notifications::orderBy('id','desc')->where('Emp',auth()->guard('admin')->user()->emp)->paginate(30);

            }




         return view('admin.Notifications',['Notifications'=>$Notifications]);
    }
         public function ReadNoti($id){

        Notifications::where('id',$id)->update(['Status'=>1]);

        return back();

           }

         public function UnReadNoti($id){

        Notifications::where('id',$id)->update(['Status'=>0]);

        return back();

           }

      public function DeletNoti($id){

        $del=Notifications::find($id);
        $del->delete();
        session()->flash('error',trans('admin.Deleted'));
        return back();

           }


    //Default_Data
    public function Default_DataPage(){
                $Accounts=AccountsDefaultData::orderBy('id','desc')->first();
                $Crms=CrmDefaultData::orderBy('id','desc')->first();
                $Stores=StoresDefaultData::orderBy('id','desc')->first();
                $Purchases=PurchasesDefaultData::orderBy('id','desc')->first();
                $Sales=SalesDefaultData::orderBy('id','desc')->first();
                $CustomPrint=CustomPrint::orderBy('id','desc')->first();
                $Companies=CompanyData::orderBy('id','desc')->first();
                $Print=ShowPrintDefault::orderBy('id','desc')->first();
                $ShowHide=DefaultDataShowHide::orderBy('id','desc')->first();
                $Maint=MaintainceDefaultData::orderBy('id','desc')->first();
                $Manu=ManufacturingDefaultData::orderBy('id','desc')->first();

         $CustomersGroup=CustomersGroup::all();
         $Coins=Coins::all();
         $Nationality=Countris::all();
         $CostCenters=CostCenter::all();
         $Units=Measuerments::all();
         $Taxes=Taxes::all();
         $ItemsGroups=ItemsGroups::all();
            $Storess=Stores::all();
            $ProductType=ProductTypeDefault::all();
              $Governrates=Governrate::all();
            $Cities=City::all();
            $Activites=Activites::all();
            $ClientStatus=ClientStatus::all();
            $Platforms=Platforms::all();
            $Campaigns=Campaigns::all();
             $Employess=Employess::where("EmpSort",1)->get();
        $Settings=BarcodeSettings::all();

                    $Safes = AcccountingManual::
             where('Type',1)
              ->where('Parent',28)
              ->orWhere('Parent',29)
              ->get();

                $Vendors = AcccountingManual::
             where('Type',1)
              ->where('Parent',37)
              ->orWhere('Parent',24)
              ->get();

             $Employesss = Employess::
             where('Emp_Type','Buyer')
                 ->where("EmpSort",1)
              ->get();

             $Employessss = Employess::
             where('Emp_Type','Saller')
                  ->where("EmpSort",1)
              ->get();


        $Waiters = Employess::
             where('Emp_Type','Waiter')
                  ->where("EmpSort",1)
              ->get();



             $Deliveries = Employess::
             where('Emp_Type','Delivery')
                  ->where("EmpSort",1)
              ->get();

           $Companiesss=ManufactureCompany::all();
           $Cases=DesviceCases::all();
          $Engs = Employess::
             where('Emp_Type','Engineer')
                  ->where("EmpSort",1)
              ->get();

           $ManufacturingHalls=ManufacturingHalls::all();

                    $Shipping=ShippingDefault::orderBy('id','desc')->first();
                 $Types=ShippingType::all();
        $Status=ShippingStatus::all();
           $vendors = AcccountingManual::
             where('Type',1)
              ->where('Parent',37)
              ->get();
           $clients = AcccountingManual::
             where('Type',1)
              ->where('Parent',24)
              ->get();





         return view('admin.Settings.Default_Data',[
             'Accounts'=>$Accounts,
             'Crms'=>$Crms,
             'Stores'=>$Stores,
             'Purchases'=>$Purchases,
             'Sales'=>$Sales,
             'Companies'=>$Companies,
             'Coins'=>$Coins,
             'Units'=>$Units,
             'Taxes'=>$Taxes,
             'ItemsGroups'=>$ItemsGroups,
             'Storess'=>$Storess,
             'ProductType'=>$ProductType,
             'Governrates'=>$Governrates,
             'Cities'=>$Cities,
             'Activites'=>$Activites,
             'ClientStatus'=>$ClientStatus,
             'Platforms'=>$Platforms,
             'Campaigns'=>$Campaigns,
             'Employess'=>$Employess,
             'Safes'=>$Safes,
             'Vendors'=>$Vendors,
             'Employesss'=>$Employesss,
             'Employessss'=>$Employessss,
             'Print'=>$Print,
             'Settings'=>$Settings,
             'ShowHide'=>$ShowHide,
             'Maint'=>$Maint,
             'Companiesss'=>$Companiesss,
             'Cases'=>$Cases,
             'Engs'=>$Engs,
             'Manu'=>$Manu,
             'CostCenters'=>$CostCenters,
             'Deliveries'=>$Deliveries,
             'ManufacturingHalls'=>$ManufacturingHalls,
             'Shipping'=>$Shipping,
             'Types'=>$Types,
             'Status'=>$Status,
             'vendors'=>$vendors,
             'clients'=>$clients,
             'Nationality'=>$Nationality,
             'Waiters'=>$Waiters,
             'CustomersGroup'=>$CustomersGroup,
             'CustomPrint'=>$CustomPrint,



         ]);
    }

     public function AddDefaultCompanyFirst(){


         $data['Name']=null;

         CompanyData::create($data);


             return back();

    }

      public function AddDefaultCompany(){

        $data= $this->validate(request(),[
             'Name'=>'required',
              'Logo'=>'image|mimes:jpeg,png,jpg|max:2048',
              'Icon'=>'image|mimes:jpeg,png,jpg|max:2048',
               ],[
            'Name.required' => trans('admin.nameRequired'),


         ]);

          $image=request()->file('Logo');
          if($image){
            $image_name=Str::random(20);
            $ext=strtolower($image->getClientOriginalExtension());
            $image_full_name=$image_name .'.' . $ext ;
            $upload_path='LogoImages/';
            $image_url=$upload_path.$image_full_name;
            $success=$image->move($upload_path,$image_full_name);
                   }


             if(!empty($image_url)){

                 $data['Logo']=$image_url;

             }else{
                 $data['Logo']=request('Logos');
             }


                $imagee=request()->file('Icon');
          if($imagee){
            $image_namee=Str::random(20);
            $exte=strtolower($imagee->getClientOriginalExtension());
            $image_full_namee=$image_namee .'.' . $exte ;
            $upload_pathe='IconImages/';
            $image_urle=$upload_pathe.$image_full_namee;
            $successe=$imagee->move($upload_pathe,$image_full_namee);
                   }


             if(!empty($image_urle)){

                 $data['Icon']=$image_urle;

             }else{
               $data['Icon']=request('Icons');
             }




                $imagex=request()->file('Seal');
          if($imagex){
            $image_namex=Str::random(20);
            $extx=strtolower($imagex->getClientOriginalExtension());
            $image_full_namex=$image_namex .'.' . $extx ;
            $upload_pathx='LogoImages/';
            $image_urlx=$upload_pathx.$image_full_namex;
            $successx=$imagex->move($upload_pathx,$image_full_namex);
                   }


             if(!empty($image_urlx)){

                 $data['Seal']=$image_urlx;

             }else{
                 $data['Seal']=request('Seals');
             }


                       $imagexs=request()->file('Logo_Store');
          if($imagexs){
            $image_namexs=Str::random(20);
            $extxs=strtolower($imagexs->getClientOriginalExtension());
            $image_full_namexs=$image_namexs .'.' . $extxs ;
            $upload_pathxs='LogoImages/';
            $image_urlxs=$upload_pathxs.$image_full_namexs;
            $successxs=$imagexs->move($upload_pathxs,$image_full_namexs);
                   }


             if(!empty($image_urlxs)){

                 $data['Logo_Store']=$image_urlxs;

             }else{
                 $data['Logo_Store']=request('Logo_StoreS');
             }



                                $imagexse=request()->file('Icon_Store');
          if($imagexse){
            $image_namexse=Str::random(20);
            $extxse=strtolower($imagexse->getClientOriginalExtension());
            $image_full_namexse=$image_namexse .'.' . $extxse ;
            $upload_pathxse='LogoImages/';
            $image_urlxse=$upload_pathxse.$image_full_namexse;
            $successxse=$imagexse->move($upload_pathxse,$image_full_namexse);
                   }


             if(!empty($image_urlxse)){

                 $data['Icon_Store']=$image_urlxse;

             }else{
                 $data['Icon_Store']=request('Icon_StoreS');
             }






                                       $imagePDF=request()->file('PDF');
          if($imagePDF){
            $image_namePDF=Str::random(20);
            $extPDF=strtolower($imagePDF->getClientOriginalExtension());
            $image_full_namePDF=$image_namePDF .'.' . $extPDF ;
            $upload_pathPDF='LogoImages/';
            $image_urlPDF=$upload_pathPDF.$image_full_namePDF;
            $successPDF=$imagePDF->move($upload_pathPDF,$image_full_namePDF);
                   }


             if(!empty($image_urlPDF)){

                 $data['PDF']=$image_urlPDF;

             }else{
                 $data['PDF']=request('PDFs');
             }


         $data['Name']=request('Name');
         $data['NameEn']=request('NameEn');
         $data['View']=request('View');
         $data['Phone1']=request('Phone1');
         $data['Phone2']=request('Phone2');
         $data['Phone3']=request('Phone3');
         $data['Phone4']=request('Phone4');
         $data['Address']=request('Address');
         $data['Commercial_Record']=request('Commercial_Record');
         $data['Tax_File_Number']=request('Tax_File_Number');
         $data['Print_Text']=request('Print_Text');
         $data['Print_Text_Footer']=request('Print_Text_Footer');
         $data['Print_Text_Footer_Sales']=request('Print_Text_Footer_Sales');
         $data['Name_Sales_Bill']=request('Name_Sales_Bill');
         $data['Name_Sales_Order_Bill']=request('Name_Sales_Order_Bill');
         $data['Print_Text_Footer_Quote']=request('Print_Text_Footer_Quote');
         $data['Name_Quote_Bill']=request('Name_Quote_Bill');
         $data['Print_Text_Footer_Secretariat']=request('Print_Text_Footer_Secretariat');
         $data['Tax_Registration_Number']=request('Tax_Registration_Number');
         $data['Tax_activity_code']=request('Tax_activity_code');
         $data['work_nature']=request('work_nature');
         $data['Governrate']=request('Governrate');
         $data['City']=request('City');
         $data['Place']=request('Place');
         $data['Nationality']=request('Nationality');
         $data['Buliding_Num']=request('Buliding_Num');
         $data['Street']=request('Street');
         $data['Postal_Code']=request('Postal_Code');
         $data['tax_magistrate']=request('tax_magistrate');
         $data['Client_ID']=request('Client_ID');
         $data['Serial_Client_ID']=request('Serial_Client_ID');
         $data['Print_Text_Footer_Manufacturing']=request('Print_Text_Footer_Manufacturing');
         $data['Version_Type']=request('Version_Type');
         $data['Computer_SN']=request('Computer_SN');
         $data['Invoice_Type']=request('Invoice_Type');
         $data['Floor']=request('Floor');
         $data['Room']=request('Room');
         $data['Landmark']=request('Landmark');
         $data['Add_Info']=request('Add_Info');
         $data['POS_Version']=request('POS_Version');
         $data['Path']=request('Path');
         $data['DB_Backup']=request('DB_Backup');
         $data['AddressEn']=request('AddressEn');
         $data['Print_Text_En']=request('Print_Text_En');
         $data['Print_Text_Footer_En']=request('Print_Text_Footer_En');
         $data['Print_Text_Footer_Manufacturing_En']=request('Print_Text_Footer_Manufacturing_En');
         $data['Print_Text_Footer_Sales_En']=request('Print_Text_Footer_Sales_En');
         $data['Print_Text_Footer_Quote_En']=request('Print_Text_Footer_Quote_En');
         $data['Print_Text_Footer_Secretariat_En']=request('Print_Text_Footer_Secretariat_En');
         $data['Name_Sales_Bill_En']=request('Name_Sales_Bill_En');
         $data['Name_Sales_Order_Bill_En']=request('Name_Sales_Order_Bill_En');
         $data['Name_Quote_Bill_En']=request('Name_Quote_Bill_En');
         $data['Location']=request('Location');
         $data['Email']=request('Email');
         $data['HomeMainScreen']=request('HomeMainScreen');
         $data['Bill_View']=request('Bill_View');
         $data['Font_Type']=request('Font_Type');
         $data['Welcome_Arabic_Word_App']=request('Welcome_Arabic_Word_App');
         $data['Welcome_English_Word_App']=request('Welcome_English_Word_App');


         CompanyData::orderBy('id','desc')->update($data);


             session()->flash('success',trans('admin.Updated'));
             return back();

    }

    public function AddDefaultAccountsFirst(){


         $data['Coin']=null;

         AccountsDefaultData::create($data);


             return back();

    }

      public function AddDefaultAccount(){

        $data= $this->validate(request(),[
             'Coin'=>'required',

               ],[
            'Coin.required' => trans('admin.CoinRequired'),


         ]);




         $data['Coin']=request('Coin');
         $data['Draw']=request('Draw');
         $data['Sure_Recipts']=request('Sure_Recipts');
         $data['Show_Group']=request('Show_Group');
         $data['Account_Balance']=request('Account_Balance');
         $data['Salary']=request('Salary');
         $data['Commission']=request('Commission');

         AccountsDefaultData::orderBy('id','desc')->update($data);


             session()->flash('success',trans('admin.Updated'));
             return back();

    }

      public function AddDefaultStoreFirst(){


         $data['Group']=null;

         StoresDefaultData::create($data);


             return back();

    }

      public function AddDefaultStore(){

        $data= $this->validate(request(),[
             'Group'=>'required',
             'Unit'=>'required',
             'Tax'=>'required',
             'Coin'=>'required',
             'Account_Excess'=>'required',
             'Account_Dificit'=>'required',
             'Store'=>'required',

               ],[



         ]);


         $data['Group']=request('Group');
         $data['Unit']=request('Unit');
         $data['Tax']=request('Tax');
         $data['Coin']=request('Coin');
         $data['Account_Excess']=request('Account_Excess');
         $data['Account_Dificit']=request('Account_Dificit');
         $data['Store']=request('Store');
         $data['Type']=request('Type');
         $data['Style']=request('Style');
         $data['Guide_Product_Cost']=request('Guide_Product_Cost');
         $data['Client_Store_Account']=request('Client_Store_Account');
         $data['StoresTarnsferPrice']=request('StoresTarnsferPrice');
         $data['Show_Ship']=request('Show_Ship');
         $data['StoresTarnsferHide']=request('StoresTarnsferHide');
         $data['CodeType']=request('CodeType');
         $data['ReturnStoresTransfer']=request('ReturnStoresTransfer');
         $data['Cost_Price']=request('Cost_Price');

         StoresDefaultData::orderBy('id','desc')->update($data);


          if(!empty(request('P_Type'))){
              ProductTypeDefault::truncate();
              $type=request('P_Type');

              for($i=0 ; $i < count($type) ; $i++){

                  $tt['Type']=$type[$i];

               ProductTypeDefault::create($tt);
              }




          }

       if(!empty(request('Show'))){

              $Shows=request('Show');
  $comp=0;
  $pN=0;
  $pP=0;
  $coin=0;
  $unit=0;
  $group=0;
  $code=0;
  $logo=0;

            for($i=0 ; $i < count($Shows) ; $i++){

                           if($Shows[$i] == 'CompanyName'){
              $comp += 1 ;
                  }else{
                       $comp += 0 ;

                           }

                    if($Shows[$i] == 'ProductName'){
                 $pN += 1 ;
                  }else{
                     $pN += 0 ;

                    }

                    if($Shows[$i] == 'ProductPrice'){
                   $pP += 1 ;
                  }else{

                    $pP += 0 ;
                    }

                    if($Shows[$i] == 'Coin'){
              $coin += 1 ;
                  }else{

                      $coin += 0 ;
                    }

                    if($Shows[$i] == 'Unit'){
             $unit += 1 ;
                  }else{

                   $unit += 0 ;
                    }

                    if($Shows[$i] == 'Group'){
                 $group += 1 ;
                  }else{

                   $group += 0 ;
                    }

                    if($Shows[$i] == 'Code'){
                $code += 1 ;
                  }else{

                 $code += 0 ;
                    }

                    if($Shows[$i] == 'Logo'){
                   $logo += 1 ;
                  }else{

                   $logo += 0 ;
                    }


                  }


                 $ttt['CompanyName']=$comp;
   $ttt['ProductName']=$pN;
 $ttt['ProductPrice']=$pP;
      $ttt['Coin']=$coin;
       $ttt['Unit']=$unit;
   $ttt['Group']=$group;
    $ttt['Code']=$code;
$ttt['Logo']=$logo;

                ShowPrintDefault::orderBy('id','desc')->update($ttt);



          }



             session()->flash('success',trans('admin.Updated'));
             return back();

    }

      public function AddDefaultCrmFirst(){


         $data['Price_Level']=null;

         CrmDefaultData::create($data);



             return back();

    }

      public function AddDefaultCrm(){

        $data= $this->validate(request(),[
             'Price_Level'=>'required',
             'Governrate'=>'required',
             'City'=>'required',
             'Responsible'=>'required',
             'Activity'=>'required',
             'Campagin'=>'required',
             'ClientStatus'=>'required',
             'Platforms'=>'required',

               ],[



         ]);


         $data['Price_Level']=request('Price_Level');
         $data['Governrate']=request('Governrate');
         $data['City']=request('City');
         $data['Responsible']=request('Responsible');
         $data['Activity']=request('Activity');
         $data['Campagin']=request('Campagin');
         $data['ClientStatus']=request('ClientStatus');
         $data['Platforms']=request('Platforms');
         $data['Client_Delegate']=request('Client_Delegate');
         $data['Nationality']=request('Nationality');
         $data['ClientGroup']=request('ClientGroup');

         CrmDefaultData::orderBy('id','desc')->update($data);


             session()->flash('success',trans('admin.Updated'));
             return back();

    }

      public function AddDefaultPurchasesFirst(){


         $data['Payment_Method']=null;

         PurchasesDefaultData::create($data);



             return back();

    }

       public function AddDefaultPurchases(){

        $data= $this->validate(request(),[
             'Payment_Method'=>'required',
             'Status'=>'required',
             'V_and_C'=>'required',
             'Safe'=>'required',
             'Vendor'=>'required',
             'Delegate'=>'required',
             'Store'=>'required',
             'Coin'=>'required',

               ],[



         ]);


         $data['Payment_Method']=request('Payment_Method');
         $data['Status']=request('Status');
         $data['V_and_C']=request('V_and_C');
         $data['Safe']=request('Safe');
         $data['Vendor']=request('Vendor');
         $data['Delegate']=request('Delegate');
         $data['Store']=request('Store');
         $data['Coin']=request('Coin');
         $data['Brand']=request('Brand');
         $data['Group']=request('Group');
         $data['English_Name']=request('English_Name');
         $data['Expire']=request('Expire');
         $data['Quality_Qty']=request('Quality_Qty');
            $data['Empp']=request('Empp');
            $data['Discount']=request('Discount');

         PurchasesDefaultData::orderBy('id','desc')->update($data);


             session()->flash('success',trans('admin.Updated'));
             return back();

    }

        public function AddDefaultSalesFirst(){


         $data['Payment_Method']=null;

         SalesDefaultData::create($data);



             return back();

    }

       public function AddDefaultSales(){

        $data= $this->validate(request(),[
             'Payment_Method'=>'required',
             'Status'=>'required',
             'V_and_C'=>'required',
             'Safe'=>'required',
             'Client'=>'required',
             'Delegate'=>'required',
             'Store'=>'required',
             'Coin'=>'required',

               ],[



         ]);


         $data['Payment_Method']=request('Payment_Method');
         $data['Status']=request('Status');
         $data['V_and_C']=request('V_and_C');
         $data['Mainus']=request('Mainus');
         $data['Price_Sale']=request('Price_Sale');
         $data['Safe']=request('Safe');
         $data['Client']=request('Client');
         $data['Delegate']=request('Delegate');
         $data['Store']=request('Store');
         $data['Coin']=request('Coin');
         $data['Brand']=request('Brand');
         $data['Group']=request('Group');
         $data['English_Name']=request('English_Name');
         $data['Expire']=request('Expire');
         $data['Draw']=request('Draw');
         $data['Empp']=request('Empp');
         $data['Discount']=request('Discount');
         $data['Shift_Pass']=request('Shift_Pass');
         $data['Delivery']=request('Delivery');
         $data['Execute_Precent']=request('Execute_Precent');
         $data['StoresQty']=request('StoresQty');
         $data['DelegateEmp']=request('DelegateEmp');
         $data['TaxType']=request('TaxType');
         $data['DiscountTaxShow']=request('DiscountTaxShow');
         $data['SalesOrderType']=request('SalesOrderType');
         $data['ECommercceSaleType']=request('ECommercceSaleType');
         $data['Kitchen_Order']=request('Kitchen_Order');
         $data['Waiter']=request('Waiter');
         $data['Hall_Service_Type']=request('Hall_Service_Type');
         $data['Hall_Service_Precent']=request('Hall_Service_Precent');
         $data['CountryResturantWebsite']=request('CountryResturantWebsite');
         $data['Bank']=request('Bank');
         $data['Country']=request('Country');
         $data['SalesLowCostPrice']=request('SalesLowCostPrice');
         $data['ShowJobOrders']=request('ShowJobOrders');
         $data['LimitSalesQty']=request('LimitSalesQty');
         $data['Total_Wight_Bill']=request('Total_Wight_Bill');
         $data['Duplicate_Items']=request('Duplicate_Items');

         SalesDefaultData::orderBy('id','desc')->update($data);


             session()->flash('success',trans('admin.Updated'));
             return back();

    }

    public function AddDefaultShowHideFirst(){


         $data['Coin']=null;

         DefaultDataShowHide::create($data);



             return back();

    }

       public function AddDefaultShowHide(){


         $data['Status']=request('Status');
         $data['Shipping_Company']=request('Shipping_Company');
         $data['Vendor_Date']=request('Vendor_Date');
         $data['Expire_Date']=request('Expire_Date');
         $data['Total_BF_Taxes']=request('Total_BF_Taxes');
         $data['Total_Taxes']=request('Total_Taxes');
         $data['Coin']=request('Coin');
         $data['Draw']=request('Draw');
         $data['Delegate_Sale']=request('Delegate_Sale');
         $data['Delegate_Purchase']=request('Delegate_Purchase');
         $data['Note']=request('Note');
         $data['Refrence_Number']=request('Refrence_Number');
         $data['Cost_Center']=request('Cost_Center');
         $data['Branch']=request('Branch');
         $data['Serial_Num']=request('Serial_Num');
         $data['Pass']=request('Pass');
         $data['Pattern_Image']=request('Pattern_Image');
         $data['Barcode_Print']=request('Barcode_Print');
         $data['Unit_Print']=request('Unit_Print');
         $data['Total_BF_Print']=request('Total_BF_Print');
         $data['Discount_Print']=request('Discount_Print');
         $data['Tax_Print']=request('Tax_Print');
         $data['A4']=request('A4');
         $data['A5']=request('A5');
         $data['CM8']=request('CM8');
         $data['Manufacturing_Model_Shortcomings']=request('Manufacturing_Model_Shortcomings');
         $data['Patch_Number']=request('Patch_Number');
         $data['Group_Brand']=request('Group_Brand');
         $data['Validity_Product']=request('Validity_Product');
         $data['Search_Typical']=request('Search_Typical');
         $data['Executor_Sale']=request('Executor_Sale');
         $data['Totuch_Screen']=request('Totuch_Screen');
         $data['Tax_POS']=request('Tax_POS');
         $data['TaxOnTotal']=request('TaxOnTotal');
         $data['TotalDiscountPrint']=request('TotalDiscountPrint');
         $data['TotalTaxPrint']=request('TotalTaxPrint');
         $data['ProductsNumber']=request('ProductsNumber');
         $data['TotalQtyPrint']=request('TotalQtyPrint');
         $data['Credit']=request('Credit');
         $data['Barcode']=request('Barcode');
         $data['Taknet']=request('Taknet');
         $data['Address']=request('Address');
         $data['Phone1']=request('Phone1');
         $data['Phone2']=request('Phone2');
         $data['Phone3']=request('Phone3');
         $data['Phone4']=request('Phone4');
         $data['Text']=request('Text');
         $data['Seal']=request('Seal');
         $data['Code_Report']=request('Code_Report');
         $data['Unit']=request('Unit');
         $data['Refrence_Number_Print']=request('Refrence_Number_Print');
         $data['Icon_Payment_Recipt']=request('Icon_Payment_Recipt');
         $data['SearchCode']=request('SearchCode');
         $data['TotalBfTax']=request('TotalBfTax');
         $data['AvQty']=request('AvQty');
         $data['Disc']=request('Disc');
         $data['Tax']=request('Tax');
         $data['Store']=request('Store');
         $data['client_delivery']=request('client_delivery');
         $data['TaxBill']=request('TaxBill');
         $data['Note_POS']=request('Note_POS');
         $data['Open_Drawer']=request('Open_Drawer');
         $data['Change_Way_Stores_Transfer']=request('Change_Way_Stores_Transfer');
         $data['POS_RecivedDate']=request('POS_RecivedDate');
         $data['POS_Qty']=request('POS_Qty');
         $data['POS_Barcode']=request('POS_Barcode');
         $data['Thickness_Print']=request('Thickness_Print');
         $data['Height_Print']=request('Height_Print');

         $data['Show_File_ReciptVoucher']=request('Show_File_ReciptVoucher');
         $data['Show_File_PaymentVoucher']=request('Show_File_PaymentVoucher');
         $data['Show_File_Sales']=request('Show_File_Sales');
         $data['Show_File_Purchases']=request('Show_File_Purchases');
         $data['Show_File_Checks']=request('Show_File_Checks');
         $data['Show_File_InsurancePaper']=request('Show_File_InsurancePaper');
         $data['Show_File_TransferStores']=request('Show_File_TransferStores');
         $data['Thickness']=request('Thickness');
         $data['Height']=request('Height');
         $data['Items_Guide_Store_Show']=request('Items_Guide_Store_Show');
         $data['Sales_Pro_Desc']=request('Sales_Pro_Desc');

         DefaultDataShowHide::orderBy('id','desc')->update($data);


             session()->flash('success',trans('admin.Updated'));
             return back();

    }

        public function AddDefaultMaintainceFirst(){


         $data['Coin']=null;

         MaintainceDefaultData::create($data);



             return back();

    }

       public function AddDefaultMaintaince(){


         $data['Company']=request('Company');
         $data['Device_Type']=request('Device_Type');
         $data['Device_Case']=request('Device_Case');
         $data['Coin']=request('Coin');
         $data['Cost_Center']=request('Cost_Center');
         $data['Draw']=request('Draw');
         $data['Client']=request('Client');
         $data['Sure']=request('Sure');
         $data['Eng']=request('Eng');
         $data['Recipient']=request('Recipient');
         $data['Store']=request('Store');

         MaintainceDefaultData::orderBy('id','desc')->update($data);


             session()->flash('success',trans('admin.Updated'));
             return back();

    }


        public function AddDefaultManufactureFirst(){


         $data['Coin']=null;

         ManufacturingDefaultData::create($data);



             return back();

    }

       public function AddDefaultManufacture(){


         $data['Coin']=request('Coin');
         $data['Draw']=request('Draw');
         $data['Hall']=request('Hall');
         $data['Manu_Type']=request('Manu_Type');
         $data['Executing_Qty']=request('Executing_Qty');

         ManufacturingDefaultData::orderBy('id','desc')->update($data);


             session()->flash('success',trans('admin.Updated'));
             return back();

    }

                public function AddDefaultShippingFirst(){


         $data['Delegate']=null;
         $data['Vendor']=null;
         $data['Client']=null;
         $data['Type']=null;
         $data['Status']=null;
         $data['Breakable']=null;
         $data['Coin']=null;

         ShippingDefault::create($data);



             return back();

    }

      public function AddDefaultShipping(){

       $data['Delegate']=request('Delegate');
         $data['Vendor']=request('Vendor');
         $data['Client']=request('Client');
         $data['Type']=request('Type');
         $data['Status']=request('Status');
         $data['Breakable']=request('Breakable');
         $data['Coin']=request('Coin');
         $data['Safe']=request('Safe');
         $data['Payment_Method']=request('Payment_Method');

         $data['Show_Coin']=request('Show_Coin');
         $data['Show_Draw']=request('Show_Draw');
         $data['Show_Safe']=request('Show_Safe');
         $data['Show_Code']=request('Show_Code');
         $data['Show_Weight']=request('Show_Weight');
         $data['Show_Width']=request('Show_Width');
         $data['Show_Length']=request('Show_Length');
         $data['Show_Height']=request('Show_Height');

         ShippingDefault::orderBy('id','desc')->update($data);


             session()->flash('success',trans('admin.Updated'));
             return back();

    }


     public function AddDefaultCustomPrintFirst(){


         $data['Sales_Print_Type']=null;
         $data['Sales_Bill_Code']=null;


         CustomPrint::create($data);



             return back();

    }

      public function AddDefaultCustomPrint(){

       $data['Sales_Print_Type']=request('Sales_Print_Type');
         $data['Sales_Bill_Code']=request('Sales_Bill_Code');
         $data['Sales_Date']=request('Sales_Date');
         $data['Sales_Coin']=request('Sales_Coin');
         $data['Sales_Draw']=request('Sales_Draw');
         $data['Sales_Payment_Method']=request('Sales_Payment_Method');
         $data['Sales_Status']=request('Sales_Status');
         $data['Sales_Executor']=request('Sales_Executor');
         $data['Sales_Refernce_Number']=request('Sales_Refernce_Number');
         $data['Sales_Safe']=request('Sales_Safe');
       $data['Sales_Client']=request('Sales_Client');
         $data['Sales_Delegate']=request('Sales_Delegate');
         $data['Sales_Store']=request('Sales_Store');
         $data['Sales_User']=request('Sales_User');
         $data['Sales_Cost_Center']=request('Sales_Cost_Center');
         $data['Sales_Notes']=request('Sales_Notes');
         $data['Sales_Pro_Code']=request('Sales_Pro_Code');
         $data['Sales_Pro_Name']=request('Sales_Pro_Name');
         $data['Sales_Pro_Unit']=request('Sales_Pro_Unit');
         $data['Sales_Pro_Qty']=request('Sales_Pro_Qty');
       $data['Sales_Pro_Price']=request('Sales_Pro_Price');
         $data['Sales_Pro_Discount']=request('Sales_Pro_Discount');
         $data['Sales_Pro_Total_Bf_Tax']=request('Sales_Pro_Total_Bf_Tax');
         $data['Sales_Pro_Total_Tax']=request('Sales_Pro_Total_Tax');
         $data['Sales_Pro_Total']=request('Sales_Pro_Total');
         $data['Sales_Pro_Store']=request('Sales_Pro_Store');
         $data['Sales_Pro_Desc']=request('Sales_Pro_Desc');
         $data['Sales_Pro_Exp_Date']=request('Sales_Pro_Exp_Date');
         $data['Sales_Pro_Weight']=request('Sales_Pro_Weight');
         $data['Sales_Pro_Patch_Number']=request('Sales_Pro_Patch_Number');
       $data['Sales_Product_Numbers']=request('Sales_Product_Numbers');
         $data['Sales_Total_Qty']=request('Sales_Total_Qty');
         $data['Sales_Total_Discount']=request('Sales_Total_Discount');
         $data['Sales_Total_Bf_Taxes']=request('Sales_Total_Bf_Taxes');
         $data['Sales_Total_Taxes']=request('Sales_Total_Taxes');
         $data['Sales_Total_Price']=request('Sales_Total_Price');
         $data['Sales_Paid']=request('Sales_Paid');
         $data['Sales_Residual']=request('Sales_Residual');
         $data['Sales_Taknet']=request('Sales_Taknet');
         $data['Sales_Credit']=request('Sales_Credit');
       $data['Sales_Barcode']=request('Sales_Barcode');
         $data['Sales_Text']=request('Sales_Text');
         $data['Sales_Seal']=request('Sales_Seal');
         $data['Purch_Print_Type']=request('Purch_Print_Type');
         $data['Purch_Bill_Code']=request('Purch_Bill_Code');
         $data['Purch_Date']=request('Purch_Date');
         $data['Purch_Vendor_Bill_Date']=request('Purch_Vendor_Bill_Date');
         $data['Purch_Coin']=request('Purch_Coin');
         $data['Purch_Draw']=request('Purch_Draw');
         $data['Purch_Payment_Method']=request('Purch_Payment_Method');
       $data['Purch_Status']=request('Purch_Status');
         $data['Purch_Refernce_Number']=request('Purch_Refernce_Number');
         $data['Purch_Safe']=request('Purch_Safe');
         $data['Purch_Vendor']=request('Purch_Vendor');
         $data['Purch_Delegate']=request('Purch_Delegate');
         $data['Purch_Store']=request('Purch_Store');
         $data['Purch_User']=request('Purch_User');
         $data['Purch_Cost_Center']=request('Purch_Cost_Center');
         $data['Purch_Notes']=request('Purch_Notes');
         $data['Purch_Pro_Code']=request('Purch_Pro_Code');
       $data['Purch_Pro_Name']=request('Purch_Pro_Name');
         $data['Purch_Pro_Unit']=request('Purch_Pro_Unit');
         $data['Purch_Pro_Qty']=request('Purch_Pro_Qty');
         $data['Purch_Pro_Price']=request('Purch_Pro_Price');
         $data['Purch_Pro_Discount']=request('Purch_Pro_Discount');
         $data['Purch_Pro_Total_Bf_Tax']=request('Purch_Pro_Total_Bf_Tax');
         $data['Purch_Pro_Total_Tax']=request('Purch_Pro_Total_Tax');
         $data['Purch_Pro_Total']=request('Purch_Pro_Total');
         $data['Purch_Pro_Store']=request('Purch_Pro_Store');
         $data['Purch_Pro_Exp_Date']=request('Purch_Pro_Exp_Date');
       $data['Purch_Product_Numbers']=request('Purch_Product_Numbers');
         $data['Purch_Total_Qty']=request('Purch_Total_Qty');
         $data['Purch_Total_Discount']=request('Purch_Total_Discount');
         $data['Purch_Total_Bf_Taxes']=request('Purch_Total_Bf_Taxes');
         $data['Purch_Total_Taxes']=request('Purch_Total_Taxes');
         $data['Purch_Total_Price']=request('Purch_Total_Price');
         $data['Purch_Paid']=request('Purch_Paid');
         $data['Purch_Residual']=request('Purch_Residual');
         $data['Purch_Taknet']=request('Purch_Taknet');
         $data['Purch_Credit']=request('Purch_Credit');
         $data['Purch_Barcode']=request('Purch_Barcode');
         $data['Purch_Text']=request('Purch_Text');
         $data['Purch_Seal']=request('Purch_Seal');


         CustomPrint::orderBy('id','desc')->update($data);


             session()->flash('success',trans('admin.Updated'));
             return back();

    }



    //   =======  GusetListPrice ==========

      public function GusetListPrice(){
          $Products=ProductUnits::distinct(['P_Ar_Name'])->get();
          $Groups=ItemsGroups::whereIn('Store_Show',[2,3])->get();
          $Brands=Brands::whereIn('Store_Show',[2,3])->get();
          $company=CompanyData::orderBy('id','desc')->first();
         return view('admin.GusetListPrice',['Products'=>$Products,'Groups'=>$Groups,'Brands'=>$Brands,'company'=>$company]);
    }

        function PriceListFilter(Request $request)

             {

     if($request->ajax())
     {
      $output = '';
      $brand = $request->get('Brand');
      $group = $request->get('Group');
      $HP = $request->get('HP');
      $LP = $request->get('LP');
      $Search = $request->get('Search');

    if($brand != '' or $group != '' or $HP != '' or $LP != '' or $Search != '')
    {

    if($Search != ''){

          if($HP != '' and $LP != ''){
            $Prods=ProductUnits::
               where('P_Ar_Name','ILIKE', "%{$Search}%")
               ->orWhere('P_En_Name','ILIKE', "%{$Search}%")
              ->whereBetween('Price', [$LP, $HP])
            ->distinct(['P_Ar_Name'])
            ->get();
        }elseif($HP != '' and $LP == ''){
             $Prods=ProductUnits::
               where('P_Ar_Name','ILIKE', "%{$Search}%")
               ->orWhere('P_En_Name','ILIKE', "%{$Search}%")
              ->whereBetween('Price', [0, $HP])
              ->distinct(['P_Ar_Name'])
            ->get();
        }elseif($HP == '' and $LP != ''){
             $Prods=ProductUnits::
              where('P_Ar_Name','ILIKE', "%{$Search}%")
              ->orWhere('P_En_Name','ILIKE', "%{$Search}%")
              ->whereBetween('Price', [0,$LP])
                  ->distinct(['P_Ar_Name'])
            ->get();
        }elseif($HP == '' and $LP == ''){

            $Prods=ProductUnits::where('P_Ar_Name','ILIKE', "%{$Search}%")
                ->orWhere('P_En_Name','ILIKE', "%{$Search}%")
              ->distinct(['P_Ar_Name'])
                ->get();
        }



    }else{

       if($HP != '' and $LP != ''){
            $Prods=ProductUnits::
                whereBetween('Price', [$LP, $HP])
                      ->distinct(['P_Ar_Name'])
            ->get();
        }elseif($HP != '' and $LP == ''){
             $Prods=ProductUnits::
              whereBetween('Price', [0, $HP])
                    ->distinct(['P_Ar_Name'])
            ->get();
        }elseif($HP == '' and $LP != ''){
             $Prods=ProductUnits::
             whereBetween('Price', [0,$LP])
                   ->distinct(['P_Ar_Name'])
            ->get();
        }elseif($HP == '' and $LP == ''){

            $Prods=ProductUnits::  distinct(['P_Ar_Name'])    ->get();
        }


    }

     }

         $total_row = $Prods->count();
      if($total_row > 0)
      {

         foreach($Prods as $pro){
             if($pro->Product()->first()->Store_Show == 2 or $pro->Product()->first()->Store_Show == 3){
    if($brand != '' and $group != '')
    {
        if($pro->Product()->first()->Group == $group){

              if($pro->Product()->first()->Brand == $brand){

        if(!empty($pro->Product()->first()->Image)){
             $img='<img src="'.URL::to($pro->Product()->first()->Image).'" alt="">';
        }else{
                 $img='<img src="'.asset('Admin/img/demo/gallery/5.jpg').'" alt="">';
        }

     if(auth()->guard('admin')->user()->price_level == 1){
                       $price=$pro->Price;
                   }elseif(auth()->guard('admin')->user()->price_level == 2){
                        $price=$pro->Price_Two;
                   }elseif(auth()->guard('admin')->user()->price_level == 3){
                        $price= $pro->Price_Three;
                   }

                    if(!empty($pro->Product()->first()->Ar_Desc)){

                   if(app()->getLocale() == 'ar' ){
            $Desc=$pro->Product()->first()->Ar_Desc;
                   }else{

              $Desc=$pro->Product()->first()->En_Desc;
                   }

                    }else{
                    $Desc='';
                    }
                                          if(auth()->guard('admin')->user()->email  != '<EMAIL>'){
        $priceX='<p>'.trans('admin.Price').': <span>'.$price.'</span></p>';
        }else{
           $priceX='';
        }



     if(app()->getLocale() == 'ar' ){
                      $PrrroName=$pro->P_Ar_Name;
                      $UnitName=$pro->Unit()->first()->Name;


                   }else{
                         $PrrroName=$pro->P_En_Name;
                         $UnitName=$pro->Unit()->first()->NameEn;


                   }


             $output .='

                          <div class="col-md-2">
                    <div class="product">
                    <div class="row">
                        <div class="col-md-6 col-6">
                      '.$img.'
                        </div>
                        <div class="col-md-6 col-6 padding-item">
                            <h5>'.$PrrroName.' </h5>
                            <h6>'.$UnitName.'</h6>
                        '.$priceX.'
                            <p> <button type="button" class="btn btn-primary btn-xs" data-toggle="modal" data-target="#description'.$pro->id.'"> '.trans('admin.Desc').'</button></p>
                        </div>
                    </div>
            </div>
                </div>

    <div class="modal fade" id="description'.$pro->id.'" tabindex="-1" role="dialog" aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h4 class="modal-title">
                        '.trans('admin.Desc').'
                    </h4>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true"><i class="fal fa-times"></i></span>
                    </button>
                </div>
                <div class="modal-body">
           '.$Desc.'
                </div>
                <div class="modal-footer">
                </div>
            </div>
        </div>
    </div>





             ';



                    }
        }

    }elseif($brand != '' and $group == ''){


              if($pro->Product()->first()->Brand == $brand){

                if(!empty($pro->Product()->first()->Image)){
             $img='<img src="'.URL::to($pro->Product()->first()->Image).'" alt="">';
        }else{
                 $img='<img src="'.asset('Admin/img/demo/gallery/5.jpg').'" alt="">';
        }

     if(auth()->guard('admin')->user()->price_level == 1){
                       $price=$pro->Price;
                   }elseif(auth()->guard('admin')->user()->price_level == 2){
                        $price=$pro->Price_Two;
                   }elseif(auth()->guard('admin')->user()->price_level == 3){
                        $price= $pro->Price_Three;
                   }

                    if(!empty($pro->Product()->first()->Ar_Desc)){

                   if(app()->getLocale() == 'ar' ){
            $Desc=$pro->Product()->first()->Ar_Desc;
                   }else{

              $Desc=$pro->Product()->first()->En_Desc;
                   }

                    }else{
                    $Desc='';
                    }
                                          if(auth()->guard('admin')->user()->email  != '<EMAIL>'){
        $priceX='<p>'.trans('admin.Price').': <span>'.$price.'</span></p>';
        }else{
           $priceX='';
        }



     if(app()->getLocale() == 'ar' ){
                      $PrrroName=$pro->P_Ar_Name;
                      $UnitName=$pro->Unit()->first()->Name;


                   }else{
                         $PrrroName=$pro->P_En_Name;
                         $UnitName=$pro->Unit()->first()->NameEn;


                   }


             $output .='

                          <div class="col-md-2">
                    <div class="product">
                    <div class="row">
                        <div class="col-md-6 col-6">
                      '.$img.'
                        </div>
                        <div class="col-md-6 col-6 padding-item">
                            <h5>'.$PrrroName.' </h5>
                            <h6>'.$UnitName.'</h6>
                        '.$priceX.'
                            <p> <button type="button" class="btn btn-primary btn-xs" data-toggle="modal" data-target="#description'.$pro->id.'"> '.trans('admin.Desc').'</button></p>
                        </div>
                    </div>
            </div>
                </div>

    <div class="modal fade" id="description'.$pro->id.'" tabindex="-1" role="dialog" aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h4 class="modal-title">
                        '.trans('admin.Desc').'
                    </h4>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true"><i class="fal fa-times"></i></span>
                    </button>
                </div>
                <div class="modal-body">
           '.$Desc.'
                </div>
                <div class="modal-footer">
                </div>
            </div>
        </div>
    </div>





             ';



                    }


    }elseif($brand == '' and $group != ''){

             if($pro->Product()->first()->Group == $group){
              if(!empty($pro->Product()->first()->Image)){
             $img='<img src="'.URL::to($pro->Product()->first()->Image).'" alt="">';
        }else{
                 $img='<img src="'.asset('Admin/img/demo/gallery/5.jpg').'" alt="">';
        }

     if(auth()->guard('admin')->user()->price_level == 1){
                       $price=$pro->Price;
                   }elseif(auth()->guard('admin')->user()->price_level == 2){
                        $price=$pro->Price_Two;
                   }elseif(auth()->guard('admin')->user()->price_level == 3){
                        $price= $pro->Price_Three;
                   }

                    if(!empty($pro->Product()->first()->Ar_Desc)){

                   if(app()->getLocale() == 'ar' ){
            $Desc=$pro->Product()->first()->Ar_Desc;
                   }else{

              $Desc=$pro->Product()->first()->En_Desc;
                   }

                    }else{
                    $Desc='';
                    }
                                          if(auth()->guard('admin')->user()->email  != '<EMAIL>'){
        $priceX='<p>'.trans('admin.Price').': <span>'.$price.'</span></p>';
        }else{
           $priceX='';
        }



     if(app()->getLocale() == 'ar' ){
                      $PrrroName=$pro->P_Ar_Name;
                      $UnitName=$pro->Unit()->first()->Name;


                   }else{
                         $PrrroName=$pro->P_En_Name;
                         $UnitName=$pro->Unit()->first()->NameEn;


                   }


             $output .='

                          <div class="col-md-2">
                    <div class="product">
                    <div class="row">
                        <div class="col-md-6 col-6">
                      '.$img.'
                        </div>
                        <div class="col-md-6 col-6 padding-item">
                            <h5>'.$PrrroName.' </h5>
                            <h6>'.$UnitName.'</h6>
                        '.$priceX.'
                            <p> <button type="button" class="btn btn-primary btn-xs" data-toggle="modal" data-target="#description'.$pro->id.'"> '.trans('admin.Desc').'</button></p>
                        </div>
                    </div>
            </div>
                </div>

    <div class="modal fade" id="description'.$pro->id.'" tabindex="-1" role="dialog" aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h4 class="modal-title">
                        '.trans('admin.Desc').'
                    </h4>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true"><i class="fal fa-times"></i></span>
                    </button>
                </div>
                <div class="modal-body">
           '.$Desc.'
                </div>
                <div class="modal-footer">
                </div>
            </div>
        </div>
    </div>





             ';




        }

    }elseif($brand == '' and $group == ''){


              if(!empty($pro->Product()->first()->Image)){
             $img='<img src="'.URL::to($pro->Product()->first()->Image).'" alt="">';
        }else{
                 $img='<img src="'.asset('Admin/img/demo/gallery/5.jpg').'" alt="">';
        }

     if(auth()->guard('admin')->user()->price_level == 1){
                       $price=$pro->Price;
                   }elseif(auth()->guard('admin')->user()->price_level == 2){
                        $price=$pro->Price_Two;
                   }elseif(auth()->guard('admin')->user()->price_level == 3){
                        $price= $pro->Price_Three;
                   }

                    if(!empty($pro->Product()->first()->Ar_Desc)){

                   if(app()->getLocale() == 'ar' ){
            $Desc=$pro->Product()->first()->Ar_Desc;
                   }else{

              $Desc=$pro->Product()->first()->En_Desc;
                   }

                    }else{
                    $Desc='';
                    }
                                          if(auth()->guard('admin')->user()->email  != '<EMAIL>'){
        $priceX='<p>'.trans('admin.Price').': <span>'.$price.'</span></p>';
        }else{
           $priceX='';
        }



     if(app()->getLocale() == 'ar' ){
                      $PrrroName=$pro->P_Ar_Name;
                      $UnitName=$pro->Unit()->first()->Name;


                   }else{
                         $PrrroName=$pro->P_En_Name;
                         $UnitName=$pro->Unit()->first()->NameEn;


                   }


             $output .='

                          <div class="col-md-2">
                    <div class="product">
                    <div class="row">
                        <div class="col-md-6 col-6">
                      '.$img.'
                        </div>
                        <div class="col-md-6 col-6 padding-item">
                            <h5>'.$PrrroName.' </h5>
                            <h6>'.$UnitName.'</h6>
                        '.$priceX.'
                            <p> <button type="button" class="btn btn-primary btn-xs" data-toggle="modal" data-target="#description'.$pro->id.'"> '.trans('admin.Desc').'</button></p>
                        </div>
                    </div>
            </div>
                </div>

    <div class="modal fade" id="description'.$pro->id.'" tabindex="-1" role="dialog" aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h4 class="modal-title">
                        '.trans('admin.Desc').'
                    </h4>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true"><i class="fal fa-times"></i></span>
                    </button>
                </div>
                <div class="modal-body">
           '.$Desc.'
                </div>
                <div class="modal-footer">
                </div>
            </div>
        </div>
    </div>





             ';




    }

             }


        }

      }else
      {
       $output = '
        <div class="col-md-12"> '.trans('admin.No_Data_Find').'</div>
       ';
      }
      $data = array(
       'table_data'  => $output,
      );
      echo json_encode($data);
     }
    }

       public function GUESTLIST(){
          $Products=ProductUnits::distinct(['P_Ar_Name'])->get();
       $Groups=ItemsGroups::whereIn('Store_Show',[2,3])->get();
          $Brands=Brands::whereIn('Store_Show',[2,3])->get();
          $company=CompanyData::orderBy('id','desc')->first();
         return view('GUESTLIST',['Products'=>$Products,'Groups'=>$Groups,'Brands'=>$Brands,'company'=>$company]);
    }

         function GUESTLISTFilter(Request $request)
             {


     if($request->ajax())
     {
      $output = '';
      $brand = $request->get('Brand');
      $group = $request->get('Group');
      $HP = $request->get('HP');
      $LP = $request->get('LP');
         $Search = $request->get('Search');
     if($brand != '' or $group != '' or $HP != '' or $LP != '' or $Search != '')
    {
    if($Search != ''){

          if($HP != '' and $LP != ''){
            $Prods=ProductUnits::
               where('P_Ar_Name','ILIKE', "%{$Search}%")
               ->orWhere('P_En_Name','ILIKE', "%{$Search}%")
              ->whereBetween('Price', [$LP, $HP])
                     ->distinct(['P_Ar_Name'])
            ->get();
        }elseif($HP != '' and $LP == ''){
             $Prods=ProductUnits::
               where('P_Ar_Name','ILIKE', "%{$Search}%")
               ->orWhere('P_En_Name','ILIKE', "%{$Search}%")
              ->whereBetween('Price', [0, $HP])
                     ->distinct(['P_Ar_Name'])
            ->get();
        }elseif($HP == '' and $LP != ''){
             $Prods=ProductUnits::
              where('P_Ar_Name','ILIKE', "%{$Search}%")
              ->orWhere('P_En_Name','ILIKE', "%{$Search}%")
              ->whereBetween('Price', [0,$LP])
                    ->distinct(['P_Ar_Name'])
            ->get();
        }elseif($HP == '' and $LP == ''){

            $Prods=ProductUnits::where('P_Ar_Name','ILIKE', "%{$Search}%")
                ->orWhere('P_En_Name','ILIKE', "%{$Search}%")
                  ->distinct(['P_Ar_Name'])
                ->get();
        }



    }else{

       if($HP != '' and $LP != ''){
            $Prods=ProductUnits::
                whereBetween('Price', [$LP, $HP])
                      ->distinct(['P_Ar_Name'])
            ->get();
        }elseif($HP != '' and $LP == ''){
             $Prods=ProductUnits::
              whereBetween('Price', [0, $HP])
                    ->distinct(['P_Ar_Name'])
            ->get();
        }elseif($HP == '' and $LP != ''){
             $Prods=ProductUnits::
             whereBetween('Price', [0,$LP])
                   ->distinct(['P_Ar_Name'])
            ->get();
        }elseif($HP == '' and $LP == ''){

            $Prods=ProductUnits:: distinct(['P_Ar_Name'])    ->get();
        }


    }
     }

         $total_row = $Prods->count();
      if($total_row > 0)
      {

         foreach($Prods as $pro){
             if($pro->Product()->first()->Store_Show == 2 or $pro->Product()->first()->Store_Show == 3){
    if($brand != '' and $group != '')
    {
        if($pro->Product()->first()->Group == $group){

              if($pro->Product()->first()->Brand == $brand){

        if(!empty($pro->Product()->first()->Image)){
             $img='<img onclick="OPEN('.$pro->id.')" id="myImg'.$pro->id.'" src="'.URL::to($pro->Product()->first()->Image).'" alt="">';
        }else{
                 $img='<img  onclick="OPEN('.$pro->id.')" id="myImg'.$pro->id.'" src="'.asset('Admin/img/demo/gallery/5.jpg').'" alt="">';
        }


                       $price=$pro->Price;


                    if(!empty($pro->Product()->first()->Ar_Desc)){

                           if(app()->getLocale() == 'ar' ){
            $Desc=$pro->Product()->first()->Ar_Desc;
                           }else{

                      $Desc=$pro->Product()->first()->En_Desc;
                           }


                    }else{
                    $Desc='';
                    }




          $priceX=$price;


     if(app()->getLocale() == 'ar' ){
                      $PrrroName=$pro->P_Ar_Name;
                      $UniiName=$pro->Unit()->first()->Name;


                   }else{
                         $PrrroName=$pro->P_En_Name;
                       $UniiName=$pro->Unit()->first()->NameEn;


                   }
             $output .='

                  <div class="col-md-2">
                  <div class="product">
                     <div class="row">
                        <div class="col-md-6 col-6 text-center">
                            '.$img.'
                          <!-- The Modal -->
<div id="myModal'.$pro->id.'" class="modal-img">
  <span class="close-img" id="close'.$pro->id.'">&times;</span>
  <img class="modal-content" id="img'.$pro->id.'">
  <div id="caption'.$pro->id.'"></div>
</div>
       <h6 style="color:#82b0ee;text-align: center;font-size:140%;margin: 5px 0 0 0;">'.$UniiName.' </h6>
                           <p style="color:blue; text-align: center;font-size:140%;"> <button type="button" class="btn btn-primary btn-xs" data-toggle="modal" data-target="#description'.$pro->id.'"> '.trans('admin.Desc').'</button></p>
                        </div>
                        <div class="col-md-6 col-6 padding-item">
                           <h5>'.$PrrroName.' </h5>
                           <br>
                           <p class="price">'.trans('admin.Price').': <span>'.$priceX.'</span></p>

                        </div>
                     </div>
                  </div>
               </div>


    <div class="modal fade" id="description'.$pro->id.'" tabindex="-1" role="dialog" aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h4 class="modal-title">
                        '.trans('admin.Desc').'
                    </h4>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true"><i class="fal fa-times"></i></span>
                    </button>
                </div>
                <div class="modal-body">
           '.$Desc.'
                </div>
                <div class="modal-footer">
                </div>
            </div>
        </div>
    </div>





             ';



                    }
        }

    }elseif($brand != '' and $group == ''){


              if($pro->Product()->first()->Brand == $brand){


        if(!empty($pro->Product()->first()->Image)){
             $img='<img onclick="OPEN('.$pro->id.')" id="myImg'.$pro->id.'" src="'.URL::to($pro->Product()->first()->Image).'" alt="">';
        }else{
                 $img='<img  onclick="OPEN('.$pro->id.')" id="myImg'.$pro->id.'" src="'.asset('Admin/img/demo/gallery/5.jpg').'" alt="">';
        }


                       $price=$pro->Price;


                    if(!empty($pro->Product()->first()->Ar_Desc)){

                           if(app()->getLocale() == 'ar' ){
            $Desc=$pro->Product()->first()->Ar_Desc;
                           }else{

                      $Desc=$pro->Product()->first()->En_Desc;
                           }


                    }else{
                    $Desc='';
                    }




          $priceX=$price;


     if(app()->getLocale() == 'ar' ){
                      $PrrroName=$pro->P_Ar_Name;
                      $UniiName=$pro->Unit()->first()->Name;


                   }else{
                         $PrrroName=$pro->P_En_Name;
                       $UniiName=$pro->Unit()->first()->NameEn;


                   }
             $output .='

                  <div class="col-md-2">
                  <div class="product">
                     <div class="row">
                        <div class="col-md-6 col-6 text-center">
                            '.$img.'
                          <!-- The Modal -->
<div id="myModal'.$pro->id.'" class="modal-img">
  <span class="close-img" id="close'.$pro->id.'">&times;</span>
  <img class="modal-content" id="img'.$pro->id.'">
  <div id="caption'.$pro->id.'"></div>
</div>
       <h6 style="color:#82b0ee;text-align: center;font-size:140%;margin: 5px 0 0 0;">'.$UniiName.' </h6>
                           <p style="color:blue; text-align: center;font-size:140%;"> <button type="button" class="btn btn-primary btn-xs" data-toggle="modal" data-target="#description'.$pro->id.'"> '.trans('admin.Desc').'</button></p>
                        </div>
                        <div class="col-md-6 col-6 padding-item">
                           <h5>'.$PrrroName.' </h5>
                           <br>
                           <p class="price">'.trans('admin.Price').': <span>'.$priceX.'</span></p>

                        </div>
                     </div>
                  </div>
               </div>


    <div class="modal fade" id="description'.$pro->id.'" tabindex="-1" role="dialog" aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h4 class="modal-title">
                        '.trans('admin.Desc').'
                    </h4>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true"><i class="fal fa-times"></i></span>
                    </button>
                </div>
                <div class="modal-body">
           '.$Desc.'
                </div>
                <div class="modal-footer">
                </div>
            </div>
        </div>
    </div>





             ';




                    }


    }elseif($brand == '' and $group != ''){

             if($pro->Product()->first()->Group == $group){

        if(!empty($pro->Product()->first()->Image)){
             $img='<img onclick="OPEN('.$pro->id.')" id="myImg'.$pro->id.'" src="'.URL::to($pro->Product()->first()->Image).'" alt="">';
        }else{
                 $img='<img  onclick="OPEN('.$pro->id.')" id="myImg'.$pro->id.'" src="'.asset('Admin/img/demo/gallery/5.jpg').'" alt="">';
        }


                       $price=$pro->Price;


                    if(!empty($pro->Product()->first()->Ar_Desc)){

                           if(app()->getLocale() == 'ar' ){
            $Desc=$pro->Product()->first()->Ar_Desc;
                           }else{

                      $Desc=$pro->Product()->first()->En_Desc;
                           }


                    }else{
                    $Desc='';
                    }




          $priceX=$price;


     if(app()->getLocale() == 'ar' ){
                      $PrrroName=$pro->P_Ar_Name;
                      $UniiName=$pro->Unit()->first()->Name;


                   }else{
                         $PrrroName=$pro->P_En_Name;
                       $UniiName=$pro->Unit()->first()->NameEn;


                   }
             $output .='

                  <div class="col-md-2">
                  <div class="product">
                     <div class="row">
                        <div class="col-md-6 col-6 text-center">
                            '.$img.'
                          <!-- The Modal -->
<div id="myModal'.$pro->id.'" class="modal-img">
  <span class="close-img" id="close'.$pro->id.'">&times;</span>
  <img class="modal-content" id="img'.$pro->id.'">
  <div id="caption'.$pro->id.'"></div>
</div>
       <h6 style="color:#82b0ee;text-align: center;font-size:140%;margin: 5px 0 0 0;">'.$UniiName.' </h6>
                           <p style="color:blue; text-align: center;font-size:140%;"> <button type="button" class="btn btn-primary btn-xs" data-toggle="modal" data-target="#description'.$pro->id.'"> '.trans('admin.Desc').'</button></p>
                        </div>
                        <div class="col-md-6 col-6 padding-item">
                           <h5>'.$PrrroName.' </h5>
                           <br>
                           <p class="price">'.trans('admin.Price').': <span>'.$priceX.'</span></p>

                        </div>
                     </div>
                  </div>
               </div>


    <div class="modal fade" id="description'.$pro->id.'" tabindex="-1" role="dialog" aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h4 class="modal-title">
                        '.trans('admin.Desc').'
                    </h4>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true"><i class="fal fa-times"></i></span>
                    </button>
                </div>
                <div class="modal-body">
           '.$Desc.'
                </div>
                <div class="modal-footer">
                </div>
            </div>
        </div>
    </div>





             ';





        }

    }elseif($brand == '' and $group == ''){


        if(!empty($pro->Product()->first()->Image)){
             $img='<img onclick="OPEN('.$pro->id.')" id="myImg'.$pro->id.'" src="'.URL::to($pro->Product()->first()->Image).'" alt="">';
        }else{
                 $img='<img  onclick="OPEN('.$pro->id.')" id="myImg'.$pro->id.'" src="'.asset('Admin/img/demo/gallery/5.jpg').'" alt="">';
        }


                       $price=$pro->Price;


                    if(!empty($pro->Product()->first()->Ar_Desc)){

                           if(app()->getLocale() == 'ar' ){
            $Desc=$pro->Product()->first()->Ar_Desc;
                           }else{

                      $Desc=$pro->Product()->first()->En_Desc;
                           }


                    }else{
                    $Desc='';
                    }




          $priceX=$price;


     if(app()->getLocale() == 'ar' ){
                      $PrrroName=$pro->P_Ar_Name;
                      $UniiName=$pro->Unit()->first()->Name;


                   }else{
                         $PrrroName=$pro->P_En_Name;
                       $UniiName=$pro->Unit()->first()->NameEn;


                   }
             $output .='

                  <div class="col-md-2">
                  <div class="product">
                     <div class="row">
                        <div class="col-md-6 col-6 text-center">
                            '.$img.'
                          <!-- The Modal -->
<div id="myModal'.$pro->id.'" class="modal-img">
  <span class="close-img" id="close'.$pro->id.'">&times;</span>
  <img class="modal-content" id="img'.$pro->id.'">
  <div id="caption'.$pro->id.'"></div>
</div>
       <h6 style="color:#82b0ee;text-align: center;font-size:140%;margin: 5px 0 0 0;">'.$UniiName.' </h6>
                           <p style="color:blue; text-align: center;font-size:140%;"> <button type="button" class="btn btn-primary btn-xs" data-toggle="modal" data-target="#description'.$pro->id.'"> '.trans('admin.Desc').'</button></p>
                        </div>
                        <div class="col-md-6 col-6 padding-item">
                           <h5>'.$PrrroName.' </h5>
                           <br>
                           <p class="price">'.trans('admin.Price').': <span>'.$priceX.'</span></p>

                        </div>
                     </div>
                  </div>
               </div>


    <div class="modal fade" id="description'.$pro->id.'" tabindex="-1" role="dialog" aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h4 class="modal-title">
                        '.trans('admin.Desc').'
                    </h4>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true"><i class="fal fa-times"></i></span>
                    </button>
                </div>
                <div class="modal-body">
           '.$Desc.'
                </div>
                <div class="modal-footer">
                </div>
            </div>
        </div>
    </div>





             ';




    }

             }


        }

      }else
      {
       $output = '
        <div class="col-md-12"> '.trans('admin.No_Data_Find').'</div>
       ';
      }
      $data = array(
       'table_data'  => $output,
      );
      echo json_encode($data);
     }
    }

    // ==   Modules Settings  ====
     public function Modules_SettingsPage(){
          $Packages=Packages::all();
          $Modules=Modules::orderBy('id','desc')->first();
          $ReportsSettings=ReportsSettings::orderBy('id','desc')->first();
          $ModulesNum=ModuleSettingsNum::orderBy('id','desc')->first();
          $intro=Intro::orderBy('id','desc')->first();
          $Intro=Intro::orderBy('id','desc')->first();
          $permission = Permission::orderBy('Main','asc')->get();

                 $Educations=RabihEducation::all();

         return view('admin.ModulesSettings',[
             'Modules'=>$Modules,
             'ModulesNum'=>$ModulesNum,
             'ReportsSettings'=>$ReportsSettings,
             'permission'=>$permission,
             'Packages'=>$Packages,
             'intro'=>$intro,
             'Intro'=>$Intro,
             'Educations'=>$Educations
         ]);
    }

       public function AddDefaultModulesFirst(){

         $data['Capital']=0;
         $data['Accounts']=0;
         $data['Stores']=0;
         $data['CRM']=0;
         $data['HR']=0;
         $data['Manufacturing']=0;
         $data['Maintenance']=0;
         $data['Secretariat']=0;
         $data['Petrol']=0;
         $data['Shipping']=0;
         $data['Resturant']=0;

         Modules::create($data);



             return back();

    }

       public function AddDefaultModules(){


            $data['Capital']=request('Capital');
         $data['Accounts']=request('Accounts');
         $data['Stores']=request('Stores');
         $data['CRM']=request('CRM');
         $data['HR']=request('HR');
         $data['Manufacturing']=request('Manufacturing');
         $data['Maintenance']=request('Maintenance');
         $data['Secretariat']=request('Secretariat');
         $data['Petrol']=request('Petrol');
         $data['ECommerce']=request('ECommerce');
         $data['Shipping']=request('Shipping');
         $data['Bill_Electronic']=request('Bill_Electronic');
         $data['Hotels']=request('Hotels');
         $data['Resturant']=request('Resturant');
         $data['Traning_Center']=request('Traning_Center');
         $data['Translate']=request('Translate');
         Modules::orderBy('id','desc')->update($data);


             session()->flash('success',trans('admin.Updated'));
             return back();

    }

     public function AddDefaultModulesNumFirst(){

         $data['Branch_Select']=null;
         $data['Branch_Num']=null;
         $data['Store_Select']=null;
         $data['Store_Num']=null;
         $data['Users_Select']=null;
         $data['Users_Num']=null;
         ModuleSettingsNum::create($data);
             return back();

    }

     public function AddDefaultModulesNum(){

         $data['Branch_Select']=request('Branch_Select');
         $data['Branch_Num']=request('Branch_Num');
         $data['Store_Select']=request('Store_Select');
         $data['Store_Num']=request('Store_Num');
         $data['Users_Select']=request('Users_Select');
         $data['Users_Num']=request('Users_Num');
         $data['System']=request('System');
         $data['Expire_Date']=request('Expire_Date');
         $data['Type']=request('Type');
         $data['Price']=request('Price');

           ModuleSettingsNum::orderBy('id','desc')->update($data);
             return back();

    }


       public function AddDefaultReportSettingsFirst(){

         $data['Product_Info']=null;

         ReportsSettings::create($data);
             return back();

    }


     public function AddDefaultReportSettings(){



         $data['Product_Info']=request('Product_Info');
         $data['Product_Order_Limit']=request('Product_Order_Limit');
         $data['ReportStartPeriodProducts']=request('ReportStartPeriodProducts');
         $data['SettlementsReports']=request('SettlementsReports');
         $data['StoresCost']=request('StoresCost');
         $data['StoresInventory']=request('StoresInventory');
         $data['Collection_Delegates']=request('Collection_Delegates');
         $data['Sales_Delegates']=request('Sales_Delegates');
         $data['StagnantItems']=request('StagnantItems');
         $data['ItemsMoves']=request('ItemsMoves');
         $data['StoresBalancesTwo']=request('StoresBalancesTwo');
         $data['NetPurchases']=request('NetPurchases');
         $data['NetSales']=request('NetSales');
         $data['ClientSales']=request('ClientSales');
         $data['ExecutorSales']=request('ExecutorSales');
         $data['InstallmentReport']=request('InstallmentReport');
         $data['ExpiredProucts']=request('ExpiredProucts');
         $data['StoresSalesDetails']=request('StoresSalesDetails');
         $data['TotalNetPurchases']=request('TotalNetPurchases');
         $data['TotalNetSales']=request('TotalNetSales');
         $data['Profits']=request('Profits');
         $data['Shifts']=request('Shifts');
         $data['Shifts_Details']=request('Shifts_Details');
         $data['DailyClosing']=request('DailyClosing');
         $data['Products']=request('Products');
         $data['DailyShifts']=request('DailyShifts');
         $data['ExpensesReport']=request('ExpensesReport');
         $data['DailyProducts']=request('DailyProducts');
         $data['EmployeeCommissionDiscounts']=request('EmployeeCommissionDiscounts');
         $data['VendorPricesReport']=request('VendorPricesReport');
         $data['DailyMoves']=request('DailyMoves');
         $data['GroupsSales']=request('GroupsSales');
         $data['VendorPurchases']=request('VendorPurchases');
         $data['ExceptProfits']=request('ExceptProfits');
         $data['DelegateSalesDetails']=request('DelegateSalesDetails');
         $data['CreditStores']=request('CreditStores');
         $data['ProductProfits']=request('ProductProfits');
         $data['ExceptProductProfits']=request('ExceptProductProfits');
         $data['SalesBills']=request('SalesBills');
         $data['PurchasesBills']=request('PurchasesBills');
         $data['StoresMovesReport']=request('StoresMovesReport');
         $data['StoresTransferReport']=request('StoresTransferReport');
         $data['SafesTransferReport']=request('SafesTransferReport');
         $data['CompareSalesPrice']=request('CompareSalesPrice');
         $data['ProductMoveDetails']=request('ProductMoveDetails');
         $data['MostSalesProducts']=request('MostSalesProducts');
         $data['ProfitSalesProduct']=request('ProfitSalesProduct');
         $data['ClientAccountStatement']=request('ClientAccountStatement');
         $data['ClientsStatement']=request('ClientsStatement');
         $data['VendorAccountStatement']=request('VendorAccountStatement');
         $data['VendorsStatement']=request('VendorsStatement');
         $data['EmpGoals']=request('EmpGoals');
         $data['InventorySerial']=request('InventorySerial');
         $data['TotalExpensesSafes']=request('TotalExpensesSafes');
         $data['SubIncomList']=request('SubIncomList');
         $data['ExpensesList']=request('ExpensesList');
         $data['StoresBalances']=request('StoresBalances');
         $data['StoresBalancesCat']=request('StoresBalancesCat');
         $data['ItemCost']=request('ItemCost');
         $data['StoresInventoryy']=request('StoresInventoryy');
         $data['DelegateSalesDetailss']=request('DelegateSalesDetailss');
         $data['ProfitDelegateSalesDetails']=request('ProfitDelegateSalesDetails');
         $data['InstallmentCompaniesSales']=request('InstallmentCompaniesSales');
         $data['StoresCosts']=request('StoresCosts');
         $data['DailyClosingDetails']=request('DailyClosingDetails');
         $data['SalesProsMoreDetails']=request('SalesProsMoreDetails');
         $data['StagnantItemss']=request('StagnantItemss');
         $data['SalesCustomersGroups']=request('SalesCustomersGroups');
         $data['BrandsSales']=request('BrandsSales');
         $data['Customer_Debts']=request('Customer_Debts');
         $data['Vendor_Debts']=request('Vendor_Debts');
         $data['MaintanceSalesReport']=request('MaintanceSalesReport');
         $data['Maintenance_Tune']=request('Maintenance_Tune');
         $data['ProfitGroupsReport']=request('ProfitGroupsReport');
         $data['IncomListReport']=request('IncomListReport');
         ReportsSettings::orderBy('id','desc')->update($data);

             return back();

    }


         public function AddDefaultPackagesSettings(){


         $ID = DB::table('packages')->insertGetId(
        array(


            'Arabic_Name' => request('Arabic_Name'),
            'English_Name' => request('English_Name'),



        )
    );



             $permission=request('permission');

             for($i=0 ; $i < count($permission) ; $i++){
                $de['package']=$ID;
                $de['premission']=$permission[$i];

              PackPrem::create($de);
             }





           session()->flash('success',trans('admin.Added_Successfully'));
             return back();

    }

         public function AddDefaultIntroFirst(){

         $data['Arabic_About']=null;
         $data['English_About']=null;
         $data['Phone_1']=null;
         $data['Phone_2']=null;
         $data['Phone_3']=null;
         $data['Phone_4']=null;
         $data['Arabic_Terms']=null;
         $data['English_Terms']=null;
         Intro::create($data);
             return back();

    }

     public function AddDefaultIntro(){

         $data['Arabic_About']=request('Arabic_About');
         $data['English_About']=request('English_About');
         $data['Phone_1']=request('Phone_1');
         $data['Phone_2']=request('Phone_2');
         $data['Phone_3']=request('Phone_3');
         $data['Phone_4']=request('Phone_4');
         $data['Arabic_Terms']=request('Arabic_Terms');
         $data['English_Terms']=request('English_Terms');


           Intro::orderBy('id','desc')->update($data);
             return back();

    }



    //IntroView  Rabih =======================
         public function IntroView(){

          $Intro=Intro::orderBy('id','desc')->first();

         return view('admin.IntroView',[

             'Intro'=>$Intro
         ]);
    }

        public function TermsView(){

          $Intro=Intro::orderBy('id','desc')->first();

         return view('admin.TermsView',[

             'Intro'=>$Intro
         ]);
    }




    //Chat Issues



            public function ReportIssue(){

          $Issues=Issues::orderBy('id','desc')->paginate(20);

         return view('admin.ReportIssue',[

             'Issues'=>$Issues
         ]);
    }

           public function CreateNewChat(){

          $user=Admin::find(auth()->guard('admin')->user()->id);


            $res=Issues::orderBy('id','desc')->first();

           if(!empty($res->Code)){

              $Code=$res->Code + 1 ;

           }else{

              $Code=1;

           }
   $ID = DB::table('issues')->insertGetId(
        array(


            'Code' => $Code,
            'Date' => date('Y-m-d'),
            'Time' => date('h-i A'),
            'Client' => $user->name,
            'Link' => URL::to('/'),
            'Status' => 0,



        )
    );


               $Issue=Issues::find($ID);
             $Chat=ChatIssue::where('Issue',$ID)->get();


    $curl = curl_init();

curl_setopt_array($curl, array(
  CURLOPT_URL => 'https://rabiherp.com/public/api/OpenNewIssueApi',
  CURLOPT_RETURNTRANSFER => true,
  CURLOPT_ENCODING => '',
  CURLOPT_MAXREDIRS => 10,
  CURLOPT_TIMEOUT => 0,
  CURLOPT_FOLLOWLOCATION => true,
  CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
  CURLOPT_CUSTOMREQUEST => 'POST',
  CURLOPT_POSTFIELDS => array('Code' => $Issue->Code,'Date' => $Issue->Date,'Time' => $Issue->Time,'Client' => $Issue->Client,'Link' => $Issue->Link),
  CURLOPT_HTTPHEADER => array(
    'lang: ar'
  ),
));

$response = curl_exec($curl);

curl_close($curl);

         return back();
    }

         public function ChatIssue($id){

          $user=Admin::find(auth()->guard('admin')->user()->id);
             $Issue=Issues::find($id);
             $Chat=ChatIssue::where('Issue',$id)->get();


         return view('admin.ChatIssue',[

             'user'=>$user,
             'Issue'=>$Issue,
             'Chat'=>$Chat,
         ]);
    }


     public function UploadChatImg(Request $request){

               $image=request()->file('Image');
          if($image){
            $image_name=Str::random(20);
            $ext=strtolower($image->getClientOriginalExtension());
            $image_full_name=$image_name .'.' . $ext ;
            $upload_path='AdminsImages/';
            $image_url=$upload_path.$image_full_name;
            $success=$image->move($upload_path,$image_full_name);
                   }


             if(!empty($image_url)){

        $CHAT['Image']=$image_url;
        $CHAT['Name']=request('Name');
        $CHAT['Date']=date('Y-m-d');
        $CHAT['Time']=date('h-i A');
        $CHAT['Desc']=null;
        $CHAT['Issue']=request('Issue');
        $CHAT['Type']=1;

        ChatIssue::create($CHAT);


          $Iss=Issues::find(request('Issue'));

        $curl = curl_init();

curl_setopt_array($curl, array(
  CURLOPT_URL => 'https://rabiherp.com/public/api/GetTextChatImage',
  CURLOPT_RETURNTRANSFER => true,
  CURLOPT_ENCODING => '',
  CURLOPT_MAXREDIRS => 10,
  CURLOPT_TIMEOUT => 0,
  CURLOPT_FOLLOWLOCATION => true,
  CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
  CURLOPT_CUSTOMREQUEST => 'POST',
  CURLOPT_POSTFIELDS => array('Code' => $Iss->Code,'Name' => request('Name'),'Date' => date('Y-m-d'),'Time' => date('h-i A'),'Image' => $image_url),
  CURLOPT_HTTPHEADER => array(
    'lang: ar'
  ),
));

$response = curl_exec($curl);

curl_close($curl);






             }



         return back();
    }



         function SendIssue(Request $request)
             {


     if($request->ajax())
     {
      $output = '';
      $msgr_input = $request->get('msgr_input');
      $Name = $request->get('Name');
      $Link = $request->get('Link');
      $Auto = $request->get('Auto');
      $Issue = $request->get('Issue');

    if($msgr_input != '' and $Name != '')
    {




        $CHAT['Name']=$Name;
        $CHAT['Date']=date('Y-m-d');
        $CHAT['Time']=date('h-i A');
        $CHAT['Desc']=$msgr_input;
        $CHAT['Issue']=$Issue;
        $CHAT['Type']=1;

        ChatIssue::create($CHAT);

         $Iss=Issues::find($Issue);

        $curl = curl_init();

curl_setopt_array($curl, array(
  CURLOPT_URL => 'https://rabiherp.com/public/api/GetTextChat',
  CURLOPT_RETURNTRANSFER => true,
  CURLOPT_ENCODING => '',
  CURLOPT_MAXREDIRS => 10,
  CURLOPT_TIMEOUT => 0,
  CURLOPT_FOLLOWLOCATION => true,
  CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
  CURLOPT_CUSTOMREQUEST => 'POST',
  CURLOPT_POSTFIELDS => array('Code' => $Iss->Code,'Name' => $Name,'Date' => date('Y-m-d'),'Time' => date('h-i A'),'Desc' => $msgr_input),
  CURLOPT_HTTPHEADER => array(
    'lang: ar'
  ),
));

$response = curl_exec($curl);

curl_close($curl);


             $ff=trans("admin.ThankUForContactUs");
               if($Auto == 1){

            $x='none';

        }else{
            $x='block';

              $CHAT['Name']='Customer Service / خدمة العملاء ';
        $CHAT['Date']=date('Y-m-d');
        $CHAT['Time']=date('h-i A');
        $CHAT['Desc']=$ff;
        $CHAT['Image']=null;
        $CHAT['Issue']=$Issue;
        $CHAT['Type']=2;
        $CHAT['Appear']=1;

        ChatIssue::create($CHAT);


        }

         $output .= '

           <div class="chat-segment chat-segment-sent">
           <h2>'.$Name.' </h2>
                                                            <div class="chat-message">
                                                                <p>
                                                            '.$msgr_input.'
                                                                </p>

                                                            </div>
                                                            <div class="text-right fw-300 text-muted mt-1 fs-xs">
                                                               '.date('Y-m-d').'/'.date('h:i A').'
                                                            </div>
                                                        </div>

                                                                       <div class="chat-segment chat-segment-get" style="display:'.$x.'">
                                                                                <h2>Customer Service / خدمة العملاء </h2>
                                                            <div class="chat-message">
                                                                <h3>
                                                              '.$ff.'
                                                                </h3>



                                                            </div>
                                                            <div class="fw-300 text-muted mt-1 fs-xs">
                                                                   '.date('Y-m-d').'/'.date('h:i A').'
                                                            </div>
                                                        </div>


         ';



     }

      $data = array(
       'table_data'  => $output,
      );
      echo json_encode($data);
     }
    }


         public function SolveIssue($id){




             Issues::where('id',$id)->update(['Status'=>1]);


             $Issue=Issues::find($id);

    $curl = curl_init();

curl_setopt_array($curl, array(
  CURLOPT_URL => 'https://rabiherp.com/public/api/SolveIssueApi',
  CURLOPT_RETURNTRANSFER => true,
  CURLOPT_ENCODING => '',
  CURLOPT_MAXREDIRS => 10,
  CURLOPT_TIMEOUT => 0,
  CURLOPT_FOLLOWLOCATION => true,
  CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
  CURLOPT_CUSTOMREQUEST => 'POST',
  CURLOPT_POSTFIELDS => array('Code' => $Issue->Code),
  CURLOPT_HTTPHEADER => array(
    'lang: ar'
  ),
));

$response = curl_exec($curl);

curl_close($curl);

         return back();
    }



            function RefreshSendIssue(Request $request)
             {


     if($request->ajax())
     {
      $output = '';
          $Issue = $request->get('Issue');
      $x = 1;

    if($x == 1)
    {




        $Last=ChatIssue::where('Issue',$Issue)->where('Type',2)->orderBy('id','desc')->first();


      if($Last->Appear != 1){

          if(!empty($Last->Image)){
          $imgoo='<img src="'.$Last->Image.'" style="width:40%">';
          }else{
          $imgoo='';

          }
         $output .= '

                           <div class="chat-segment chat-segment-get">
                                                                                <h2> Customer Service / خدمة العملاء </h2>
                                                            <div class="chat-message">
                                                                <h3>
                                                              '.$Last->Desc.'
                                                                </h3>

                                                  '.$imgoo.'

                                                            </div>
                                                            <div class="fw-300 text-muted mt-1 fs-xs">
                                                                   '.$Last->Date.'/'.$Last->Time.'
                                                            </div>
                                                        </div>





         ';

       ChatIssue::where('id',$Last->id)->update(['Appear'=>1]);

      }


     }

      $data = array(
       'table_data'  => $output,
      );
      echo json_encode($data);
     }
    }




//GetTextChat
     public function GetTextChat(){


           $iss=Issues::where('Code',request('Code'))->orderBy('id','desc')->first();
          $CHAT['Name']=request('Name');
        $CHAT['Date']=request('Date');
        $CHAT['Time']=request('Time');
        $CHAT['Desc']=request('Desc');
        $CHAT['Issue']=$iss->id;
        $CHAT['Type']=2;

        ChatIssue::create($CHAT);

            return response()->json([
             'status'=>200,
             'message'=>trans('admin.Success'),
           ]);
    }
         public function GetTextChatImage(){


           $iss=Issues::where('Code',request('Code'))->orderBy('id','desc')->first();
          $CHAT['Name']=request('Name');
        $CHAT['Date']=request('Date');
        $CHAT['Time']=request('Time');
        $CHAT['Desc']=null;
        $CHAT['Image']='https://rabiherp.com/public/'.request('Image');
        $CHAT['Issue']=$iss->id;
        $CHAT['Type']=2;

        ChatIssue::create($CHAT);

            return response()->json([
             'status'=>200,
             'message'=>trans('admin.Success'),
           ]);
    }



    //Education
            public function RabihEducation(){

                if(auth()->guard('admin')->user()->email != '<EMAIL>'){
          $Videos=RabihEducation::where('Package',auth()->guard('admin')->user()->package)->get();
                }else{
              $Videos=RabihEducation::get();
                }
         return view('admin.RabihEducation',[

             'Videos'=>$Videos
         ]);
    }

           public function AddRabihEdu(){


        $data= $this->validate(request(),[
             'Arabic_Name'=>'required',
             'English_Name'=>'required',
             'Video'=>'required',
             'Package'=>'required',


               ],[


         ]);


         $data['Arabic_Name']=request('Arabic_Name');
         $data['English_Name']=request('English_Name');
         $data['Video']=request('Video');
         $data['Package']=request('Package');



         RabihEducation::create($data);



             session()->flash('success',trans('admin.NewAddAdmin'));
             return back();

    }

     public function EditRabihEdu($id){
        $data= $this->validate(request(),[
             'Arabic_Name'=>'required',
             'English_Name'=>'required',
             'Video'=>'required',
             'Package'=>'required',


               ],[


         ]);


         $data['Arabic_Name']=request('Arabic_Name');
         $data['English_Name']=request('English_Name');
         $data['Video']=request('Video');
         $data['Package']=request('Package');


           RabihEducation::where('id',$id)->update($data);



            session()->flash('success',trans('admin.Updated'));
            return back();


     }

     public function DeleteRabihEdu($id){

        $del=RabihEducation::find($id);
        $del->delete();
        session()->flash('error',trans('admin.Deleted'));
        return back();

           }






// . End Rabih ==========================


    //Transltor
      public function TranslatePage(){
 $items=Transltor::paginate(500);
         return view('admin.Translate',['items'=>$items]);
    }

       public function AddTranslate(){


        $data= $this->validate(request(),[
             'Original_Name'=>'required',
             'New_Arabic_Name'=>'required',


               ],[


         ]);




 if(!empty(request('New_English_Name'))){
    $data['New_English_Name']=request('New_English_Name');
 }else{
   $data['New_English_Name']=request('New_Arabic_Name');

 }


         $data['Original_Name']=request('Original_Name');
         $data['New_Arabic_Name']=request('New_Arabic_Name');



         Transltor::create($data);



             session()->flash('success',trans('admin.NewAddAdmin'));
             return back();

    }

     public function EditTranslate($id){

             $data= $this->validate(request(),[
             'Original_Name'=>'required',
             'New_Arabic_Name'=>'required',


               ],[


         ]);


    $data['New_English_Name']=request('New_English_Name');
         $data['Original_Name']=request('Original_Name');
         $data['New_Arabic_Name']=request('New_Arabic_Name');



           Transltor::where('id',$id)->update($data);



            session()->flash('success',trans('admin.Updated'));
            return back();


     }

     public function DeleteTranslate($id){

        $del=Transltor::find($id);
        $del->delete();
        session()->flash('error',trans('admin.Deleted'));
        return back();

           }


    //QR
         public function QRPage(){
  $item=QR::orderBy('id','desc')->first();
         return view('admin.QR',['item'=>$item]);
    }

        public function QRUpdate(){


    $data['QR']=request('QR');

           QR::orderBy('id','desc')->update($data);

            session()->flash('success',trans('admin.Updated'));
            return back();


     }

    //DeleteMoves
          public function DeleteMoves(){

         return view('admin.DeleteMoves');
    }


       public function PostDeleteMoves(){


           if(request('All') == 1){

Quote::truncate();
ProductsQty::truncate();
ProductUnits::truncate();
ProductsQuote::truncate();
SalesOrder::truncate();
Sales::truncate();
ProductSalesOrder::truncate();
ProductSales::truncate();
StoreCountSales::truncate();
Installment::truncate();
InstallmentDates::truncate();
ProductMoves::truncate();
Journalizing::truncate();
JournalizingDetails::truncate();
GeneralDaily::truncate();
RecivedSales::truncate();
RecivedSalesProducts::truncate();
ReturnSales::truncate();
ReturnSalesProducts::truncate();
IncomChecks::truncate();
ProductsPurchases::truncate();
ProductsStartPeriods::truncate();
ProductsStoresTransfers::truncate();
Shifts::truncate();
QuoteImage::truncate();
ProductQuoteImage::truncate();
PaymentVoucher::truncate();
PaymentVoucherDetails::truncate();
ReciptVoucher::truncate();
ReciptVoucherDetails::truncate();
SalesSubscribes::truncate();
ProductSalesSubscribes::truncate();
StoresMoves::truncate();
StartPeriods::truncate();
ProductsStartPeriods::truncate();
Inventory::truncate();
ProductInventory::truncate();
Settlement::truncate();
ProductSettlement::truncate();
StorsTransfers::truncate();
ProductsStoresTransfers::truncate();
ProductsPurchases::truncate();
ProductSales::truncate();
Consists::truncate();
ProductsConsists::truncate();
UsersMoves::truncate();
PurchasesOrder::truncate();
ProductsPurchasesOrder::truncate();
Purchases::truncate();
ProductsPurchases::truncate();
Shortcomings::truncate();
ProductsShortcomings::truncate();
ReciptMaintaince::truncate();
ProductMaintaincBill::truncate();
MaintaincBill::truncate();
ReturnMaintainceBill::truncate();
ProductsReturnMaintainceBill::truncate();
ManufacturingHalls::truncate();
ManufacturingModel::truncate();
IncomManufacturingModel::truncate();
OutcomManufacturingModel::truncate();
ExecutingReceiving::truncate();
ManuStoreCount::truncate();
ManufacturingOrder::truncate();
ExaminationsTypes::truncate();
ProductsManufacturingOrder::truncate();
ProductsExecutingReceiving::truncate();
ManufacturingRequest::truncate();
ProductsManufacturingRequest::truncate();
ManufacturingExecution::truncate();
Quality::truncate();
QualityDetails::truncate();
ProductManufacturingExecution::truncate();
ReciptsType::truncate();
BonesType::truncate();
CompanyCars::truncate();
CountersType::truncate();
PurchasePetrol::truncate();
ProductsPurchasePetrol::truncate();
ReciptsSalesPetrol::truncate();
ClientSalesPetrol::truncate();
CarsSalesPetrol::truncate();
BonesSalesPetrol::truncate();
WorkersSalesPetrol::truncate();
SalesPetrol::truncate();
SecretariatStores::truncate();
SecretariatQty::truncate();
SecretariatImportGoods::truncate();
ProductsSecretariatImportGoods::truncate();
SecretariatExportGoods::truncate();
ProductsSecretariatExportGoods::truncate();
ShippingType::truncate();
ShippingStatus::truncate();
ShippingOrder::truncate();
Rooms::truncate();
RoomsType::truncate();
Reservations::truncate();
RoomReservations::truncate();
Interviews::truncate();
Projects::truncate();
ProjectTeam::truncate();
Missions::truncate();
OpeningEntries::truncate();
OpeningEntriesDetails::truncate();
AssetsExpenses::truncate();
Assets::truncate();
InsurancePaper::truncate();
           }else{

           if(request('Sales') == 1){
               $items=Sales::all();

            foreach($items as $item){
              $id=$item->id;
                $del=Sales::find($id);

       GeneralDaily::where('Code_Type',$del->Code)->where('Type','المبيعات')->delete();
       Journalizing::where('Code_Type',$del->Code)->where('Type','المبيعات')->delete();
       ProductMoves::where('Bill_Num',$del->Code)->where('Type','مبيعات')->delete();
       StoresMoves::where('ID',$del->id)->where('Type','مبيعات')->delete();
    $Products=ProductSales::where('Sales',$del->id)->where('Qty','!=',0)->get();
      foreach($Products as $prod){

          if($prod->Product()->first()->P_Type == 'Assembly'){

                    $Asembs=AssemblyProducts::where('p_id',$prod->Product)->get();

                    foreach($Asembs as $ass){

                        $Quantity =ProductsQty::
                where('Store',$prod->Store)
                ->where('Product',$ass->Product)
                ->where('P_Code',$ass->P_Code)
                ->first();


if(empty($Quantity)){

             $Quantity =ProductsQty::
                where('Store',$prod->Store)
                ->where('Product',$ass->Product)
                ->where('PP_Code',$ass->P_Code)
                ->first();

if(empty($Quantity)){

           $Quantity =ProductsQty::
                where('Store',$prod->Store)
                ->where('Product',$ass->Product)
                ->where('PPP_Code',$ass->P_Code)
                ->first();


if(empty($Quantity)){

           $Quantity =ProductsQty::
                where('Store',$prod->Store)
                ->where('Product',$ass->Product)
                ->where('PPPP_Code',$ass->P_Code)
                ->first();

}



}






}

            if(!empty($Quantity)){
           $unit=ProductUnits::where('Unit',$prod->Unit)->where('Product',$prod->Product)->first();

           $qq= $unit->Rate * ($prod->Qty * $ass->Qty) ;

           $newqty=abs($Quantity->Qty) -  $qq ;

           ProductsQty::where('id',$Quantity->id)->update(['Qty'=>abs($newqty)]);

                }




            }

         }


      if($prod->Product()->first()->P_Type != 'Service' or $prod->Product()->first()->P_Type != 'Assembly'){
            $unit=ProductUnits::where('Unit',$prod->Unit)->where('Product',$prod->Product)->first();

              $qq= $unit->Rate * $prod->Qty ;

             $PR=ProductsQty::where('Product',$prod->Product)
             ->where('P_Code',$prod->Product_Code)
             ->where('Store',$prod->Store)
             ->first();

          if(empty($PR)){

              $PR=ProductsQty::where('Product',$prod->Product)
             ->where('PP_Code',$prod->Product_Code)
             ->where('Store',$prod->Store)
             ->first();

                    if(empty($PR)){

              $PR=ProductsQty::where('Product',$prod->Product)
             ->where('PPP_Code',$prod->Product_Code)
             ->where('Store',$prod->Store)
             ->first();

                          if(empty($PR)){

              $PR=ProductsQty::where('Product',$prod->Product)
             ->where('PPPP_Code',$prod->Product_Code)
             ->where('Store',$prod->Store)
             ->first();


          }



          }


          }


             if(!empty($PR)){
        $newqty=$PR->Qty + $qq ;

          ProductsQty::where('id',$PR->id)->update(['Qty'=>$newqty]);
             }
      }
      }

   $del->delete();

            }

           }

               if(request('StartPeriod') == 1){
                     $items=StartPeriods::all();

            foreach($items as $item){
               $id=$item->id;
                  $del=StartPeriods::find($id);

         GeneralDaily::where('Code_Type',$del->Code)->where('Type','اصناف بداية فترة')->delete();
         Journalizing::where('Code_Type',$del->Code)->where('Type','اصناف بداية فترة')->delete();
        ProductMoves::where('Bill_Num',$del->Code)->where('Type','بدايه فتره')->delete();
        StoresMoves::where('ID',$del->id)->where('Type','بدايه فتره')->delete();


           $Products=ProductsStartPeriods::where('SP_ID',$del->id)->get();

                 foreach($Products as $prod){

        $unit=ProductUnits::where('Unit',$prod->Unit)->where('Product',$prod->Product)->first();

              $qq= $unit->Rate * $prod->Qty ;

        $PR=ProductsQty::where('Product',$prod->Product)
             ->where('P_Code',$prod->Product_Code)
             ->where('Store',$prod->Store)
             ->first();

          if(empty($PR)){

              $PR=ProductsQty::where('Product',$prod->Product)
             ->where('PP_Code',$prod->Product_Code)
             ->where('Store',$prod->Store)
             ->first();

                    if(empty($PR)){

              $PR=ProductsQty::where('Product',$prod->Product)
             ->where('PPP_Code',$prod->Product_Code)
             ->where('Store',$prod->Store)
             ->first();

                          if(empty($PR)){

              $PR=ProductsQty::where('Product',$prod->Product)
             ->where('PPPP_Code',$prod->Product_Code)
             ->where('Store',$prod->Store)
             ->first();


          }



          }


          }




        $newqty=$PR->Qty - $qq ;

          ProductsQty::where('id',$PR->id)->update(['Qty'=>$newqty]);

      }


        $del->delete();

            }
           }

               if(request('Purchases') == 1){
                     $items=Purchases::all();

            foreach($items as $item){
               $id=$item->id;
                       $del=Purchases::find($id);

       GeneralDaily::where('Code_Type',$del->Code)->where('Type','المشتريات')->delete();
       Journalizing::where('Code_Type',$del->Code)->where('Type','المشتريات')->delete();
       ProductMoves::where('Bill_Num',$del->Code)->where('Type','مشتريات')->delete();
       StoresMoves::where('ID',$del->id)->where('Type','مشتريات')->delete();

    $Products=ProductsPurchases::where('Purchase',$del->id)->get();
      foreach($Products as $prod){

        $unit=ProductUnits::where('Unit',$prod->Unit)->where('Product',$prod->Product)->first();

              $qq= $unit->Rate * $prod->Qty ;

        $PR=ProductsQty::where('Product',$prod->Product)
             ->where('P_Code',$prod->Product_Code)
             ->where('Store',$prod->Store)
             ->first();

          if(empty($PR)){

              $PR=ProductsQty::where('Product',$prod->Product)
             ->where('PP_Code',$prod->Product_Code)
             ->where('Store',$prod->Store)
             ->first();

                    if(empty($PR)){

              $PR=ProductsQty::where('Product',$prod->Product)
             ->where('PPP_Code',$prod->Product_Code)
             ->where('Store',$prod->Store)
             ->first();

                          if(empty($PR)){

              $PR=ProductsQty::where('Product',$prod->Product)
             ->where('PPPP_Code',$prod->Product_Code)
             ->where('Store',$prod->Store)
             ->first();


          }



          }


          }




        $newqty=$PR->Qty - $qq ;

          ProductsQty::where('id',$PR->id)->update(['Qty'=>$newqty]);

      }

 $del->delete();

            }
           }


               if(request('StoresTransfer') == 1){
                     $items=StorsTransfers::all();

            foreach($items as $item){

                $id=$item->id;
                        $del=StorsTransfers::find($id);

       GeneralDaily::where('Code_Type',$del->Code)->where('Type','تحويلات المخازن')->delete();
       Journalizing::where('Code_Type',$del->Code)->where('Type','تحويلات المخازن')->delete();
       ProductMoves::where('Bill_Num',$del->Code)->where('Type','تحويل مخازن')->delete();

   StoresMoves::where('ID',$del->id)->where('Type','تحويلات مخازن من')->delete();
   StoresMoves::where('ID',$del->id)->where('Type','تحويلات مخازن الي')->delete();
    $Products=ProductsStoresTransfers::where('ST_ID',$del->id)->get();
      foreach($Products as $prod){

     $unit=ProductUnits::where('Unit',$prod->Unit)->where('Product',$prod->Product)->first();

              $qq= $unit->Rate * $prod->Trans_Qty ;

        $PR=ProductsQty::where('Product',$prod->Product)
             ->where('P_Code',$prod->P_Code)
             ->where('Store',$prod->To_Store)
             ->first();

        $newqty=$PR->Qty - $qq ;
          ProductsQty::where('id',$PR->id)->update(['Qty'=>$newqty]);

              $PR=ProductsQty::where('Product',$prod->Product)
             ->where('P_Code',$prod->P_Code)
             ->where('Store',$prod->ST_ID()->first()->From_Store)
             ->first();

        $newqty=$PR->Qty + $qq ;
          ProductsQty::where('id',$PR->id)->update(['Qty'=>$newqty]);

      }

$del->delete();

            }
           }

           }


            session()->flash('error',trans('admin.Deleted'));
            return back();


     }


    //Domain Regstration
         public function Domain_Regstration(){
        $items=Tenant::paginate(10);

         return view('admin.Tenants',['items'=>$items]);
    }

     public function AddDomain_Regstration(){

        $data= $this->validate(request(),[
             'name'=>'required',
             'domain'=>'required|unique:domains',
               ],[
            'domain.required' => trans('admin.domainRequired'),
            'domain.unique' =>trans('admin.domainUnique'),
         ]);

         //    'tenancy_db_username' => 'foo',
         //    'tenancy_db_password' => 'bar',
         $tenant3 =Tenant::create(['id' => request('name'),'tenancy_db_name' => request('name')]);

       $tenant3->domains()->create(['domain' => request('domain')]);


             session()->flash('success',trans('admin.Added_Successfully'));
             return back();

    }

     public function DeleteDomain_Regstration($id){

        $del=Tenant::find($id);
        $del->delete();
        session()->flash('error',trans('admin.Deleted'));
        return back();

           }

    //Updates
public function Updates(){

       return view('admin.Updates');

}

     public function NewUpdateMigration(){



             Artisan::call('migrate', [

]);

         // Specific Tenant  '--tenants' => [$tenant->id]
    Artisan::call('tenants:migrate', [
   '--path' => [database_path('migrations/update')],
]);

                session()->flash('success',trans('admin.Updated'));
         return back();
    }

        public function NewUpdateSeeders(){


                 Artisan::call('db:seed', [

        ]);

         // Specific Tenant  '--tenants' => [$tenant->id]
   Artisan::call('tenants:seed', [

    '--class' => 'DatabaseSeeder'
                ]);

 session()->flash('success',trans('admin.Updated'));
            return back();
    }



    //LoginSlider
      public function LoginSlider(){
        $items=LoginSlider::all();
         return view('admin.LoginSlider',['items'=>$items]);
    }

     public function AddLoginSlider(){

        $data= $this->validate(request(),[
             'Image'=>'required|max:100000',
               ],[

            'Image.required' => trans('admin.ImageRequired'),

         ]);

            $image=request()->file('Image');
          if($image){
            $image_name=Str::random(20);
            $ext=strtolower($image->getClientOriginalExtension());
            $image_full_name=$image_name .'.' . $ext ;
            $upload_path='WebsliderImages/';
            $image_url=$upload_path.$image_full_name;
            $success=$image->move($upload_path,$image_full_name);
                   }


         $data['Image']=$image_url;

         LoginSlider::create($data);


             session()->flash('success',trans('admin.Added_Successfully'));
             return back();

    }

     public function EditLoginSlider($id){

              $data= $this->validate(request(),[

             'Image'=>'sometimes|nullable|max:100000',
               ],[

            'Image.required' => trans('admin.ImageRequired'),

         ]);



            $image=request()->file('Image');
          if($image){
            $image_name=Str::random(20);
            $ext=strtolower($image->getClientOriginalExtension());
            $image_full_name=$image_name .'.' . $ext ;
            $upload_path='WebsliderImages/';
            $image_url=$upload_path.$image_full_name;
            $success=$image->move($upload_path,$image_full_name);
                   }

         if(!empty($image_url)){
            $data['Image']=$image_url;
         }else{

             $data['Image']=request('Images');
         }



           LoginSlider::where('id',$id)->update($data);


            session()->flash('success',trans('admin.Updated'));
            return back();


     }

     public function DeleteLoginSlider($id){

        $del=LoginSlider::find($id);

        $del->delete();
        session()->flash('error',trans('admin.Deleted'));
        return back();

           }

    //get weight

   public function getWeight()
    {

       $serial =new PhpSerial;


        // قم بتعيين موقع منفذ الـ USB المناسب لميزان الوزن الرقمي
        $port = 'COM3'; // استبدل بالمنفذ الخاص بميزان الوزن الرقمي الخاص بك

        // قم بفتح المنفذ
        $serial->deviceSet($port,'windows');



         // We can change the baud rate, parity, length, stop bits, flow control
        $serial->confBaudRate(9600);
$serial->confParity("none");
$serial->confCharacterLength(8);
$serial->confStopBits(1);
$serial->confFlowControl("none");

        $serial->deviceOpen();

        // اقرأ البيانات المستلمة من الميزان
        $weight = $serial->readPort();

        // أغلق المنفذ
        $serial->deviceClose();

           dd($weight);
        return response()->json(['weight' => $weight]);


    }



       //Top Menu
            public function OstAdmin(){


            //Sales
              $SalesJan=Sales::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-01-01'),date('Y-02-01')])
            ->where('Status',1)
          ->where('KitchenEnd',1)->where('RecivedOrder',1)
            ->get()->sum('Total_Price');


                      $SalesFeb=Sales::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-02-01'),date('Y-03-01')])
            ->where('Status',1)
          ->where('KitchenEnd',1)->where('RecivedOrder',1)
            ->get()->sum('Total_Price');

                      $SalesMar=Sales::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-03-01'),date('Y-04-01')])
            ->where('Status',1)
          ->where('KitchenEnd',1)->where('RecivedOrder',1)
            ->get()->sum('Total_Price');

                      $SalesApr=Sales::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-04-01'),date('Y-05-01')])
            ->where('Status',1)
          ->where('KitchenEnd',1)->where('RecivedOrder',1)
            ->get()->sum('Total_Price');

                      $SalesMay=Sales::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-05-01'),date('Y-06-01')])
            ->where('Status',1)
          ->where('KitchenEnd',1)->where('RecivedOrder',1)
            ->get()->sum('Total_Price');

                      $SalesJun=Sales::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-06-01'),date('Y-07-01')])
            ->where('Status',1)
          ->where('KitchenEnd',1)->where('RecivedOrder',1)
            ->get()->sum('Total_Price');

                      $SalesJul=Sales::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-07-01'),date('Y-08-01')])
            ->where('Status',1)
          ->where('KitchenEnd',1)->where('RecivedOrder',1)
            ->get()->sum('Total_Price');

                      $SalesAug=Sales::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-08-01'),date('Y-09-01')])
            ->where('Status',1)
          ->where('KitchenEnd',1)->where('RecivedOrder',1)
            ->get()->sum('Total_Price');

                      $SalesSep=Sales::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-09-01'),date('Y-10-01')])
            ->where('Status',1)
          ->where('KitchenEnd',1)->where('RecivedOrder',1)
            ->get()->sum('Total_Price');


                      $SalesOct=Sales::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-10-01'),date('Y-11-01')])
            ->where('Status',1)
          ->where('KitchenEnd',1)->where('RecivedOrder',1)
            ->get()->sum('Total_Price');

                      $SalesNov=Sales::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-11-01'),date('Y-12-01')])
            ->where('Status',1)
          ->where('KitchenEnd',1)->where('RecivedOrder',1)
            ->get()->sum('Total_Price');

                      $SalesDec=Sales::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-12-01'),date('Y-12-31')])
            ->where('Status',1)
          ->where('KitchenEnd',1)->where('RecivedOrder',1)
            ->get()->sum('Total_Price');




              //Purchases
              $PurchJan=Purchases::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-01-01'),date('Y-02-01')])
            ->where('Status',1)
            ->get()->sum('Total_Price');


                      $PurchFeb=Purchases::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-02-01'),date('Y-03-01')])
            ->where('Status',1)
            ->get()->sum('Total_Price');

                      $PurchMar=Purchases::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-03-01'),date('Y-04-01')])
            ->where('Status',1)

            ->get()->sum('Total_Price');

                      $PurchApr=Purchases::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-04-01'),date('Y-05-01')])
            ->where('Status',1)

            ->get()->sum('Total_Price');

                      $PurchMay=Purchases::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-05-01'),date('Y-06-01')])
            ->where('Status',1)

            ->get()->sum('Total_Price');

                      $PurchJun=Purchases::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-06-01'),date('Y-07-01')])
            ->where('Status',1)

            ->get()->sum('Total_Price');

                      $PurchJul=Purchases::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-07-01'),date('Y-08-01')])
            ->where('Status',1)
            ->get()->sum('Total_Price');

                      $PurchAug=Purchases::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-08-01'),date('Y-09-01')])
            ->where('Status',1)

            ->get()->sum('Total_Price');

                      $PurchSep=Purchases::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-09-01'),date('Y-10-01')])
            ->where('Status',1)
            ->get()->sum('Total_Price');


                      $PurchOct=Purchases::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-10-01'),date('Y-11-01')])
            ->where('Status',1)
            ->get()->sum('Total_Price');

                      $PurchNov=Purchases::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-11-01'),date('Y-12-01')])
            ->where('Status',1)
            ->get()->sum('Total_Price');

                      $PurchDec=Purchases::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-12-01'),date('Y-12-31')])
            ->where('Status',1)
            ->get()->sum('Total_Price');



            //ReturnSales
                   $ReturnSalesJan=ReturnSales::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-01-01'),date('Y-02-01')])

            ->get()->sum('Total_Return_Value');


                      $ReturnSalesFeb=ReturnSales::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-02-01'),date('Y-03-01')])

            ->get()->sum('Total_Return_Value');

                      $ReturnSalesMar=ReturnSales::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-03-01'),date('Y-04-01')])

            ->get()->sum('Total_Return_Value');

                      $ReturnSalesApr=ReturnSales::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-04-01'),date('Y-05-01')])


            ->get()->sum('Total_Return_Value');

                      $ReturnSalesMay=ReturnSales::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-05-01'),date('Y-06-01')])


            ->get()->sum('Total_Return_Value');

                      $ReturnSalesJun=ReturnSales::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-06-01'),date('Y-07-01')])


            ->get()->sum('Total_Return_Value');

                      $ReturnSalesJul=ReturnSales::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-07-01'),date('Y-08-01')])

            ->get()->sum('Total_Return_Value');

                      $ReturnSalesAug=ReturnSales::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-08-01'),date('Y-09-01')])


            ->get()->sum('Total_Return_Value');

                      $ReturnSalesSep=ReturnSales::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-09-01'),date('Y-10-01')])

            ->get()->sum('Total_Return_Value');


                      $ReturnSalesOct=ReturnSales::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-10-01'),date('Y-11-01')])

            ->get()->sum('Total_Return_Value');

                      $ReturnSalesNov=ReturnSales::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-11-01'),date('Y-12-01')])

            ->get()->sum('Total_Return_Value');

                      $ReturnSalesDec=ReturnSales::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-12-01'),date('Y-12-31')])

            ->get()->sum('Total_Return_Value');


//ReturnPurch
                 $ReturnPurchJan=ReturnPurch::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-01-01'),date('Y-02-01')])

            ->get()->sum('Total_Return_Value');


                      $ReturnPurchFeb=ReturnPurch::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-02-01'),date('Y-03-01')])

            ->get()->sum('Total_Return_Value');

                      $ReturnPurchMar=ReturnPurch::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-03-01'),date('Y-04-01')])

            ->get()->sum('Total_Return_Value');

                      $ReturnPurchApr=ReturnPurch::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-04-01'),date('Y-05-01')])


            ->get()->sum('Total_Return_Value');

                      $ReturnPurchMay=ReturnPurch::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-05-01'),date('Y-06-01')])


            ->get()->sum('Total_Return_Value');

                      $ReturnPurchJun=ReturnPurch::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-06-01'),date('Y-07-01')])


            ->get()->sum('Total_Return_Value');

                      $ReturnPurchJul=ReturnPurch::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-07-01'),date('Y-08-01')])

            ->get()->sum('Total_Return_Value');

                      $ReturnPurchAug=ReturnPurch::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-08-01'),date('Y-09-01')])


            ->get()->sum('Total_Return_Value');

                      $ReturnPurchSep=ReturnPurch::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-09-01'),date('Y-10-01')])

            ->get()->sum('Total_Return_Value');


                      $ReturnPurchOct=ReturnPurch::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-10-01'),date('Y-11-01')])

            ->get()->sum('Total_Return_Value');

                      $ReturnPurchNov=ReturnPurch::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-11-01'),date('Y-12-01')])

            ->get()->sum('Total_Return_Value');

                      $ReturnPurchDec=ReturnPurch::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-12-01'),date('Y-12-31')])

            ->get()->sum('Total_Return_Value');


            //Expenses
                             $AMsrfoat=AcccountingManual::where('id',20)->first();

        if($AMsrfoat->Parent == 0){

          $wordsss=$AMsrfoat->Code.'0';

        }else{

           $wordsss=$AMsrfoat->Code;

        }





                    $TotalDebMasrofatJan = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {

            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->whereBetween('general_dailies.Date',[date('Y-01-01'),date('Y-02-01')]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordsss.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Debitor_Coin');



        $TotalCredMasrofatJan = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {

            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->whereBetween('general_dailies.Date',[date('Y-01-01'),date('Y-02-01')]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordsss.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Creditor_Coin');



            $EgmalyMasrofatJan=  $TotalDebMasrofatJan -  $TotalCredMasrofatJan ;



                $TotalDebMasrofatFeb = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {

            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->whereBetween('general_dailies.Date',[date('Y-02-01'),date('Y-03-01')]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordsss.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Debitor_Coin');



        $TotalCredMasrofatFeb = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {

            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->whereBetween('general_dailies.Date',[date('Y-02-01'),date('Y-03-01')]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordsss.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Creditor_Coin');



            $EgmalyMasrofatFeb=  $TotalDebMasrofatFeb -  $TotalCredMasrofatFeb ;



                   $TotalDebMasrofatMar = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {

            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->whereBetween('general_dailies.Date',[date('Y-03-01'),date('Y-04-01')]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordsss.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Debitor_Coin');



        $TotalCredMasrofatMar = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {

            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->whereBetween('general_dailies.Date',[date('Y-03-01'),date('Y-04-01')]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordsss.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Creditor_Coin');



            $EgmalyMasrofatMar=  $TotalDebMasrofatMar -  $TotalCredMasrofatMar ;



                             $TotalDebMasrofatApr = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {

            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->whereBetween('general_dailies.Date',[date('Y-04-01'),date('Y-05-01')]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordsss.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Debitor_Coin');



        $TotalCredMasrofatApr = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {

            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->whereBetween('general_dailies.Date',[date('Y-04-01'),date('Y-05-01')]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordsss.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Creditor_Coin');



            $EgmalyMasrofatApr=  $TotalDebMasrofatApr -  $TotalCredMasrofatApr ;



                                        $TotalDebMasrofatMay = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {

            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->whereBetween('general_dailies.Date',[date('Y-05-01'),date('Y-06-01')]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordsss.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Debitor_Coin');



        $TotalCredMasrofatMay = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {

            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->whereBetween('general_dailies.Date',[date('Y-05-01'),date('Y-06-01')]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordsss.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Creditor_Coin');



            $EgmalyMasrofatMay=  $TotalDebMasrofatMay -  $TotalCredMasrofatMay ;



                       $TotalDebMasrofatJun = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {

            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->whereBetween('general_dailies.Date',[date('Y-06-01'),date('Y-07-01')]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordsss.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Debitor_Coin');



        $TotalCredMasrofatJun = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {

            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->whereBetween('general_dailies.Date',[date('Y-06-01'),date('Y-07-01')]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordsss.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Creditor_Coin');



            $EgmalyMasrofatJun=  $TotalDebMasrofatJun -  $TotalCredMasrofatJun ;


                    $TotalDebMasrofatJul = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {

            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->whereBetween('general_dailies.Date',[date('Y-07-01'),date('Y-08-01')]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordsss.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Debitor_Coin');



        $TotalCredMasrofatJul = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {

            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->whereBetween('general_dailies.Date',[date('Y-07-01'),date('Y-08-01')]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordsss.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Creditor_Coin');



            $EgmalyMasrofatJul=  $TotalDebMasrofatJul -  $TotalCredMasrofatJul ;




                    $TotalDebMasrofatAug = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {

            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->whereBetween('general_dailies.Date',[date('Y-08-01'),date('Y-09-01')]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordsss.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Debitor_Coin');



        $TotalCredMasrofatAug = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {

            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->whereBetween('general_dailies.Date',[date('Y-08-01'),date('Y-09-01')]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordsss.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Creditor_Coin');



            $EgmalyMasrofatAug=  $TotalDebMasrofatAug -  $TotalCredMasrofatAug ;


                $TotalDebMasrofatSep = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {

            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->whereBetween('general_dailies.Date',[date('Y-09-01'),date('Y-10-01')]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordsss.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Debitor_Coin');



        $TotalCredMasrofatSep = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {

            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->whereBetween('general_dailies.Date',[date('Y-09-01'),date('Y-10-01')]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordsss.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Creditor_Coin');



            $EgmalyMasrofatSep=  $TotalDebMasrofatSep -  $TotalCredMasrofatSep ;




                $TotalDebMasrofatOct = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {

            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->whereBetween('general_dailies.Date',[date('Y-10-01'),date('Y-11-01')]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordsss.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Debitor_Coin');



        $TotalCredMasrofatOct = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {

            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->whereBetween('general_dailies.Date',[date('Y-10-01'),date('Y-11-01')]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordsss.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Creditor_Coin');



            $EgmalyMasrofatOct=  $TotalDebMasrofatOct -  $TotalCredMasrofatOct ;




                $TotalDebMasrofatNov = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {

            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->whereBetween('general_dailies.Date',[date('Y-11-01'),date('Y-12-01')]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordsss.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Debitor_Coin');



        $TotalCredMasrofatNov = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {

            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->whereBetween('general_dailies.Date',[date('Y-11-01'),date('Y-12-01')]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordsss.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Creditor_Coin');



            $EgmalyMasrofatNov=  $TotalDebMasrofatNov -  $TotalCredMasrofatNov ;



              $TotalDebMasrofatDec = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {

            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->whereBetween('general_dailies.Date',[date('Y-12-01'),date('Y-12-31')]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordsss.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Debitor_Coin');



        $TotalCredMasrofatDec = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {

            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->whereBetween('general_dailies.Date',[date('Y-12-01'),date('Y-12-31')]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordsss.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Creditor_Coin');



            $EgmalyMasrofatDec=  $TotalDebMasrofatDec -  $TotalCredMasrofatDec ;



            //Vouchers
        $PaymentVoucherJan=GeneralDaily::whereBetween('Date',[date('Y-01-01'),date('Y-02-01')])
          ->where('Type','سند صرف')
          ->get()->sum('Debitor_Coin');


    $ReciptVoucherJan=GeneralDaily::whereBetween('Date',[date('Y-01-01'),date('Y-02-01')])
        ->where('Type','سند قبض')
          ->get()->sum('Creditor_Coin');


                $PaymentVoucherFeb=GeneralDaily::whereBetween('Date',[date('Y-02-01'),date('Y-03-01')])
          ->where('Type','سند صرف')
          ->get()->sum('Debitor_Coin');


    $ReciptVoucherFeb=GeneralDaily::whereBetween('Date',[date('Y-02-01'),date('Y-03-01')])
        ->where('Type','سند قبض')
          ->get()->sum('Creditor_Coin');



                    $PaymentVoucherMar=GeneralDaily::whereBetween('Date',[date('Y-03-01'),date('Y-04-01')])
          ->where('Type','سند صرف')
          ->get()->sum('Debitor_Coin');


    $ReciptVoucherMar=GeneralDaily::whereBetween('Date',[date('Y-03-01'),date('Y-04-01')])
        ->where('Type','سند قبض')
          ->get()->sum('Creditor_Coin');




                    $PaymentVoucherApr=GeneralDaily::whereBetween('Date',[date('Y-04-01'),date('Y-05-01')])
          ->where('Type','سند صرف')
          ->get()->sum('Debitor_Coin');


    $ReciptVoucherApr=GeneralDaily::whereBetween('Date',[date('Y-04-01'),date('Y-05-01')])
        ->where('Type','سند قبض')
          ->get()->sum('Creditor_Coin');



                    $PaymentVoucherMay=GeneralDaily::whereBetween('Date',[date('Y-05-01'),date('Y-06-01')])
          ->where('Type','سند صرف')
          ->get()->sum('Debitor_Coin');


    $ReciptVoucherMay=GeneralDaily::whereBetween('Date',[date('Y-05-01'),date('Y-06-01')])
        ->where('Type','سند قبض')
          ->get()->sum('Creditor_Coin');



                    $PaymentVoucherJun=GeneralDaily::whereBetween('Date',[date('Y-06-01'),date('Y-07-01')])
          ->where('Type','سند صرف')
          ->get()->sum('Debitor_Coin');


    $ReciptVoucherJun=GeneralDaily::whereBetween('Date',[date('Y-06-01'),date('Y-07-01')])
        ->where('Type','سند قبض')
          ->get()->sum('Creditor_Coin');



                    $PaymentVoucherJul=GeneralDaily::whereBetween('Date',[date('Y-07-01'),date('Y-08-01')])
          ->where('Type','سند صرف')
          ->get()->sum('Debitor_Coin');


    $ReciptVoucherJul=GeneralDaily::whereBetween('Date',[date('Y-07-01'),date('Y-08-01')])
        ->where('Type','سند قبض')
          ->get()->sum('Creditor_Coin');


                    $PaymentVoucherAug=GeneralDaily::whereBetween('Date',[date('Y-08-01'),date('Y-09-01')])
          ->where('Type','سند صرف')
          ->get()->sum('Debitor_Coin');


    $ReciptVoucherAug=GeneralDaily::whereBetween('Date',[date('Y-08-01'),date('Y-09-01')])
        ->where('Type','سند قبض')
          ->get()->sum('Creditor_Coin');



                        $PaymentVoucherSep=GeneralDaily::whereBetween('Date',[date('Y-09-01'),date('Y-10-01')])
          ->where('Type','سند صرف')
          ->get()->sum('Debitor_Coin');


    $ReciptVoucherSep=GeneralDaily::whereBetween('Date',[date('Y-09-01'),date('Y-10-01')])
        ->where('Type','سند قبض')
          ->get()->sum('Creditor_Coin');


    $PaymentVoucherOct=GeneralDaily::whereBetween('Date',[date('Y-10-01'),date('Y-11-01')])
          ->where('Type','سند صرف')
          ->get()->sum('Debitor_Coin');


    $ReciptVoucherOct=GeneralDaily::whereBetween('Date',[date('Y-10-01'),date('Y-11-01')])
        ->where('Type','سند قبض')
          ->get()->sum('Creditor_Coin');


        $PaymentVoucherNov=GeneralDaily::whereBetween('Date',[date('Y-11-01'),date('Y-12-01')])
          ->where('Type','سند صرف')
          ->get()->sum('Debitor_Coin');


    $ReciptVoucherNov=GeneralDaily::whereBetween('Date',[date('Y-11-01'),date('Y-12-01')])
        ->where('Type','سند قبض')
          ->get()->sum('Creditor_Coin');


                $PaymentVoucherDec=GeneralDaily::whereBetween('Date',[date('Y-12-01'),date('Y-12-31')])
          ->where('Type','سند صرف')
          ->get()->sum('Debitor_Coin');


    $ReciptVoucherDec=GeneralDaily::whereBetween('Date',[date('Y-12-01'),date('Y-12-31')])
        ->where('Type','سند قبض')
          ->get()->sum('Creditor_Coin');


            //Checks
            $IncomChecksJan=IncomChecks::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-01-01'),date('Y-02-01')])
            ->get()->sum('Amount');

   $ExportChecksJan=ExportChecks::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-01-01'),date('Y-02-01')])
            ->get()->sum('Amount');

                        $IncomChecksFeb=IncomChecks::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-02-01'),date('Y-03-01')])
            ->get()->sum('Amount');

   $ExportChecksFeb=ExportChecks::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-02-01'),date('Y-03-01')])
            ->get()->sum('Amount');


                $IncomChecksMar=IncomChecks::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-03-01'),date('Y-04-01')])
            ->get()->sum('Amount');

   $ExportChecksMar=ExportChecks::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-03-01'),date('Y-04-01')])
            ->get()->sum('Amount');


                $IncomChecksApr=IncomChecks::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-04-01'),date('Y-05-01')])
            ->get()->sum('Amount');

   $ExportChecksApr=ExportChecks::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-04-01'),date('Y-05-01')])
            ->get()->sum('Amount');


                $IncomChecksMay=IncomChecks::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-05-01'),date('Y-06-01')])
            ->get()->sum('Amount');

   $ExportChecksMay=ExportChecks::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-05-01'),date('Y-06-01')])
            ->get()->sum('Amount');


            $IncomChecksJun=IncomChecks::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-06-01'),date('Y-07-01')])
            ->get()->sum('Amount');

   $ExportChecksJun=ExportChecks::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-06-01'),date('Y-07-01')])
            ->get()->sum('Amount');


                  $IncomChecksJul=IncomChecks::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-07-01'),date('Y-08-01')])
            ->get()->sum('Amount');

   $ExportChecksJul=ExportChecks::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-07-01'),date('Y-08-01')])
            ->get()->sum('Amount');


                  $IncomChecksAug=IncomChecks::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-08-01'),date('Y-09-01')])
            ->get()->sum('Amount');

   $ExportChecksAug=ExportChecks::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-08-01'),date('Y-09-01')])
            ->get()->sum('Amount');


                          $IncomChecksSep=IncomChecks::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-09-01'),date('Y-10-01')])
            ->get()->sum('Amount');

   $ExportChecksSep=ExportChecks::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-09-01'),date('Y-10-01')])
            ->get()->sum('Amount');



                        $IncomChecksOct=IncomChecks::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-10-01'),date('Y-11-01')])
            ->get()->sum('Amount');

   $ExportChecksOct=ExportChecks::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-10-01'),date('Y-11-01')])
            ->get()->sum('Amount');


                              $IncomChecksNov=IncomChecks::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-11-01'),date('Y-12-01')])
            ->get()->sum('Amount');

   $ExportChecksNov=ExportChecks::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-11-01'),date('Y-12-01')])
            ->get()->sum('Amount');


                                  $IncomChecksDec=IncomChecks::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-12-01'),date('Y-12-31')])
            ->get()->sum('Amount');

   $ExportChecksDec=ExportChecks::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-12-01'),date('Y-12-31')])
            ->get()->sum('Amount');



$datesat = date("Y-m-d",strtotime('-2 day monday this week'));
$datesun = date("Y-m-d",strtotime('-1 day monday this week'));
$datemon = date("Y-m-d",strtotime('monday this week'));
$datetue = date("Y-m-d",strtotime("tuesday this week"));
$datewed = date("Y-m-d",strtotime("wednesday this week"));
$datethu = date("Y-m-d",strtotime("thursday this week"));
$datefri = date("Y-m-d",strtotime("friday this week"));


            //Sales
             $SalesSat=Sales::orderBy('id','desc')
            ->where('Date',$datesat)
            ->where('Status',1)
          ->where('KitchenEnd',1)->where('RecivedOrder',1)
            ->get()->sum('Total_Price');


                   $SalesSun=Sales::orderBy('id','desc')
            ->where('Date',$datesun)
            ->where('Status',1)
          ->where('KitchenEnd',1)->where('RecivedOrder',1)
            ->get()->sum('Total_Price');

                   $SalesMon=Sales::orderBy('id','desc')
            ->where('Date',$datemon)
            ->where('Status',1)
          ->where('KitchenEnd',1)->where('RecivedOrder',1)
            ->get()->sum('Total_Price');

                   $SalesTue=Sales::orderBy('id','desc')
            ->where('Date',$datetue)
            ->where('Status',1)
          ->where('KitchenEnd',1)->where('RecivedOrder',1)
            ->get()->sum('Total_Price');

                   $SalesWed=Sales::orderBy('id','desc')
            ->where('Date',$datewed)
            ->where('Status',1)
          ->where('KitchenEnd',1)->where('RecivedOrder',1)
            ->get()->sum('Total_Price');

                   $SalesThr=Sales::orderBy('id','desc')
            ->where('Date',$datethu)
            ->where('Status',1)
          ->where('KitchenEnd',1)->where('RecivedOrder',1)
            ->get()->sum('Total_Price');

                   $SalesFri=Sales::orderBy('id','desc')
            ->where('Date',$datefri)
            ->where('Status',1)
          ->where('KitchenEnd',1)->where('RecivedOrder',1)
            ->get()->sum('Total_Price');



                //Purchases
             $PurchSat=Purchases::orderBy('id','desc')
            ->where('Date',$datesat)
            ->where('Status',1)

            ->get()->sum('Total_Price');


                   $PurchSun=Purchases::orderBy('id','desc')
            ->where('Date',$datesun)
            ->where('Status',1)

            ->get()->sum('Total_Price');

                   $PurchMon=Purchases::orderBy('id','desc')
            ->where('Date',$datemon)
            ->where('Status',1)

            ->get()->sum('Total_Price');

                   $PurchTue=Purchases::orderBy('id','desc')
            ->where('Date',$datetue)
            ->where('Status',1)

            ->get()->sum('Total_Price');

                   $PurchWed=Purchases::orderBy('id','desc')
            ->where('Date',$datewed)
            ->where('Status',1)

            ->get()->sum('Total_Price');

                   $PurchThr=Purchases::orderBy('id','desc')
            ->where('Date',$datethu)
            ->where('Status',1)

            ->get()->sum('Total_Price');

                   $PurchFri=Purchases::orderBy('id','desc')
            ->where('Date',$datefri)
            ->where('Status',1)

            ->get()->sum('Total_Price');


                //ReturnSales
               $ReturnSalesSat=ReturnSales::orderBy('id','desc')
            ->where('Date',$datesat)


            ->get()->sum('Total_Return_Value');


                   $ReturnSalesSun=ReturnSales::orderBy('id','desc')
            ->where('Date',$datesun)


            ->get()->sum('Total_Return_Value');

                   $ReturnSalesMon=ReturnSales::orderBy('id','desc')
            ->where('Date',$datemon)


            ->get()->sum('Total_Return_Value');

                   $ReturnSalesTue=ReturnSales::orderBy('id','desc')
            ->where('Date',$datetue)


            ->get()->sum('Total_Return_Value');

                   $ReturnSalesWed=ReturnSales::orderBy('id','desc')
            ->where('Date',$datewed)


            ->get()->sum('Total_Return_Value');

                   $ReturnSalesThr=ReturnSales::orderBy('id','desc')
            ->where('Date',$datethu)


            ->get()->sum('Total_Return_Value');

                   $ReturnSalesFri=ReturnSales::orderBy('id','desc')
            ->where('Date',$datefri)


            ->get()->sum('Total_Return_Value');

//ReturnPurch
              $ReturnPurchSat=ReturnPurch::orderBy('id','desc')
            ->where('Date',$datesat)


            ->get()->sum('Total_Return_Value');


                   $ReturnPurchSun=ReturnPurch::orderBy('id','desc')
            ->where('Date',$datesun)


            ->get()->sum('Total_Return_Value');

                   $ReturnPurchMon=ReturnPurch::orderBy('id','desc')
            ->where('Date',$datemon)


            ->get()->sum('Total_Return_Value');

                   $ReturnPurchTue=ReturnPurch::orderBy('id','desc')
            ->where('Date',$datetue)


            ->get()->sum('Total_Return_Value');

                   $ReturnPurchWed=ReturnPurch::orderBy('id','desc')
            ->where('Date',$datewed)


            ->get()->sum('Total_Return_Value');

                   $ReturnPurchThr=ReturnPurch::orderBy('id','desc')
            ->where('Date',$datethu)


            ->get()->sum('Total_Return_Value');

                   $ReturnPurchFri=ReturnPurch::orderBy('id','desc')
            ->where('Date',$datefri)


            ->get()->sum('Total_Return_Value');



            //Vouchers


        $PaymentVoucherSat=GeneralDaily::where('Date',$datesat)
          ->where('Type','سند صرف')
          ->get()->sum('Debitor_Coin');


    $ReciptVoucherSat=GeneralDaily::where('Date',$datesat)
        ->where('Type','سند قبض')
          ->get()->sum('Creditor_Coin');


            $PaymentVoucherSun=GeneralDaily::where('Date',$datesun)
          ->where('Type','سند صرف')
          ->get()->sum('Debitor_Coin');


    $ReciptVoucherSun=GeneralDaily::where('Date',$datesun)
        ->where('Type','سند قبض')
          ->get()->sum('Creditor_Coin');


                $PaymentVoucherMon=GeneralDaily::where('Date',$datemon)
          ->where('Type','سند صرف')
          ->get()->sum('Debitor_Coin');


    $ReciptVoucherMon=GeneralDaily::where('Date',$datemon)
        ->where('Type','سند قبض')
          ->get()->sum('Creditor_Coin');



                    $PaymentVoucherTue=GeneralDaily::where('Date',$datetue)
          ->where('Type','سند صرف')
          ->get()->sum('Debitor_Coin');


    $ReciptVoucherTue=GeneralDaily::where('Date',$datetue)
        ->where('Type','سند قبض')
          ->get()->sum('Creditor_Coin');


                 $PaymentVoucherWed=GeneralDaily::where('Date',$datewed)
          ->where('Type','سند صرف')
          ->get()->sum('Debitor_Coin');


    $ReciptVoucherWed=GeneralDaily::where('Date',$datewed)
        ->where('Type','سند قبض')
          ->get()->sum('Creditor_Coin');


    $PaymentVoucherThr=GeneralDaily::where('Date',$datethu)
          ->where('Type','سند صرف')
          ->get()->sum('Debitor_Coin');


    $ReciptVoucherThr=GeneralDaily::where('Date',$datethu)
        ->where('Type','سند قبض')
          ->get()->sum('Creditor_Coin');



    $PaymentVoucherFri=GeneralDaily::where('Date',$datefri)
          ->where('Type','سند صرف')
          ->get()->sum('Debitor_Coin');


    $ReciptVoucherFri=GeneralDaily::where('Date',$datefri)
        ->where('Type','سند قبض')
          ->get()->sum('Creditor_Coin');





            //Checks
        $IncomChecksSat=IncomChecks::orderBy('id','desc')
            ->where('Date',$datesat)
            ->get()->sum('Amount');

   $ExportChecksSat=ExportChecks::orderBy('id','desc')
            ->where('Date',$datesat)
            ->get()->sum('Amount');


            $IncomChecksSun=IncomChecks::orderBy('id','desc')
            ->where('Date',$datesun)
            ->get()->sum('Amount');

   $ExportChecksSun=ExportChecks::orderBy('id','desc')
            ->where('Date',$datesun)
            ->get()->sum('Amount');



           $IncomChecksMon=IncomChecks::orderBy('id','desc')
            ->where('Date',$datemon)
            ->get()->sum('Amount');

   $ExportChecksMon=ExportChecks::orderBy('id','desc')
            ->where('Date',$datemon)
            ->get()->sum('Amount');


       $IncomChecksTue=IncomChecks::orderBy('id','desc')
            ->where('Date',$datetue)
            ->get()->sum('Amount');

   $ExportChecksTue=ExportChecks::orderBy('id','desc')
            ->where('Date',$datetue)
            ->get()->sum('Amount');


           $IncomChecksWed=IncomChecks::orderBy('id','desc')
            ->where('Date',$datewed)
            ->get()->sum('Amount');

   $ExportChecksWed=ExportChecks::orderBy('id','desc')
            ->where('Date',$datewed)
            ->get()->sum('Amount');


         $IncomChecksThr=IncomChecks::orderBy('id','desc')
            ->where('Date',$datethu)
            ->get()->sum('Amount');

   $ExportChecksThr=ExportChecks::orderBy('id','desc')
            ->where('Date',$datethu)
            ->get()->sum('Amount');



                $IncomChecksFri=IncomChecks::orderBy('id','desc')
            ->where('Date',$datefri)
            ->get()->sum('Amount');

   $ExportChecksFri=ExportChecks::orderBy('id','desc')
            ->where('Date',$datefri)
            ->get()->sum('Amount');




            //Expenses

              $TotalDebMasrofatSat = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {
                 $datesat = date("Y-m-d",strtotime('-2 day monday this week'));
            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->where('general_dailies.Date',$datesat);
        })

          ->where('acccounting_manuals.Code', 'like', $wordsss.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Debitor_Coin');



        $TotalCredMasrofatSat = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {
                 $datesat = date("Y-m-d",strtotime('-2 day monday this week'));
            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->where('general_dailies.Date',$datesat);
        })

          ->where('acccounting_manuals.Code', 'like', $wordsss.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Creditor_Coin');



            $EgmalyMasrofatSat=  $TotalDebMasrofatSat -  $TotalCredMasrofatSat ;



               $TotalDebMasrofatSun = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {
           $datesun = date("Y-m-d",strtotime('-1 day monday this week'));
            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->where('general_dailies.Date',$datesun);
        })

          ->where('acccounting_manuals.Code', 'like', $wordsss.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Debitor_Coin');



        $TotalCredMasrofatSun = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {
           $datesun = date("Y-m-d",strtotime('-1 day monday this week'));
            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->where('general_dailies.Date',$datesun);
        })

          ->where('acccounting_manuals.Code', 'like', $wordsss.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Creditor_Coin');



            $EgmalyMasrofatSun=  $TotalDebMasrofatSun -  $TotalCredMasrofatSun ;



               $TotalDebMasrofatMon = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {
           $datemon = date("Y-m-d",strtotime('monday this week'));
            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->where('general_dailies.Date',$datemon);
        })

          ->where('acccounting_manuals.Code', 'like', $wordsss.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Debitor_Coin');



        $TotalCredMasrofatMon = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {
           $datemon = date("Y-m-d",strtotime('monday this week'));
            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->where('general_dailies.Date',$datemon);
        })

          ->where('acccounting_manuals.Code', 'like', $wordsss.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Creditor_Coin');



            $EgmalyMasrofatMon=  $TotalDebMasrofatMon -  $TotalCredMasrofatMon ;



                 $TotalDebMasrofatTue = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {
           $datetue = date("Y-m-d",strtotime("tuesday this week"));
            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->where('general_dailies.Date',$datetue);
        })

          ->where('acccounting_manuals.Code', 'like', $wordsss.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Debitor_Coin');



        $TotalCredMasrofatTue = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {
           $datetue = date("Y-m-d",strtotime("tuesday this week"));
            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->where('general_dailies.Date',$datetue);
        })

          ->where('acccounting_manuals.Code', 'like', $wordsss.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Creditor_Coin');



            $EgmalyMasrofatTue=  $TotalDebMasrofatTue -  $TotalCredMasrofatTue ;


                    $TotalDebMasrofatWed = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {
           $datewed = date("Y-m-d",strtotime("wednesday this week"));
            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->where('general_dailies.Date',$datewed);
        })

          ->where('acccounting_manuals.Code', 'like', $wordsss.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Debitor_Coin');



        $TotalCredMasrofatWed = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {
           $datewed = date("Y-m-d",strtotime("wednesday this week"));
            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->where('general_dailies.Date',$datewed);
        })

          ->where('acccounting_manuals.Code', 'like', $wordsss.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Creditor_Coin');



            $EgmalyMasrofatWed=  $TotalDebMasrofatWed -  $TotalCredMasrofatWed ;


                      $TotalDebMasrofatThr = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {
           $datethu = date("Y-m-d",strtotime("thursday this week"));
            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->where('general_dailies.Date',$datethu);
        })

          ->where('acccounting_manuals.Code', 'like', $wordsss.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Debitor_Coin');



        $TotalCredMasrofatThr = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {
           $datethu = date("Y-m-d",strtotime("thursday this week"));
            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->where('general_dailies.Date',$datethu);
        })

          ->where('acccounting_manuals.Code', 'like', $wordsss.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Creditor_Coin');



            $EgmalyMasrofatThr=  $TotalDebMasrofatThr -  $TotalCredMasrofatThr ;





             $TotalDebMasrofatFri = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {
           $datefri = date("Y-m-d",strtotime("friday this week"));
            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->where('general_dailies.Date',$datefri);
        })

          ->where('acccounting_manuals.Code', 'like', $wordsss.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Debitor_Coin');



        $TotalCredMasrofatFri = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {
           $datefri = date("Y-m-d",strtotime("friday this week"));
            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->where('general_dailies.Date',$datefri);
        })

          ->where('acccounting_manuals.Code', 'like', $wordsss.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Creditor_Coin');



            $EgmalyMasrofatFri=  $TotalDebMasrofatFri -  $TotalCredMasrofatFri ;




        $Y1F=date("Y-01-01",strtotime("-1 year"));
        $Y1T=date("Y-12-31",strtotime("-1 year"));


       $Y2F=date("Y-01-01",strtotime("-2 year"));
        $Y2T=date("Y-12-31",strtotime("-2 year"));


           $Y3F=date("Y-01-01",strtotime("-3 year"));
        $Y3T=date("Y-12-31",strtotime("-3 year"));


               $Y4F=date("Y-01-01",strtotime("-4 year"));
        $Y4T=date("Y-12-31",strtotime("-4 year"));



        $YCF=date("Y-01-01");
        $YCT=date("Y-12-31");



            //Sales
                      $SalesPrevY1=Sales::orderBy('id','desc')
            ->whereBetween('Date',[$Y1F,$Y1T])
            ->where('Status',1)
          ->where('KitchenEnd',1)->where('RecivedOrder',1)
            ->get()->sum('Total_Price');


                           $SalesPrevY2=Sales::orderBy('id','desc')
            ->whereBetween('Date',[$Y2F,$Y2T])
            ->where('Status',1)
          ->where('KitchenEnd',1)->where('RecivedOrder',1)
            ->get()->sum('Total_Price');


                           $SalesPrevY3=Sales::orderBy('id','desc')
            ->whereBetween('Date',[$Y3F,$Y3T])
            ->where('Status',1)
          ->where('KitchenEnd',1)->where('RecivedOrder',1)
            ->get()->sum('Total_Price');

                           $SalesPrevY4=Sales::orderBy('id','desc')
            ->whereBetween('Date',[$Y4F,$Y4T])
            ->where('Status',1)
          ->where('KitchenEnd',1)->where('RecivedOrder',1)
            ->get()->sum('Total_Price');

                          $SalesPrevYC=Sales::orderBy('id','desc')
            ->whereBetween('Date',[$YCF,$YCT])
            ->where('Status',1)
          ->where('KitchenEnd',1)->where('RecivedOrder',1)
            ->get()->sum('Total_Price');



             //Purchases
                      $PurchPrevY1=Purchases::orderBy('id','desc')
            ->whereBetween('Date',[$Y1F,$Y1T])
            ->where('Status',1)

            ->get()->sum('Total_Price');


                           $PurchPrevY2=Purchases::orderBy('id','desc')
            ->whereBetween('Date',[$Y2F,$Y2T])
            ->where('Status',1)

            ->get()->sum('Total_Price');


                           $PurchPrevY3=Purchases::orderBy('id','desc')
            ->whereBetween('Date',[$Y3F,$Y3T])
            ->where('Status',1)

            ->get()->sum('Total_Price');

                           $PurchPrevY4=Purchases::orderBy('id','desc')
            ->whereBetween('Date',[$Y4F,$Y4T])
            ->where('Status',1)

            ->get()->sum('Total_Price');

                          $PurchPrevYC=Purchases::orderBy('id','desc')
            ->whereBetween('Date',[$YCF,$YCT])
            ->where('Status',1)

            ->get()->sum('Total_Price');


                //ReturnSales
                  $ReturnSalesPrevY1=ReturnSales::orderBy('id','desc')
            ->whereBetween('Date',[$Y1F,$Y1T])


            ->get()->sum('Total_Return_Value');


                           $ReturnSalesPrevY2=ReturnSales::orderBy('id','desc')
            ->whereBetween('Date',[$Y2F,$Y2T])


            ->get()->sum('Total_Return_Value');


                           $ReturnSalesPrevY3=ReturnSales::orderBy('id','desc')
            ->whereBetween('Date',[$Y3F,$Y3T])


            ->get()->sum('Total_Return_Value');

                           $ReturnSalesPrevY4=ReturnSales::orderBy('id','desc')
            ->whereBetween('Date',[$Y4F,$Y4T])


            ->get()->sum('Total_Return_Value');

                          $ReturnSalesPrevYC=ReturnSales::orderBy('id','desc')
            ->whereBetween('Date',[$YCF,$YCT])


            ->get()->sum('Total_Return_Value');
//ReturnPurch
                 $ReturnPurchPrevY1=ReturnPurch::orderBy('id','desc')
            ->whereBetween('Date',[$Y1F,$Y1T])


            ->get()->sum('Total_Return_Value');


                           $ReturnPurchPrevY2=ReturnPurch::orderBy('id','desc')
            ->whereBetween('Date',[$Y2F,$Y2T])


            ->get()->sum('Total_Return_Value');


                           $ReturnPurchPrevY3=ReturnPurch::orderBy('id','desc')
            ->whereBetween('Date',[$Y3F,$Y3T])


            ->get()->sum('Total_Return_Value');

                           $ReturnPurchPrevY4=ReturnPurch::orderBy('id','desc')
            ->whereBetween('Date',[$Y4F,$Y4T])


            ->get()->sum('Total_Return_Value');

                          $ReturnPurchPrevYC=ReturnPurch::orderBy('id','desc')
            ->whereBetween('Date',[$YCF,$YCT])


            ->get()->sum('Total_Return_Value');


            //Expenses

            $TotalDebMasrofatY1 = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {
                  $Y1F=date("Y-01-01",strtotime("-1 year"));
        $Y1T=date("Y-12-31",strtotime("-1 year"));
            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->whereBetween('general_dailies.Date',[$Y1F,$Y1T]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordsss.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Debitor_Coin');



        $TotalCredMasrofatY1 = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {
                  $Y1F=date("Y-01-01",strtotime("-1 year"));
        $Y1T=date("Y-12-31",strtotime("-1 year"));
            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->whereBetween('general_dailies.Date',[$Y1F,$Y1T]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordsss.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Creditor_Coin');



            $EgmalyMasrofatY1=  $TotalDebMasrofatY1 -  $TotalCredMasrofatY1 ;



               $TotalDebMasrofatY2 = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {
                  $Y2F=date("Y-01-01",strtotime("-2 year"));
        $Y2T=date("Y-12-31",strtotime("-2 year"));
            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->whereBetween('general_dailies.Date',[$Y2F,$Y2T]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordsss.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Debitor_Coin');



        $TotalCredMasrofatY2 = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {
                  $Y2F=date("Y-01-01",strtotime("-2 year"));
        $Y2T=date("Y-12-31",strtotime("-2 year"));
            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->whereBetween('general_dailies.Date',[$Y2F,$Y2T]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordsss.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Creditor_Coin');



            $EgmalyMasrofatY2=  $TotalDebMasrofatY2 -  $TotalCredMasrofatY2 ;




                   $TotalDebMasrofatY3 = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {
                $Y3F=date("Y-01-01",strtotime("-3 year"));
        $Y3T=date("Y-12-31",strtotime("-3 year"));
            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->whereBetween('general_dailies.Date',[$Y3F,$Y3T]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordsss.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Debitor_Coin');



        $TotalCredMasrofatY3 = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {
                $Y3F=date("Y-01-01",strtotime("-3 year"));
        $Y3T=date("Y-12-31",strtotime("-3 year"));
            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->whereBetween('general_dailies.Date',[$Y3F,$Y3T]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordsss.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Creditor_Coin');



            $EgmalyMasrofatY3=  $TotalDebMasrofatY3 -  $TotalCredMasrofatY3 ;



                 $TotalDebMasrofatY4 = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {
                          $Y4F=date("Y-01-01",strtotime("-4 year"));
        $Y4T=date("Y-12-31",strtotime("-4 year"));


            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->whereBetween('general_dailies.Date',[$Y4F,$Y4T]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordsss.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Debitor_Coin');



        $TotalCredMasrofatY4 = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {
                          $Y4F=date("Y-01-01",strtotime("-4 year"));
        $Y4T=date("Y-12-31",strtotime("-4 year"));


            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->whereBetween('general_dailies.Date',[$Y4F,$Y4T]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordsss.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Creditor_Coin');



            $EgmalyMasrofatY4=  $TotalDebMasrofatY4 -  $TotalCredMasrofatY4 ;




                  $TotalDebMasrofatCY = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {
               $YCF=date("Y-01-01");
        $YCT=date("Y-12-31");
            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->whereBetween('general_dailies.Date',[$YCF,$YCT]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordsss.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Debitor_Coin');



        $TotalCredMasrofatCY = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {
               $YCF=date("Y-01-01");
        $YCT=date("Y-12-31");
            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->whereBetween('general_dailies.Date',[$YCF,$YCT]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordsss.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Creditor_Coin');



            $EgmalyMasrofatCY=  $TotalDebMasrofatCY -  $TotalCredMasrofatCY ;


            // =============  Safy Rab7   ===============


            $AMsrfoat=AcccountingManual::where('id',17)->first();

        if($AMsrfoat->Parent == 0){

          $wordsss=$AMsrfoat->Code.'0';

        }else{

           $wordsss=$AMsrfoat->Code;

        }
  $AErydaaat=AcccountingManual::where('id',18)->first();

        if($AErydaaat->Parent == 0){

          $wordsX=$AErydaaat->Code.'0';

        }else{

           $wordsX=$AEryd->Code;

        }


    $ATaklfaaat=AcccountingManual::where('id',19)->first();

        if($ATaklfaaat->Parent == 0){

          $wordssX=$ATaklfaaat->Code.'0';

        }else{

           $wordssX=$ATaklfaaat->Code;

        }


        $AMasroffffffffat=AcccountingManual::where('id',20)->first();

        if($AMasroffffffffat->Parent == 0){

          $wordsssX=$AMasroffffffffat->Code.'0';

        }else{

           $wordsssX=$AMasroffffffffat->Code;

        }


              $TotalDebErydatJan = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {
            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->whereBetween('general_dailies.Date', [date('Y-01-01'),date('Y-02-01')]);
        })
          ->where('acccounting_manuals.Code', 'like', $wordsX.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Debitor_Coin');



        $TotalCredErydatJan = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {
            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->whereBetween('general_dailies.Date', [date('Y-01-01'),date('Y-02-01')]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordsX.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Creditor_Coin');

        $EgmalyErydatJan=$TotalCredErydatJan - $TotalDebErydatJan  ;




              $TotalDebTaklfaatJan = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {

            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                   ->whereBetween('general_dailies.Date', [date('Y-01-01'),date('Y-02-01')]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordssX.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Debitor_Coin');



        $TotalCredTaklfaatJan = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {
            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
               ->whereBetween('general_dailies.Date', [date('Y-01-01'),date('Y-02-01')]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordssX.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Creditor_Coin');

        $EgmalyTaklfaatJan=$TotalDebTaklfaatJan - $TotalCredTaklfaatJan  ;


           $TotalDebMasrofaatJan = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {


            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->whereBetween('general_dailies.Date',[date('Y-01-01'),date('Y-02-01')]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordsssX.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Debitor_Coin');



        $TotalCredMasrofaatJan = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {


            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                   ->whereBetween('general_dailies.Date', [date('Y-01-01'),date('Y-02-01')]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordsssX.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Creditor_Coin');

        $EgmalyMasrofaatJan=$TotalDebMasrofaatJan - $TotalCredMasrofaatJan  ;


        $fgJan= $EgmalyTaklfaatJan + $EgmalyMasrofaatJan;
        if($EgmalyErydatJan >  $fgJan){
         if($fgJan  <  0 ){

            $SafyRab7Jan =$EgmalyErydatJan + ($EgmalyTaklfaatJan  +   $EgmalyMasrofaatJan)  ;
        }else{

              $SafyRab7Jan = $EgmalyErydatJan -  ($EgmalyTaklfaatJan  +   $EgmalyMasrofaatJan)  ;

        }
        }elseif($EgmalyErydatJan <  $fgJan){

        if($fgJan  <  0 ){

            $SafyRab7Jan =$EgmalyErydatJan + ($EgmalyTaklfaatJan  +   $EgmalyMasrofaatJan)  ;
        }else{

              $SafyRab7Jan = $EgmalyErydatJan -  ($EgmalyTaklfaatJan  +   $EgmalyMasrofaatJan)  ;

        }


        }elseif($EgmalyErydatJan ==  $fgJan){

           $SafyRab7Jan = $EgmalyErydatJan -  ($EgmalyTaklfaatJan  +   $EgmalyMasrofaatJan)  ;

        }


       $TotalDebErydatFeb = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {
            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->whereBetween('general_dailies.Date', [date('Y-02-01'),date('Y-03-01')]);
        })
          ->where('acccounting_manuals.Code', 'like', $wordsX.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Debitor_Coin');



        $TotalCredErydatFeb = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {
            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->whereBetween('general_dailies.Date',[date('Y-02-01'),date('Y-03-01')]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordsX.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Creditor_Coin');

        $EgmalyErydatFeb=$TotalCredErydatFeb - $TotalDebErydatFeb  ;




              $TotalDebTaklfaatFeb = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {

            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                   ->whereBetween('general_dailies.Date', [date('Y-02-01'),date('Y-03-01')]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordssX.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Debitor_Coin');



        $TotalCredTaklfaatFeb = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {
            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
               ->whereBetween('general_dailies.Date', [date('Y-02-01'),date('Y-03-01')]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordssX.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Creditor_Coin');

        $EgmalyTaklfaatFeb=$TotalDebTaklfaatFeb - $TotalCredTaklfaatFeb  ;


           $TotalDebMasrofaatFeb = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {


            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->whereBetween('general_dailies.Date', [date('Y-02-01'),date('Y-03-01')]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordsssX.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Debitor_Coin');



        $TotalCredMasrofaatFeb = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {


            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                   ->whereBetween('general_dailies.Date', [date('Y-02-01'),date('Y-03-01')]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordsssX.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Creditor_Coin');

        $EgmalyMasrofaatFeb=$TotalDebMasrofaatFeb - $TotalCredMasrofaatFeb  ;


        $fgFeb= $EgmalyTaklfaatFeb + $EgmalyMasrofaatFeb;
        if($EgmalyErydatFeb >  $fgFeb){
         if($fgFeb  <  0 ){

            $SafyRab7Feb =$EgmalyErydatFeb + ($EgmalyTaklfaatFeb  +   $EgmalyMasrofaatFeb)  ;
        }else{

              $SafyRab7Feb = $EgmalyErydatFeb -  ($EgmalyTaklfaatFeb  +   $EgmalyMasrofaatFeb)  ;

        }
        }elseif($EgmalyErydatFeb <  $fgFeb){

        if($fgFeb  <  0 ){

            $SafyRab7Feb =$EgmalyErydatFeb + ($EgmalyTaklfaatFeb  +   $EgmalyMasrofaatFeb)  ;
        }else{

              $SafyRab7Feb = $EgmalyErydatFeb -  ($EgmalyTaklfaatFeb  +   $EgmalyMasrofaatFeb)  ;

        }


        }elseif($EgmalyErydatFeb ==  $fgFeb){

           $SafyRab7Feb = $EgmalyErydatFeb -  ($EgmalyTaklfaatFeb  +   $EgmalyMasrofaatFeb)  ;

        }



     $TotalDebErydatMar = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {
            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->whereBetween('general_dailies.Date', [date('Y-03-01'),date('Y-04-01')]);
        })
          ->where('acccounting_manuals.Code', 'like', $wordsX.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Debitor_Coin');



        $TotalCredErydatMar = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {
            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->whereBetween('general_dailies.Date', [date('Y-03-01'),date('Y-04-01')]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordsX.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Creditor_Coin');

        $EgmalyErydatMar=$TotalCredErydatMar - $TotalDebErydatMar  ;




              $TotalDebTaklfaatMar = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {

            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                   ->whereBetween('general_dailies.Date', [date('Y-03-01'),date('Y-04-01')]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordssX.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Debitor_Coin');



        $TotalCredTaklfaatMar = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {
            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
               ->whereBetween('general_dailies.Date', [date('Y-03-01'),date('Y-04-01')]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordssX.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Creditor_Coin');

        $EgmalyTaklfaatMar=$TotalDebTaklfaatMar - $TotalCredTaklfaatMar  ;


           $TotalDebMasrofaatMar = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {


            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->whereBetween('general_dailies.Date', [date('Y-03-01'),date('Y-04-01')]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordsssX.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Debitor_Coin');



        $TotalCredMasrofaatMar = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {


            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                   ->whereBetween('general_dailies.Date', [date('Y-03-01'),date('Y-04-01')]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordsssX.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Creditor_Coin');

        $EgmalyMasrofaatMar=$TotalDebMasrofaatMar - $TotalCredMasrofaatMar  ;


        $fgMar= $EgmalyTaklfaatMar + $EgmalyMasrofaatMar;
        if($EgmalyErydatMar >  $fgMar){
         if($fgMar  <  0 ){

            $SafyRab7Mar =$EgmalyErydatMar + ($EgmalyTaklfaatMar  +   $EgmalyMasrofaatMar)  ;
        }else{

              $SafyRab7Mar = $EgmalyErydatMar -  ($EgmalyTaklfaatMar  +   $EgmalyMasrofaatMar)  ;

        }
        }elseif($EgmalyErydatMar <  $fgMar){

        if($fgMar  <  0 ){

            $SafyRab7Mar =$EgmalyErydatMar + ($EgmalyTaklfaatMar  +   $EgmalyMasrofaatMar)  ;
        }else{

              $SafyRab7Mar = $EgmalyErydatMar -  ($EgmalyTaklfaatMar  +   $EgmalyMasrofaatMar)  ;

        }


        }elseif($EgmalyErydatMar ==  $fgMar){

           $SafyRab7Mar = $EgmalyErydatMar -  ($EgmalyTaklfaatMar  +   $EgmalyMasrofaatMar)  ;

        }

     $TotalDebErydatApr = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {
            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->whereBetween('general_dailies.Date', [date('Y-04-01'),date('Y-05-01')]);
        })
          ->where('acccounting_manuals.Code', 'like', $wordsX.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Debitor_Coin');



        $TotalCredErydatApr = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {
            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->whereBetween('general_dailies.Date', [date('Y-04-01'),date('Y-05-01')]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordsX.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Creditor_Coin');

        $EgmalyErydatApr=$TotalCredErydatApr - $TotalDebErydatApr  ;




              $TotalDebTaklfaatApr = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {

            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                   ->whereBetween('general_dailies.Date', [date('Y-04-01'),date('Y-05-01')]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordssX.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Debitor_Coin');



        $TotalCredTaklfaatApr = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {
            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
               ->whereBetween('general_dailies.Date', [date('Y-04-01'),date('Y-05-01')]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordssX.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Creditor_Coin');

        $EgmalyTaklfaatApr=$TotalDebTaklfaatApr - $TotalCredTaklfaatApr  ;


           $TotalDebMasrofaatApr = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {


            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->whereBetween('general_dailies.Date', [date('Y-04-01'),date('Y-05-01')]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordsssX.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Debitor_Coin');



        $TotalCredMasrofaatApr = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {


            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                   ->whereBetween('general_dailies.Date', [date('Y-04-01'),date('Y-05-01')]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordsssX.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Creditor_Coin');

        $EgmalyMasrofaatApr=$TotalDebMasrofaatApr - $TotalCredMasrofaatApr  ;


        $fgApr= $EgmalyTaklfaatApr + $EgmalyMasrofaatApr;
        if($EgmalyErydatApr >  $fgApr){
         if($fgApr  <  0 ){

            $SafyRab7Apr =$EgmalyErydatApr + ($EgmalyTaklfaatApr  +   $EgmalyMasrofaatApr)  ;
        }else{

              $SafyRab7Apr = $EgmalyErydatApr -  ($EgmalyTaklfaatApr  +   $EgmalyMasrofaatApr)  ;

        }
        }elseif($EgmalyErydatApr <  $fgApr){

        if($fgApr  <  0 ){

            $SafyRab7Apr =$EgmalyErydatApr + ($EgmalyTaklfaatApr  +   $EgmalyMasrofaatApr)  ;
        }else{

              $SafyRab7Apr = $EgmalyErydatApr -  ($EgmalyTaklfaatApr  +   $EgmalyMasrofaatApr)  ;

        }


        }elseif($EgmalyErydatApr ==  $fgApr){

           $SafyRab7Apr = $EgmalyErydatApr -  ($EgmalyTaklfaatApr  +   $EgmalyMasrofaatApr)  ;

        }


     $TotalDebErydatMay = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {
            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->whereBetween('general_dailies.Date', [date('Y-05-01'),date('Y-06-01')]);
        })
          ->where('acccounting_manuals.Code', 'like', $wordsX.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Debitor_Coin');



        $TotalCredErydatMay = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {
            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->whereBetween('general_dailies.Date', [date('Y-05-01'),date('Y-06-01')]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordsX.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Creditor_Coin');

        $EgmalyErydatMay=$TotalCredErydatMay - $TotalDebErydatMay  ;




              $TotalDebTaklfaatMay = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {

            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                   ->whereBetween('general_dailies.Date', [date('Y-05-01'),date('Y-06-01')]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordssX.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Debitor_Coin');



        $TotalCredTaklfaatMay = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {
            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
               ->whereBetween('general_dailies.Date', [date('Y-05-01'),date('Y-06-01')]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordssX.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Creditor_Coin');

        $EgmalyTaklfaatMay=$TotalDebTaklfaatMay - $TotalCredTaklfaatMay  ;


           $TotalDebMasrofaatMay = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {


            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->whereBetween('general_dailies.Date', [date('Y-05-01'),date('Y-06-01')]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordsssX.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Debitor_Coin');



        $TotalCredMasrofaatMay = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {


            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                   ->whereBetween('general_dailies.Date', [date('Y-05-01'),date('Y-06-01')]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordsssX.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Creditor_Coin');

        $EgmalyMasrofaatMay=$TotalDebMasrofaatMay - $TotalCredMasrofaatMay  ;


        $fgMay= $EgmalyTaklfaatMay + $EgmalyMasrofaatMay;
        if($EgmalyErydatMay >  $fgMay){
         if($fgMay  <  0 ){

            $SafyRab7May =$EgmalyErydatMay + ($EgmalyTaklfaatMay  +   $EgmalyMasrofaatMay)  ;
        }else{

              $SafyRab7May = $EgmalyErydatMay -  ($EgmalyTaklfaatMay  +   $EgmalyMasrofaatMay)  ;

        }
        }elseif($EgmalyErydatMay <  $fgMay){

        if($fgMay  <  0 ){

            $SafyRab7May =$EgmalyErydatMay + ($EgmalyTaklfaatMay  +   $EgmalyMasrofaatMay)  ;
        }else{

              $SafyRab7May = $EgmalyErydatMay -  ($EgmalyTaklfaatMay  +   $EgmalyMasrofaatMay)  ;

        }


        }elseif($EgmalyErydatMay ==  $fgMay){

           $SafyRab7May = $EgmalyErydatMay -  ($EgmalyTaklfaatMay  +   $EgmalyMasrofaatMay)  ;

        }

     $TotalDebErydatJun = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {
            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->whereBetween('general_dailies.Date', [date('Y-06-01'),date('Y-07-01')]);
        })
          ->where('acccounting_manuals.Code', 'like', $wordsX.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Debitor_Coin');



        $TotalCredErydatJun = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {
            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->whereBetween('general_dailies.Date', [date('Y-06-01'),date('Y-07-01')]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordsX.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Creditor_Coin');

        $EgmalyErydatJun=$TotalCredErydatJun - $TotalDebErydatJun  ;




              $TotalDebTaklfaatJun = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {

            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                   ->whereBetween('general_dailies.Date', [date('Y-06-01'),date('Y-07-01')]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordssX.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Debitor_Coin');



        $TotalCredTaklfaatJun = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {
            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
               ->whereBetween('general_dailies.Date', [date('Y-06-01'),date('Y-07-01')]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordssX.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Creditor_Coin');

        $EgmalyTaklfaatJun=$TotalDebTaklfaatJun - $TotalCredTaklfaatJun  ;


           $TotalDebMasrofaatJun = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {


            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->whereBetween('general_dailies.Date', [date('Y-06-01'),date('Y-07-01')]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordsssX.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Debitor_Coin');



        $TotalCredMasrofaatJun = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {


            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                   ->whereBetween('general_dailies.Date', [date('Y-06-01'),date('Y-07-01')]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordsssX.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Creditor_Coin');

        $EgmalyMasrofaatJun=$TotalDebMasrofaatJun - $TotalCredMasrofaatJun  ;


        $fgJun= $EgmalyTaklfaatJun + $EgmalyMasrofaatJun;
        if($EgmalyErydatJun >  $fgJun){
         if($fgJun  <  0 ){

            $SafyRab7Jun =$EgmalyErydatJun + ($EgmalyTaklfaatJun  +   $EgmalyMasrofaatJun)  ;
        }else{

              $SafyRab7Jun = $EgmalyErydatJun -  ($EgmalyTaklfaatJun  +   $EgmalyMasrofaatJun)  ;

        }
        }elseif($EgmalyErydatJun <  $fgJun){

        if($fgJun  <  0 ){

            $SafyRab7Jun =$EgmalyErydatJun + ($EgmalyTaklfaatJun  +   $EgmalyMasrofaatJun)  ;
        }else{

              $SafyRab7Jun = $EgmalyErydatJun -  ($EgmalyTaklfaatJun  +   $EgmalyMasrofaatJun)  ;

        }


        }elseif($EgmalyErydatJun ==  $fgJun){

           $SafyRab7Jun = $EgmalyErydatJun -  ($EgmalyTaklfaatJun  +   $EgmalyMasrofaatJun)  ;

        }

     $TotalDebErydatJul = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {
            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->whereBetween('general_dailies.Date', [date('Y-07-01'),date('Y-08-01')]);
        })
          ->where('acccounting_manuals.Code', 'like', $wordsX.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Debitor_Coin');



        $TotalCredErydatJul = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {
            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->whereBetween('general_dailies.Date', [date('Y-07-01'),date('Y-08-01')]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordsX.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Creditor_Coin');

        $EgmalyErydatJul=$TotalCredErydatJul - $TotalDebErydatJul  ;




              $TotalDebTaklfaatJul = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {

            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                   ->whereBetween('general_dailies.Date', [date('Y-07-01'),date('Y-08-01')]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordssX.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Debitor_Coin');



        $TotalCredTaklfaatJul = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {
            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
               ->whereBetween('general_dailies.Date', [date('Y-07-01'),date('Y-08-01')]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordssX.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Creditor_Coin');

        $EgmalyTaklfaatJul=$TotalDebTaklfaatJul - $TotalCredTaklfaatJul  ;


           $TotalDebMasrofaatJul = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {


            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->whereBetween('general_dailies.Date', [date('Y-07-01'),date('Y-08-01')]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordsssX.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Debitor_Coin');



        $TotalCredMasrofaatJul = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {


            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                   ->whereBetween('general_dailies.Date', [date('Y-07-01'),date('Y-08-01')]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordsssX.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Creditor_Coin');

        $EgmalyMasrofaatJul=$TotalDebMasrofaatJul - $TotalCredMasrofaatJul  ;


        $fgJul= $EgmalyTaklfaatJul + $EgmalyMasrofaatJul;
        if($EgmalyErydatJul >  $fgJul){
         if($fgJul  <  0 ){

            $SafyRab7Jul =$EgmalyErydatJul + ($EgmalyTaklfaatJul  +   $EgmalyMasrofaatJul)  ;
        }else{

              $SafyRab7Jul = $EgmalyErydatJul -  ($EgmalyTaklfaatJul  +   $EgmalyMasrofaatJul)  ;

        }
        }elseif($EgmalyErydatJul <  $fgJul){

        if($fgJul  <  0 ){

            $SafyRab7Jul =$EgmalyErydatJul + ($EgmalyTaklfaatJul  +   $EgmalyMasrofaatJul)  ;
        }else{

              $SafyRab7Jul = $EgmalyErydatJul -  ($EgmalyTaklfaatJul  +   $EgmalyMasrofaatJul)  ;

        }


        }elseif($EgmalyErydatJul ==  $fgJul){

           $SafyRab7Jul = $EgmalyErydatJul -  ($EgmalyTaklfaatJul  +   $EgmalyMasrofaatJul)  ;

        }

     $TotalDebErydatAug = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {
            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->whereBetween('general_dailies.Date', [date('Y-08-01'),date('Y-09-01')]);
        })
          ->where('acccounting_manuals.Code', 'like', $wordsX.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Debitor_Coin');



        $TotalCredErydatAug = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {
            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->whereBetween('general_dailies.Date', [date('Y-08-01'),date('Y-09-01')]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordsX.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Creditor_Coin');

        $EgmalyErydatAug=$TotalCredErydatAug - $TotalDebErydatAug  ;




              $TotalDebTaklfaatAug = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {

            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                   ->whereBetween('general_dailies.Date', [date('Y-08-01'),date('Y-09-01')]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordssX.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Debitor_Coin');



        $TotalCredTaklfaatAug = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {
            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
               ->whereBetween('general_dailies.Date', [date('Y-08-01'),date('Y-09-01')]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordssX.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Creditor_Coin');

        $EgmalyTaklfaatAug=$TotalDebTaklfaatAug - $TotalCredTaklfaatAug  ;


           $TotalDebMasrofaatAug = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {


            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->whereBetween('general_dailies.Date', [date('Y-08-01'),date('Y-09-01')]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordsssX.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Debitor_Coin');



        $TotalCredMasrofaatAug = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {


            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                   ->whereBetween('general_dailies.Date', [date('Y-08-01'),date('Y-09-01')]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordsssX.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Creditor_Coin');

        $EgmalyMasrofaatAug=$TotalDebMasrofaatAug - $TotalCredMasrofaatAug  ;


        $fgAug= $EgmalyTaklfaatAug + $EgmalyMasrofaatAug;
        if($EgmalyErydatAug >  $fgAug){
         if($fgAug  <  0 ){

            $SafyRab7Aug =$EgmalyErydatAug + ($EgmalyTaklfaatAug  +   $EgmalyMasrofaatAug)  ;
        }else{

              $SafyRab7Aug = $EgmalyErydatAug -  ($EgmalyTaklfaatAug  +   $EgmalyMasrofaatAug)  ;

        }
        }elseif($EgmalyErydatAug <  $fgAug){

        if($fgAug  <  0 ){

            $SafyRab7Aug =$EgmalyErydatAug + ($EgmalyTaklfaatAug  +   $EgmalyMasrofaatAug)  ;
        }else{

              $SafyRab7Aug = $EgmalyErydatAug -  ($EgmalyTaklfaatAug  +   $EgmalyMasrofaatAug)  ;

        }


        }elseif($EgmalyErydatAug ==  $fgAug){

           $SafyRab7Aug = $EgmalyErydatAug -  ($EgmalyTaklfaatAug  +   $EgmalyMasrofaatAug)  ;

        }

     $TotalDebErydatSep = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {
            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->whereBetween('general_dailies.Date', [date('Y-09-01'),date('Y-10-01')]);
        })
          ->where('acccounting_manuals.Code', 'like', $wordsX.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Debitor_Coin');



        $TotalCredErydatSep = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {
            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->whereBetween('general_dailies.Date', [date('Y-09-01'),date('Y-10-01')]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordsX.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Creditor_Coin');

        $EgmalyErydatSep=$TotalCredErydatSep - $TotalDebErydatSep  ;




              $TotalDebTaklfaatSep = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {

            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                   ->whereBetween('general_dailies.Date', [date('Y-09-01'),date('Y-10-01')]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordssX.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Debitor_Coin');



        $TotalCredTaklfaatSep = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {
            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
               ->whereBetween('general_dailies.Date', [date('Y-09-01'),date('Y-10-01')]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordssX.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Creditor_Coin');

        $EgmalyTaklfaatSep=$TotalDebTaklfaatSep - $TotalCredTaklfaatSep  ;


           $TotalDebMasrofaatSep = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {


            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->whereBetween('general_dailies.Date', [date('Y-09-01'),date('Y-10-01')]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordsssX.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Debitor_Coin');



        $TotalCredMasrofaatSep = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {


            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                   ->whereBetween('general_dailies.Date', [date('Y-09-01'),date('Y-10-01')]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordsssX.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Creditor_Coin');

        $EgmalyMasrofaatSep=$TotalDebMasrofaatSep - $TotalCredMasrofaatSep  ;


        $fgSep= $EgmalyTaklfaatSep + $EgmalyMasrofaatSep;
        if($EgmalyErydatSep >  $fgSep){
         if($fgSep  <  0 ){

            $SafyRab7Sep =$EgmalyErydatSep + ($EgmalyTaklfaatSep  +   $EgmalyMasrofaatSep)  ;
        }else{

              $SafyRab7Sep = $EgmalyErydatSep -  ($EgmalyTaklfaatSep  +   $EgmalyMasrofaatSep)  ;

        }
        }elseif($EgmalyErydatSep <  $fgSep){

        if($fgSep  <  0 ){

            $SafyRab7Sep =$EgmalyErydatSep + ($EgmalyTaklfaatSep  +   $EgmalyMasrofaatSep)  ;
        }else{

              $SafyRab7Sep = $EgmalyErydatSep -  ($EgmalyTaklfaatSep  +   $EgmalyMasrofaatSep)  ;

        }


        }elseif($EgmalyErydatSep ==  $fgSep){

           $SafyRab7Sep = $EgmalyErydatSep -  ($EgmalyTaklfaatSep  +   $EgmalyMasrofaatSep)  ;

        }

     $TotalDebErydatOct = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {
            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->whereBetween('general_dailies.Date', [date('Y-10-01'),date('Y-11-01')]);
        })
          ->where('acccounting_manuals.Code', 'like', $wordsX.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Debitor_Coin');



        $TotalCredErydatOct = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {
            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->whereBetween('general_dailies.Date', [date('Y-10-01'),date('Y-11-01')]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordsX.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Creditor_Coin');

        $EgmalyErydatOct=$TotalCredErydatOct - $TotalDebErydatOct  ;




              $TotalDebTaklfaatOct = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {

            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                   ->whereBetween('general_dailies.Date', [date('Y-10-01'),date('Y-11-01')]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordssX.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Debitor_Coin');



        $TotalCredTaklfaatOct = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {
            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
               ->whereBetween('general_dailies.Date', [date('Y-10-01'),date('Y-11-01')]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordssX.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Creditor_Coin');

        $EgmalyTaklfaatOct=$TotalDebTaklfaatOct - $TotalCredTaklfaatOct  ;


           $TotalDebMasrofaatOct = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {


            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->whereBetween('general_dailies.Date', [date('Y-10-01'),date('Y-11-01')]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordsssX.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Debitor_Coin');



        $TotalCredMasrofaatOct = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {


            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                   ->whereBetween('general_dailies.Date', [date('Y-10-01'),date('Y-11-01')]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordsssX.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Creditor_Coin');

        $EgmalyMasrofaatOct=$TotalDebMasrofaatOct - $TotalCredMasrofaatOct  ;


        $fgOct= $EgmalyTaklfaatOct + $EgmalyMasrofaatOct;
        if($EgmalyErydatOct >  $fgOct){
         if($fgOct  <  0 ){

            $SafyRab7Oct =$EgmalyErydatOct + ($EgmalyTaklfaatOct  +   $EgmalyMasrofaatOct)  ;
        }else{

              $SafyRab7Oct = $EgmalyErydatOct -  ($EgmalyTaklfaatOct  +   $EgmalyMasrofaatOct)  ;

        }
        }elseif($EgmalyErydatOct <  $fgOct){

        if($fgOct  <  0 ){

            $SafyRab7Oct =$EgmalyErydatOct + ($EgmalyTaklfaatOct  +   $EgmalyMasrofaatOct)  ;
        }else{

              $SafyRab7Oct = $EgmalyErydatOct -  ($EgmalyTaklfaatOct  +   $EgmalyMasrofaatOct)  ;

        }


        }elseif($EgmalyErydatOct ==  $fgOct){

           $SafyRab7Oct = $EgmalyErydatOct -  ($EgmalyTaklfaatOct  +   $EgmalyMasrofaatOct)  ;

        }

     $TotalDebErydatNov = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {
            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->whereBetween('general_dailies.Date', [date('Y-11-01'),date('Y-12-01')]);
        })
          ->where('acccounting_manuals.Code', 'like', $wordsX.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Debitor_Coin');



        $TotalCredErydatNov = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {
            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->whereBetween('general_dailies.Date', [date('Y-11-01'),date('Y-12-01')]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordsX.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Creditor_Coin');

        $EgmalyErydatNov=$TotalCredErydatNov - $TotalDebErydatNov  ;




              $TotalDebTaklfaatNov = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {

            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                   ->whereBetween('general_dailies.Date', [date('Y-11-01'),date('Y-12-01')]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordssX.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Debitor_Coin');



        $TotalCredTaklfaatNov = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {
            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
               ->whereBetween('general_dailies.Date', [date('Y-11-01'),date('Y-12-01')]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordssX.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Creditor_Coin');

        $EgmalyTaklfaatNov=$TotalDebTaklfaatNov - $TotalCredTaklfaatNov  ;


           $TotalDebMasrofaatNov = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {


            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->whereBetween('general_dailies.Date', [date('Y-11-01'),date('Y-12-01')]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordsssX.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Debitor_Coin');



        $TotalCredMasrofaatNov = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {


            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                   ->whereBetween('general_dailies.Date', [date('Y-11-01'),date('Y-12-01')]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordsssX.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Creditor_Coin');

        $EgmalyMasrofaatNov=$TotalDebMasrofaatNov - $TotalCredMasrofaatNov  ;


        $fgNov= $EgmalyTaklfaatNov + $EgmalyMasrofaatNov;
        if($EgmalyErydatNov >  $fgNov){
         if($fgNov  <  0 ){

            $SafyRab7Nov =$EgmalyErydatNov + ($EgmalyTaklfaatNov  +   $EgmalyMasrofaatNov)  ;
        }else{

              $SafyRab7Nov = $EgmalyErydatNov -  ($EgmalyTaklfaatNov  +   $EgmalyMasrofaatNov)  ;

        }
        }elseif($EgmalyErydatNov <  $fgNov){

        if($fgNov  <  0 ){

            $SafyRab7Nov =$EgmalyErydatNov + ($EgmalyTaklfaatNov  +   $EgmalyMasrofaatNov)  ;
        }else{

              $SafyRab7Nov = $EgmalyErydatNov -  ($EgmalyTaklfaatNov  +   $EgmalyMasrofaatNov)  ;

        }


        }elseif($EgmalyErydatNov ==  $fgNov){

           $SafyRab7Nov = $EgmalyErydatNov -  ($EgmalyTaklfaatNov  +   $EgmalyMasrofaatNov)  ;

        }

     $TotalDebErydatDec = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {
            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->whereBetween('general_dailies.Date', [date('Y-12-01'),date('Y-12-31')]);
        })
          ->where('acccounting_manuals.Code', 'like', $wordsX.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Debitor_Coin');



        $TotalCredErydatDec = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {
            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->whereBetween('general_dailies.Date', [date('Y-12-01'),date('Y-12-31')]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordsX.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Creditor_Coin');

        $EgmalyErydatDec=$TotalCredErydatDec - $TotalDebErydatDec  ;




              $TotalDebTaklfaatDec = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {

            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                   ->whereBetween('general_dailies.Date', [date('Y-12-01'),date('Y-12-31')]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordssX.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Debitor_Coin');



        $TotalCredTaklfaatDec = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {
            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
               ->whereBetween('general_dailies.Date', [date('Y-12-01'),date('Y-12-31')]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordssX.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Creditor_Coin');

        $EgmalyTaklfaatDec=$TotalDebTaklfaatDec - $TotalCredTaklfaatDec  ;


           $TotalDebMasrofaatDec = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {


            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->whereBetween('general_dailies.Date', [date('Y-12-01'),date('Y-12-31')]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordsssX.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Debitor_Coin');



        $TotalCredMasrofaatDec = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {


            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                   ->whereBetween('general_dailies.Date', [date('Y-12-01'),date('Y-12-31')]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordsssX.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Creditor_Coin');

        $EgmalyMasrofaatDec=$TotalDebMasrofaatDec - $TotalCredMasrofaatDec  ;


        $fgDec= $EgmalyTaklfaatDec + $EgmalyMasrofaatDec;
        if($EgmalyErydatDec >  $fgDec){
         if($fgDec  <  0 ){

            $SafyRab7Dec =$EgmalyErydatDec + ($EgmalyTaklfaatDec  +   $EgmalyMasrofaatDec)  ;
        }else{

              $SafyRab7Dec = $EgmalyErydatDec -  ($EgmalyTaklfaatDec  +   $EgmalyMasrofaatDec)  ;

        }
        }elseif($EgmalyErydatDec <  $fgDec){

        if($fgDec  <  0 ){

            $SafyRab7Dec =$EgmalyErydatDec + ($EgmalyTaklfaatDec  +   $EgmalyMasrofaatDec)  ;
        }else{

              $SafyRab7Dec = $EgmalyErydatDec -  ($EgmalyTaklfaatDec  +   $EgmalyMasrofaatDec)  ;

        }


        }elseif($EgmalyErydatDec ==  $fgDec){

           $SafyRab7Dec = $EgmalyErydatDec -  ($EgmalyTaklfaatDec  +   $EgmalyMasrofaatDec)  ;

        }



            $Groups=ItemsGroups::select('Name','NameEn','id')->get();
            $Brands=Brands::select('Name','NameEn','id')->get();






         return view('admin.home',[

             'SalesJan'=>$SalesJan,
             'SalesFeb'=>$SalesFeb,
             'SalesMar'=>$SalesMar,
             'SalesApr'=>$SalesApr,
             'SalesMay'=>$SalesMay,
             'SalesJun'=>$SalesJun,
             'SalesJul'=>$SalesJul,
             'SalesAug'=>$SalesAug,
             'SalesSep'=>$SalesSep,
             'SalesOct'=>$SalesOct,
             'SalesNov'=>$SalesNov,
             'SalesDec'=>$SalesDec,

             'SalesSat'=>$SalesSat,
             'SalesSun'=>$SalesSun,
             'SalesMon'=>$SalesMon,
             'SalesTue'=>$SalesTue,
             'SalesWed'=>$SalesWed,
             'SalesThr'=>$SalesThr,
             'SalesFri'=>$SalesFri,

             'SalesPrevY1'=>$SalesPrevY1,
             'SalesPrevY2'=>$SalesPrevY2,
             'SalesPrevY3'=>$SalesPrevY3,
             'SalesPrevY4'=>$SalesPrevY4,
             'SalesPrevYC'=>$SalesPrevYC,

              'PurchJan'=>$PurchJan,
             'PurchFeb'=>$PurchFeb,
             'PurchMar'=>$PurchMar,
             'PurchApr'=>$PurchApr,
             'PurchMay'=>$PurchMay,
             'PurchJun'=>$PurchJun,
             'PurchJul'=>$PurchJul,
             'PurchAug'=>$PurchAug,
             'PurchSep'=>$PurchSep,
             'PurchOct'=>$PurchOct,
             'PurchNov'=>$PurchNov,
             'PurchDec'=>$PurchDec,

             'PurchSat'=>$PurchSat,
             'PurchSun'=>$PurchSun,
             'PurchMon'=>$PurchMon,
             'PurchTue'=>$PurchTue,
             'PurchWed'=>$PurchWed,
             'PurchThr'=>$PurchThr,
             'PurchFri'=>$PurchFri,

             'PurchPrevY1'=>$PurchPrevY1,
             'PurchPrevY2'=>$PurchPrevY2,
             'PurchPrevY3'=>$PurchPrevY3,
             'PurchPrevY4'=>$PurchPrevY4,
             'PurchPrevYC'=>$PurchPrevYC,


               'ReturnSalesJan'=>$ReturnSalesJan,
             'ReturnSalesFeb'=>$ReturnSalesFeb,
             'ReturnSalesMar'=>$ReturnSalesMar,
             'ReturnSalesApr'=>$ReturnSalesApr,
             'ReturnSalesMay'=>$ReturnSalesMay,
             'ReturnSalesJun'=>$ReturnSalesJun,
             'ReturnSalesJul'=>$ReturnSalesJul,
             'ReturnSalesAug'=>$ReturnSalesAug,
             'ReturnSalesSep'=>$ReturnSalesSep,
             'ReturnSalesOct'=>$ReturnSalesOct,
             'ReturnSalesNov'=>$ReturnSalesNov,
             'ReturnSalesDec'=>$ReturnSalesDec,

             'ReturnSalesSat'=>$ReturnSalesSat,
             'ReturnSalesSun'=>$ReturnSalesSun,
             'ReturnSalesMon'=>$ReturnSalesMon,
             'ReturnSalesTue'=>$ReturnSalesTue,
             'ReturnSalesWed'=>$ReturnSalesWed,
             'ReturnSalesThr'=>$ReturnSalesThr,
             'ReturnSalesFri'=>$ReturnSalesFri,

             'ReturnSalesPrevY1'=>$ReturnSalesPrevY1,
             'ReturnSalesPrevY2'=>$ReturnSalesPrevY2,
             'ReturnSalesPrevY3'=>$ReturnSalesPrevY3,
             'ReturnSalesPrevY4'=>$ReturnSalesPrevY4,
             'ReturnSalesPrevYC'=>$ReturnSalesPrevYC,

              'ReturnPurchJan'=>$ReturnPurchJan,
             'ReturnPurchFeb'=>$ReturnPurchFeb,
             'ReturnPurchMar'=>$ReturnPurchMar,
             'ReturnPurchApr'=>$ReturnPurchApr,
             'ReturnPurchMay'=>$ReturnPurchMay,
             'ReturnPurchJun'=>$ReturnPurchJun,
             'ReturnPurchJul'=>$ReturnPurchJul,
             'ReturnPurchAug'=>$ReturnPurchAug,
             'ReturnPurchSep'=>$ReturnPurchSep,
             'ReturnPurchOct'=>$ReturnPurchOct,
             'ReturnPurchNov'=>$ReturnPurchNov,
             'ReturnPurchDec'=>$ReturnPurchDec,

             'ReturnPurchSat'=>$ReturnPurchSat,
             'ReturnPurchSun'=>$ReturnPurchSun,
             'ReturnPurchMon'=>$ReturnPurchMon,
             'ReturnPurchTue'=>$ReturnPurchTue,
             'ReturnPurchWed'=>$ReturnPurchWed,
             'ReturnPurchThr'=>$ReturnPurchThr,
             'ReturnPurchFri'=>$ReturnPurchFri,

             'ReturnPurchPrevY1'=>$ReturnPurchPrevY1,
             'ReturnPurchPrevY2'=>$ReturnPurchPrevY2,
             'ReturnPurchPrevY3'=>$ReturnPurchPrevY3,
             'ReturnPurchPrevY4'=>$ReturnPurchPrevY4,
             'ReturnPurchPrevYC'=>$ReturnPurchPrevYC,



             'EgmalyMasrofatJan'=>$EgmalyMasrofatJan,
             'EgmalyMasrofatFeb'=>$EgmalyMasrofatFeb,
             'EgmalyMasrofatMar'=>$EgmalyMasrofatMar,
             'EgmalyMasrofatApr'=>$EgmalyMasrofatApr,
             'EgmalyMasrofatMay'=>$EgmalyMasrofatMay,
             'EgmalyMasrofatJun'=>$EgmalyMasrofatJun,
             'EgmalyMasrofatJul'=>$EgmalyMasrofatJul,
             'EgmalyMasrofatAug'=>$EgmalyMasrofatAug,
             'EgmalyMasrofatSep'=>$EgmalyMasrofatSep,
             'EgmalyMasrofatOct'=>$EgmalyMasrofatOct,
             'EgmalyMasrofatNov'=>$EgmalyMasrofatNov,
             'EgmalyMasrofatDec'=>$EgmalyMasrofatDec,

             'EgmalyMasrofatSat'=>$EgmalyMasrofatSat,
             'EgmalyMasrofatSun'=>$EgmalyMasrofatSun,
             'EgmalyMasrofatMon'=>$EgmalyMasrofatMon,
             'EgmalyMasrofatTue'=>$EgmalyMasrofatTue,
             'EgmalyMasrofatWed'=>$EgmalyMasrofatWed,
             'EgmalyMasrofatThr'=>$EgmalyMasrofatThr,
             'EgmalyMasrofatFri'=>$EgmalyMasrofatFri,

             'EgmalyMasrofatY1'=>$EgmalyMasrofatY1,
             'EgmalyMasrofatY2'=>$EgmalyMasrofatY2,
             'EgmalyMasrofatY3'=>$EgmalyMasrofatY3,
             'EgmalyMasrofatY4'=>$EgmalyMasrofatY4,
             'EgmalyMasrofatCY'=>$EgmalyMasrofatCY,


             'PaymentVoucherJan'=>$PaymentVoucherJan,
             'ReciptVoucherJan'=>$ReciptVoucherJan,
             'PaymentVoucherFeb'=>$PaymentVoucherFeb,
             'ReciptVoucherFeb'=>$ReciptVoucherFeb,
             'PaymentVoucherMar'=>$PaymentVoucherMar,
             'ReciptVoucherMar'=>$ReciptVoucherMar,
             'PaymentVoucherApr'=>$PaymentVoucherApr,
             'ReciptVoucherApr'=>$ReciptVoucherApr,
             'PaymentVoucherMay'=>$PaymentVoucherMay,
             'ReciptVoucherMay'=>$ReciptVoucherMay,
             'PaymentVoucherJun'=>$PaymentVoucherJun,
             'ReciptVoucherJun'=>$ReciptVoucherJun,
             'PaymentVoucherJul'=>$PaymentVoucherJul,
             'ReciptVoucherJul'=>$ReciptVoucherJul,
             'PaymentVoucherAug'=>$PaymentVoucherAug,
             'ReciptVoucherAug'=>$ReciptVoucherAug,
             'PaymentVoucherSep'=>$PaymentVoucherSep,
             'ReciptVoucherSep'=>$ReciptVoucherSep,
             'PaymentVoucherOct'=>$PaymentVoucherOct,
             'ReciptVoucherOct'=>$ReciptVoucherOct,
             'PaymentVoucherNov'=>$PaymentVoucherNov,
             'ReciptVoucherNov'=>$ReciptVoucherNov,
             'PaymentVoucherDec'=>$PaymentVoucherDec,
             'ReciptVoucherDec'=>$ReciptVoucherDec,
             'PaymentVoucherSat'=>$PaymentVoucherSat,
             'ReciptVoucherSat'=>$ReciptVoucherSat,
             'PaymentVoucherSun'=>$PaymentVoucherSun,
             'ReciptVoucherSun'=>$ReciptVoucherSun,
             'PaymentVoucherMon'=>$PaymentVoucherMon,
             'ReciptVoucherMon'=>$ReciptVoucherMon,
             'PaymentVoucherTue'=>$PaymentVoucherTue,
             'ReciptVoucherTue'=>$ReciptVoucherTue,
             'PaymentVoucherWed'=>$PaymentVoucherWed,
             'ReciptVoucherWed'=>$ReciptVoucherWed,
             'PaymentVoucherThr'=>$PaymentVoucherThr,
             'ReciptVoucherThr'=>$ReciptVoucherThr,
             'PaymentVoucherFri'=>$PaymentVoucherFri,
             'ReciptVoucherFri'=>$ReciptVoucherFri,
             'IncomChecksJan'=>$IncomChecksJan,
             'ExportChecksJan'=>$ExportChecksJan,
             'IncomChecksFeb'=>$IncomChecksFeb,
             'ExportChecksFeb'=>$ExportChecksFeb,
             'IncomChecksMar'=>$IncomChecksMar,
             'ExportChecksMar'=>$ExportChecksMar,
             'IncomChecksApr'=>$IncomChecksApr,
             'ExportChecksApr'=>$ExportChecksApr,
             'IncomChecksMay'=>$IncomChecksMay,
             'ExportChecksMay'=>$ExportChecksMay,
             'IncomChecksJun'=>$IncomChecksJun,
             'ExportChecksJun'=>$ExportChecksJun,
             'IncomChecksJul'=>$IncomChecksJul,
             'ExportChecksJul'=>$ExportChecksJul,
             'IncomChecksAug'=>$IncomChecksAug,
             'ExportChecksAug'=>$ExportChecksAug,
             'IncomChecksSep'=>$IncomChecksSep,
             'ExportChecksSep'=>$ExportChecksSep,
             'IncomChecksOct'=>$IncomChecksOct,
             'ExportChecksOct'=>$ExportChecksOct,
             'IncomChecksNov'=>$IncomChecksNov,
             'ExportChecksNov'=>$ExportChecksNov,
             'IncomChecksDec'=>$IncomChecksDec,
             'ExportChecksDec'=>$ExportChecksDec,
             'IncomChecksSat'=>$IncomChecksSat,
             'ExportChecksSat'=>$ExportChecksSat,
             'IncomChecksSun'=>$IncomChecksSun,
             'ExportChecksSun'=>$ExportChecksSun,
             'IncomChecksMon'=>$IncomChecksMon,
             'ExportChecksMon'=>$ExportChecksMon,
             'IncomChecksTue'=>$IncomChecksTue,
             'ExportChecksTue'=>$ExportChecksTue,
             'IncomChecksWed'=>$IncomChecksWed,
             'ExportChecksWed'=>$ExportChecksWed,
             'IncomChecksThr'=>$IncomChecksThr,
             'ExportChecksThr'=>$ExportChecksThr,
             'IncomChecksFri'=>$IncomChecksFri,
             'ExportChecksFri'=>$ExportChecksFri,
             'SafyRab7Jan'=>$SafyRab7Jan,
             'SafyRab7Feb'=>$SafyRab7Feb,
             'SafyRab7Mar'=>$SafyRab7Mar,
             'SafyRab7Apr'=>$SafyRab7Apr,
             'SafyRab7May'=>$SafyRab7May,
             'SafyRab7Jun'=>$SafyRab7Jun,
             'SafyRab7Jul'=>$SafyRab7Jul,
             'SafyRab7Aug'=>$SafyRab7Aug,
             'SafyRab7Sep'=>$SafyRab7Sep,
             'SafyRab7Oct'=>$SafyRab7Oct,
             'SafyRab7Nov'=>$SafyRab7Nov,
             'SafyRab7Dec'=>$SafyRab7Dec,

             'Groups'=>$Groups,
             'Brands'=>$Brands,


         ]);
    }

          public function BriefsAdmin(){
         return view('admin.BriefsAdmin');
    }

        public function StatisticsTotal(){



            //Sales
               $TotalSalesToday=Sales::orderBy('id','desc')
            ->where('Date',date('Y-m-d'))
            ->where('Status',1)
          ->where('KitchenEnd',1)->where('RecivedOrder',1)
            ->get()->sum('Total_Price');



            $TotalSalesThisMonth=Sales::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-m-01'),date('Y-m-d')])
            ->where('Status',1)
          ->where('KitchenEnd',1)->where('RecivedOrder',1)
            ->get()->sum('Total_Price');


                $TotalSalesThisYear=Sales::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-01-01'),date('Y-m-d')])
            ->where('Status',1)
          ->where('KitchenEnd',1)->where('RecivedOrder',1)
            ->get()->sum('Total_Price');


        $TotalSalesLast3Months=Sales::orderBy('id','desc')
            ->whereBetween('Date',[date("Y-m-d",strtotime("-3 Months")),date('Y-m-d')])
            ->where('Status',1)
          ->where('KitchenEnd',1)->where('RecivedOrder',1)
            ->get()->sum('Total_Price');


            //Purchases
              $TotalPurchasesToday=Purchases::orderBy('id','desc')
            ->where('Date',date('Y-m-d'))
            ->where('Status',1)
            ->get()->sum('Total_Price');



            $TotalPurchasesThisMonth=Purchases::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-m-01'),date('Y-m-d')])
            ->where('Status',1)
            ->get()->sum('Total_Price');


                $TotalPurchasesThisYear=Purchases::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-01-01'),date('Y-m-d')])
            ->where('Status',1)
            ->get()->sum('Total_Price');


        $TotalPurchasesLast3Months=Purchases::orderBy('id','desc')
            ->whereBetween('Date',[date("Y-m-d",strtotime("-3 Months")),date('Y-m-d')])
            ->where('Status',1)
            ->get()->sum('Total_Price');



            //ReturnSales

            $TotalReturnSalesToday=ReturnSales::orderBy('id','desc')
            ->where('Date',date('Y-m-d'))
            ->get()->sum('Total_Return_Value');



            $TotalReturnSalesThisMonth=ReturnSales::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-m-01'),date('Y-m-d')])
            ->get()->sum('Total_Return_Value');


                $TotalReturnSalesThisYear=ReturnSales::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-01-01'),date('Y-m-d')])
            ->get()->sum('Total_Return_Value');


        $TotalReturnSalesLast3Months=ReturnSales::orderBy('id','desc')
            ->whereBetween('Date',[date("Y-m-d",strtotime("-3 Months")),date('Y-m-d')])
            ->get()->sum('Total_Return_Value');

            //ReturnPurch
             $TotalReturnPurchToday=ReturnPurch::orderBy('id','desc')
            ->where('Date',date('Y-m-d'))
            ->get()->sum('Total_Return_Value');



            $TotalReturnPurchThisMonth=ReturnPurch::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-m-01'),date('Y-m-d')])
            ->get()->sum('Total_Return_Value');


                $TotalReturnPurchThisYear=ReturnPurch::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-01-01'),date('Y-m-d')])
            ->get()->sum('Total_Return_Value');


        $TotalReturnPurchLast3Months=ReturnPurch::orderBy('id','desc')
            ->whereBetween('Date',[date("Y-m-d",strtotime("-3 Months")),date('Y-m-d')])
            ->get()->sum('Total_Return_Value');




            $Q1 = date('Y-m-d', strtotime(date('Y-01-01'). ' + 3 months'));
            $Q2 = date('Y-m-d', strtotime(date('Y-04-01'). ' + 3 months'));
            $Q3 = date('Y-m-d', strtotime(date('Y-07-01'). ' + 3 months'));
            $Q4 = date('Y-m-d', strtotime(date('Y-10-01'). ' + 3 months'));


              $H1 = date('Y-m-d', strtotime(date('Y-01-01'). ' + 6 months'));
              $H2 = date('Y-m-d', strtotime(date('Y-07-01'). ' + 6 months'));


            //Sales
                    $TotalSalesFirstQuarter=Sales::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-01-01'),$Q1])
            ->where('Status',1)
          ->where('KitchenEnd',1)->where('RecivedOrder',1)
            ->get()->sum('Total_Price');

                          $TotalSalesSecondQuarter=Sales::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-04-01'),$Q2])
            ->where('Status',1)
          ->where('KitchenEnd',1)->where('RecivedOrder',1)
            ->get()->sum('Total_Price');

                          $TotalSalesThirdQuarter=Sales::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-07-01'),$Q3])
            ->where('Status',1)
          ->where('KitchenEnd',1)->where('RecivedOrder',1)
            ->get()->sum('Total_Price');

                          $TotalSalesFourthQuarter=Sales::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-10-01'),$Q4])
            ->where('Status',1)
          ->where('KitchenEnd',1)->where('RecivedOrder',1)
            ->get()->sum('Total_Price');



                $TotalSalesFirstHalf=Sales::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-04-01'),$H1])
            ->where('Status',1)
          ->where('KitchenEnd',1)->where('RecivedOrder',1)
            ->get()->sum('Total_Price');

                          $TotalSalesSecondHalf=Sales::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-07-01'),$H2])
            ->where('Status',1)
          ->where('KitchenEnd',1)->where('RecivedOrder',1)
            ->get()->sum('Total_Price');



                      $TotalSalesTodayCash=Sales::orderBy('id','desc')
            ->where('Date',date('Y-m-d'))
            ->where('Status',1)
            ->where('Payment_Method','Cash')
          ->where('KitchenEnd',1)->where('RecivedOrder',1)
            ->get()->sum('Total_Price');


                                  $TotalSalesTodayLater=Sales::orderBy('id','desc')
            ->where('Date',date('Y-m-d'))
            ->where('Status',1)
            ->where('Payment_Method','Later')
          ->where('KitchenEnd',1)->where('RecivedOrder',1)
            ->get()->sum('Total_Price');


               //Purchases

               $TotalPurchasesFirstQuarter=Purchases::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-01-01'),$Q1])
            ->where('Status',1)
            ->get()->sum('Total_Price');

                          $TotalPurchasesSecondQuarter=Purchases::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-04-01'),$Q2])
            ->where('Status',1)
            ->get()->sum('Total_Price');

                          $TotalPurchasesThirdQuarter=Purchases::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-07-01'),$Q3])
            ->where('Status',1)
            ->get()->sum('Total_Price');

                          $TotalPurchasesFourthQuarter=Purchases::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-10-01'),$Q4])
            ->where('Status',1)
            ->get()->sum('Total_Price');



                $TotalPurchasesFirstHalf=Purchases::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-04-01'),$H1])
            ->where('Status',1)
            ->get()->sum('Total_Price');

                          $TotalPurchasesSecondHalf=Purchases::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-07-01'),$H2])
            ->where('Status',1)

            ->get()->sum('Total_Price');



                      $TotalPurchasesTodayCash=Purchases::orderBy('id','desc')
            ->where('Date',date('Y-m-d'))
            ->where('Status',1)
            ->where('Payment_Method','Cash')
            ->get()->sum('Total_Price');


                                  $TotalPurchasesTodayLater=Purchases::orderBy('id','desc')
            ->where('Date',date('Y-m-d'))
            ->where('Status',1)
            ->where('Payment_Method','Later')
            ->get()->sum('Total_Price');




                   //ReturnSales
                      $TotalReturnSalesFirstQuarter=ReturnSales::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-01-01'),$Q1])
            ->get()->sum('Total_Return_Value');

                          $TotalReturnSalesSecondQuarter=ReturnSales::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-04-01'),$Q2])
            ->get()->sum('Total_Return_Value');

                          $TotalReturnSalesThirdQuarter=ReturnSales::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-07-01'),$Q3])
            ->get()->sum('Total_Return_Value');

                          $TotalReturnSalesFourthQuarter=ReturnSales::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-10-01'),$Q4])
            ->get()->sum('Total_Return_Value');



                $TotalReturnSalesFirstHalf=ReturnSales::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-04-01'),$H1])
            ->get()->sum('Total_Return_Value');

                          $TotalReturnSalesSecondHalf=ReturnSales::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-07-01'),$H2])
            ->get()->sum('Total_Return_Value');



                      $TotalReturnSalesTodayCash=ReturnSales::orderBy('id','desc')
            ->where('Date',date('Y-m-d'))
            ->where('Payment_Method','Cash')
            ->get()->sum('Total_Return_Value');


                                  $TotalReturnSalesTodayLater=ReturnSales::orderBy('id','desc')
            ->where('Date',date('Y-m-d'))
            ->where('Payment_Method','Later')
            ->get()->sum('Total_Return_Value');



   //ReturnPurch
                                  $TotalReturnPurchFirstQuarter=ReturnPurch::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-01-01'),$Q1])
            ->get()->sum('Total_Return_Value');

                          $TotalReturnPurchSecondQuarter=ReturnPurch::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-04-01'),$Q2])
            ->get()->sum('Total_Return_Value');

                          $TotalReturnPurchThirdQuarter=ReturnPurch::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-07-01'),$Q3])
            ->get()->sum('Total_Return_Value');

                          $TotalReturnPurchFourthQuarter=ReturnPurch::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-10-01'),$Q4])
            ->get()->sum('Total_Return_Value');



                $TotalReturnPurchFirstHalf=ReturnPurch::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-04-01'),$H1])
            ->get()->sum('Total_Return_Value');

                          $TotalReturnPurchSecondHalf=ReturnPurch::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-07-01'),$H2])
            ->get()->sum('Total_Return_Value');



                      $TotalReturnPurchTodayCash=ReturnPurch::orderBy('id','desc')
            ->where('Date',date('Y-m-d'))
            ->where('Payment_Method','Cash')
            ->get()->sum('Total_Return_Value');


                                  $TotalReturnPurchTodayLater=ReturnPurch::orderBy('id','desc')
            ->where('Date',date('Y-m-d'))
            ->where('Payment_Method','Later')
            ->get()->sum('Total_Return_Value');



            // ======  Vouchers   ===============



        $PaymentVoucherToday=GeneralDaily::where('Date', date('Y-m-d'))
          ->where('Type','سند صرف')
          ->get()->sum('Debitor_Coin');

    $ReciptVoucherToday=GeneralDaily::where('Date', date('Y-m-d'))
        ->where('Type','سند قبض')
          ->get()->sum('Creditor_Coin');



             $PaymentVoucherMonthly=GeneralDaily::whereBetween('Date',[date('Y-m-01'),date('Y-m-d')])
          ->where('Type','سند صرف')
          ->get()->sum('Debitor_Coin');


             $ReciptVoucherMonthly=GeneralDaily::whereBetween('Date',[date('Y-m-01'),date('Y-m-d')])
        ->where('Type','سند قبض')
          ->get()->sum('Creditor_Coin');



             $PaymentVoucherYearly=GeneralDaily::whereBetween('Date',[date('Y-01-01'),date('Y-m-d')])
          ->where('Type','سند صرف')
          ->get()->sum('Debitor_Coin');



             $ReciptVoucherYearly=GeneralDaily::whereBetween('Date',[date('Y-01-01'),date('Y-m-d')])
        ->where('Type','سند قبض')
          ->get()->sum('Creditor_Coin');



                $PaymentVoucherLast3Month=GeneralDaily::whereBetween('Date',[date("Y-m-d",strtotime("-3 Months")),date('Y-m-d')])
          ->where('Type','سند صرف')
          ->get()->sum('Debitor_Coin');




             $ReciptVoucherLast3Month=GeneralDaily::whereBetween('Date',[date("Y-m-d",strtotime("-3 Months")),date('Y-m-d')])
        ->where('Type','سند قبض')
          ->get()->sum('Creditor_Coin');



           $PaymentVoucherQ1=GeneralDaily::whereBetween('Date',[date('Y-01-01'),$Q1])
          ->where('Type','سند صرف')
          ->get()->sum('Debitor_Coin');



             $ReciptVoucherQ1=GeneralDaily::whereBetween('Date',[date('Y-01-01'),$Q1])
        ->where('Type','سند قبض')
          ->get()->sum('Creditor_Coin');




             $PaymentVoucherQ2=GeneralDaily::whereBetween('Date',[date('Y-04-01'),$Q2])
          ->where('Type','سند صرف')
          ->get()->sum('Debitor_Coin');



             $ReciptVoucherQ2=GeneralDaily::whereBetween('Date',[date('Y-04-01'),$Q2])
        ->where('Type','سند قبض')
          ->get()->sum('Creditor_Coin');

         $PaymentVoucherQ3=GeneralDaily::whereBetween('Date',[date('Y-07-01'),$Q3])
          ->where('Type','سند صرف')
          ->get()->sum('Debitor_Coin');



             $ReciptVoucherQ3=GeneralDaily::whereBetween('Date',[date('Y-07-01'),$Q3])
        ->where('Type','سند قبض')
          ->get()->sum('Creditor_Coin');


              $PaymentVoucherQ4=GeneralDaily::whereBetween('Date',[date('Y-10-01'),$Q4])
          ->where('Type','سند صرف')
          ->get()->sum('Debitor_Coin');




             $ReciptVoucherQ4=GeneralDaily::whereBetween('Date',[date('Y-10-01'),$Q4])
        ->where('Type','سند قبض')
          ->get()->sum('Creditor_Coin');



                 $PaymentVoucherH1=GeneralDaily::whereBetween('Date',[date('Y-04-01'),$H1])
          ->where('Type','سند صرف')
          ->get()->sum('Debitor_Coin');



             $ReciptVoucherH1=GeneralDaily::whereBetween('Date',[date('Y-04-01'),$H1])
        ->where('Type','سند قبض')
          ->get()->sum('Creditor_Coin');



                   $PaymentVoucherH2=GeneralDaily::whereBetween('Date',[date('Y-07-01'),$H2])
          ->where('Type','سند صرف')
          ->get()->sum('Debitor_Coin');



             $ReciptVoucherH2=GeneralDaily::whereBetween('Date',[date('Y-07-01'),$H2])
        ->where('Type','سند قبض')
          ->get()->sum('Creditor_Coin');



            //InsurancePaper
             $TotalInsurancePaperToday=InsurancePaper::orderBy('id','desc')
            ->where('Date',date('Y-m-d'))
            ->get()->sum('Amount');



            $TotalInsurancePaperThisMonth=InsurancePaper::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-m-01'),date('Y-m-d')])
            ->get()->sum('Amount');


                $TotalInsurancePaperThisYear=InsurancePaper::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-01-01'),date('Y-m-d')])
            ->get()->sum('Amount');


        $TotalInsurancePaperLast3Months=InsurancePaper::orderBy('id','desc')
            ->whereBetween('Date',[date("Y-m-d",strtotime("-3 Months")),date('Y-m-d')])
            ->get()->sum('Amount');



                  $TotalInsurancePaperFirstQuarter=InsurancePaper::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-01-01'),$Q1])
            ->get()->sum('Amount');

                          $TotalInsurancePaperSecondQuarter=InsurancePaper::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-04-01'),$Q2])
            ->get()->sum('Amount');

                          $TotalInsurancePaperThirdQuarter=InsurancePaper::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-07-01'),$Q3])
            ->get()->sum('Amount');

                          $TotalInsurancePaperFourthQuarter=InsurancePaper::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-10-01'),$Q4])
            ->get()->sum('Amount');



                $TotalInsurancePaperFirstHalf=InsurancePaper::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-04-01'),$H1])
            ->get()->sum('Amount');

                          $TotalInsurancePaperSecondHalf=InsurancePaper::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-07-01'),$H2])
            ->get()->sum('Amount');


                       $TotalInsurancePaperCollected=InsurancePaper::orderBy('id','desc')
            ->where('Status',1)
            ->get()->sum('Amount');






            //IncomChecks
               $TotalIncomChecksToday=IncomChecks::orderBy('id','desc')
            ->where('Date',date('Y-m-d'))
            ->get()->sum('Amount');



            $TotalIncomChecksThisMonth=IncomChecks::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-m-01'),date('Y-m-d')])
            ->get()->sum('Amount');


                $TotalIncomChecksThisYear=IncomChecks::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-01-01'),date('Y-m-d')])
            ->get()->sum('Amount');


        $TotalIncomChecksLast3Months=IncomChecks::orderBy('id','desc')
            ->whereBetween('Date',[date("Y-m-d",strtotime("-3 Months")),date('Y-m-d')])
            ->get()->sum('Amount');



                  $TotalIncomChecksFirstQuarter=IncomChecks::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-01-01'),$Q1])
            ->get()->sum('Amount');

                          $TotalIncomChecksSecondQuarter=IncomChecks::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-04-01'),$Q2])
            ->get()->sum('Amount');

                          $TotalIncomChecksThirdQuarter=IncomChecks::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-07-01'),$Q3])
            ->get()->sum('Amount');

                          $TotalIncomChecksFourthQuarter=IncomChecks::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-10-01'),$Q4])
            ->get()->sum('Amount');



                $TotalIncomChecksFirstHalf=IncomChecks::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-04-01'),$H1])
            ->get()->sum('Amount');

                          $TotalIncomChecksSecondHalf=IncomChecks::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-07-01'),$H2])
            ->get()->sum('Amount');


                       $TotalIncomChecksCollected=IncomChecks::orderBy('id','desc')
            ->where('Status',1)
            ->get()->sum('Amount');


            //ExportChecks
               $TotalExportChecksToday=ExportChecks::orderBy('id','desc')
            ->where('Date',date('Y-m-d'))
            ->get()->sum('Amount');



            $TotalExportChecksThisMonth=ExportChecks::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-m-01'),date('Y-m-d')])
            ->get()->sum('Amount');


                $TotalExportChecksThisYear=ExportChecks::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-01-01'),date('Y-m-d')])
            ->get()->sum('Amount');


        $TotalExportChecksLast3Months=ExportChecks::orderBy('id','desc')
            ->whereBetween('Date',[date("Y-m-d",strtotime("-3 Months")),date('Y-m-d')])
            ->get()->sum('Amount');



                  $TotalExportChecksFirstQuarter=ExportChecks::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-01-01'),$Q1])
            ->get()->sum('Amount');

                          $TotalExportChecksSecondQuarter=ExportChecks::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-04-01'),$Q2])
            ->get()->sum('Amount');

                          $TotalExportChecksThirdQuarter=ExportChecks::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-07-01'),$Q3])
            ->get()->sum('Amount');

                          $TotalExportChecksFourthQuarter=ExportChecks::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-10-01'),$Q4])
            ->get()->sum('Amount');



                $TotalExportChecksFirstHalf=ExportChecks::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-04-01'),$H1])
            ->get()->sum('Amount');

                          $TotalExportChecksSecondHalf=ExportChecks::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-07-01'),$H2])
            ->get()->sum('Amount');


                       $TotalExportChecksCollected=ExportChecks::orderBy('id','desc')
            ->where('Status',1)
            ->get()->sum('Amount');


            //Stores



                       $TotalStoreCost=ProductsQty::orderBy('id','desc')
            ->get()->sum('TotalCost');


                      $TotalStoreQty=ProductsQty::orderBy('id','desc')
            ->get()->sum('Qty');


//========================== Masrofat ==============
                  $AMsrfoat=AcccountingManual::where('id',20)->first();

        if($AMsrfoat->Parent == 0){

          $wordsss=$AMsrfoat->Code.'0';

        }else{

           $wordsss=$AMsrfoat->Code;

        }


   $TotalDebMasrofatToday = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {

            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->where('general_dailies.Date',date('Y-m-d'));
        })

          ->where('acccounting_manuals.Code', 'like', $wordsss.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Debitor_Coin');



        $TotalCredMasrofatToday = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {

            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->where('general_dailies.Date',date('Y-m-d'));
        })

          ->where('acccounting_manuals.Code', 'like', $wordsss.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Creditor_Coin');



            $EgmalyMasrofatToday=  $TotalDebMasrofatToday -  $TotalCredMasrofatToday ;


              $TotalDebMasrofatMonthly = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {

            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->whereBetween('general_dailies.Date',[date('Y-m-01'),date('Y-m-d')]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordsss.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Debitor_Coin');



        $TotalCredMasrofatMonthly = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {

            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->whereBetween('general_dailies.Date',[date('Y-m-01'),date('Y-m-d')]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordsss.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Creditor_Coin');



            $EgmalyMasrofatMonthly=  $TotalDebMasrofatMonthly -  $TotalCredMasrofatMonthly ;



              $TotalDebMasrofatYearly = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {

            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->whereBetween('general_dailies.Date',[date('Y-01-01'),date('Y-m-d')]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordsss.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Debitor_Coin');



        $TotalCredMasrofatYearly = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {

            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->whereBetween('general_dailies.Date',[date('Y-01-01'),date('Y-m-d')]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordsss.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Creditor_Coin');



            $EgmalyMasrofatYearly=  $TotalDebMasrofatYearly -  $TotalCredMasrofatYearly ;



                  $TotalDebMasrofatLast3Month = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {

            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->whereBetween('general_dailies.Date',[date("Y-m-d",strtotime("-3 Months")),date('Y-m-d')]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordsss.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Debitor_Coin');



        $TotalCredMasrofatLast3Month = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {

            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->whereBetween('general_dailies.Date',[date("Y-m-d",strtotime("-3 Months")),date('Y-m-d')]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordsss.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Creditor_Coin');



            $EgmalyMasrofatLast3Month=  $TotalDebMasrofatLast3Month -  $TotalCredMasrofatLast3Month ;


                  $TotalDebMasrofatQ1 = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {
                 $Q1 = date('Y-m-d', strtotime(date('Y-01-01'). ' + 3 months'));
            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->whereBetween('general_dailies.Date',[date('Y-01-01'),$Q1]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordsss.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Debitor_Coin');



        $TotalCredMasrofatQ1 = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {
            $Q1 = date('Y-m-d', strtotime(date('Y-01-01'). ' + 3 months'));
            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->whereBetween('general_dailies.Date',[date('Y-01-01'),$Q1]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordsss.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Creditor_Coin');



            $EgmalyMasrofatQ1=  $TotalDebMasrofatQ1 -  $TotalCredMasrofatQ1 ;


                $TotalDebMasrofatQ2 = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {
              $Q2 = date('Y-m-d', strtotime(date('Y-04-01'). ' + 3 months'));
            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->whereBetween('general_dailies.Date',[date('Y-04-01'),$Q2]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordsss.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Debitor_Coin');



        $TotalCredMasrofatQ2 = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {
             $Q2 = date('Y-m-d', strtotime(date('Y-04-01'). ' + 3 months'));
            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->whereBetween('general_dailies.Date',[date('Y-04-01'),$Q2]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordsss.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Creditor_Coin');



            $EgmalyMasrofatQ2=  $TotalDebMasrofatQ2 -  $TotalCredMasrofatQ2 ;



                  $TotalDebMasrofatQ3 = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {
            $Q3 = date('Y-m-d', strtotime(date('Y-07-01'). ' + 3 months'));
            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->whereBetween('general_dailies.Date',[date('Y-07-01'),$Q3]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordsss.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Debitor_Coin');



        $TotalCredMasrofatQ3 = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {
            $Q3 = date('Y-m-d', strtotime(date('Y-07-01'). ' + 3 months'));
            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->whereBetween('general_dailies.Date',[date('Y-07-01'),$Q3]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordsss.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Creditor_Coin');



            $EgmalyMasrofatQ3=  $TotalDebMasrofatQ3 -  $TotalCredMasrofatQ3 ;



                  $TotalDebMasrofatQ4 = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {
            $Q4 = date('Y-m-d', strtotime(date('Y-10-01'). ' + 3 months'));
            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->whereBetween('general_dailies.Date',[date('Y-10-01'),$Q4]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordsss.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Debitor_Coin');



        $TotalCredMasrofatQ4 = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {
            $Q4 = date('Y-m-d', strtotime(date('Y-10-01'). ' + 3 months'));
            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->whereBetween('general_dailies.Date',[date('Y-10-01'),$Q4]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordsss.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Creditor_Coin');



            $EgmalyMasrofatQ4=  $TotalDebMasrofatQ4 -  $TotalCredMasrofatQ4 ;



                $TotalDebMasrofatH1 = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {
              $H1 = date('Y-m-d', strtotime(date('Y-01-01'). ' + 6 months'));
            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->whereBetween('general_dailies.Date',[date('Y-04-01'),$H1]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordsss.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Debitor_Coin');



        $TotalCredMasrofatH1 = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {
              $H1 = date('Y-m-d', strtotime(date('Y-01-01'). ' + 6 months'));
            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->whereBetween('general_dailies.Date',[date('Y-04-01'),$H1]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordsss.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Creditor_Coin');



            $EgmalyMasrofatH1=  $TotalDebMasrofatH1 -  $TotalCredMasrofatH1 ;


               $TotalDebMasrofatH2 = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {
            $H2 = date('Y-m-d', strtotime(date('Y-07-01'). ' + 6 months'));
            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->whereBetween('general_dailies.Date',[date('Y-07-01'),$H2]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordsss.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Debitor_Coin');



        $TotalCredMasrofatH2 = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {
            $H2 = date('Y-m-d', strtotime(date('Y-07-01'). ' + 6 months'));
            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->whereBetween('general_dailies.Date',[date('Y-07-01'),$H2]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordsss.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Creditor_Coin');



            $EgmalyMasrofatH2=  $TotalDebMasrofatH2 -  $TotalCredMasrofatH2 ;


            //Others

                       $AEryd=AcccountingManual::where('id',18)->first();


        if($AEryd->Parent == 0){

          $words=$AEryd->Code.'0';

        }else{

           $words=$AEryd->Code;

        }
        $TotalDebErydat = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {

                  $to = date('Y-m-d');
                 $from = date('Y-01-01');
            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->whereBetween('general_dailies.Date',[$from,$to]);
        })

          ->where('acccounting_manuals.Code', 'like', $words.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Debitor_Coin');



        $TotalCredErydat = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {

                  $to = date('Y-m-d');
                 $from = date('Y-01-01');
            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->whereBetween('general_dailies.Date',[$from,$to]);
        })

          ->where('acccounting_manuals.Code', 'like', $words.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Creditor_Coin');



            $EgmalyErydaat=  $TotalCredErydat -  $TotalDebErydat ;


   $ATaklfa=AcccountingManual::where('id',19)->first();

        if($ATaklfa->Parent == 0){

          $wordss=$ATaklfa->Code.'0';

        }else{

           $wordss=$ATaklfa->Code;

        }

            $TotalDebTaklfa = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {

                  $to = date('Y-m-d');
                 $from = date('Y-01-01');
            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->whereBetween('general_dailies.Date',[$from,$to]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordss.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Debitor_Coin');



        $TotalCredTaklfa = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {

                   $to = date('Y-m-d');
                 $from = date('Y-01-01');
            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->whereBetween('general_dailies.Date',[$from,$to]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordss.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Creditor_Coin');


   $EgmalyTaklfa=  $TotalDebTaklfa -  $TotalCredTaklfa ;
  $Mogml = $EgmalyErydaat   -  $EgmalyTaklfa   ;



              $ASabta=AcccountingManual::where('id',21)->first();
        if($ASabta->Parent == 0){

          $wordsSabta=$ASabta->Code.'0';

        }else{

           $wordsSabta=$ASabta->Code;

        }

        $TotalDebAsoulSabta = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {

                $to = date('Y-m-d');
            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->where('general_dailies.Date', '<=', $to);
        })

          ->where('acccounting_manuals.Code', 'like', $wordsSabta.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Debitor_Coin');



        $TotalCredAsoulSabta = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {

                  $to = date('Y-m-d');
            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->where('general_dailies.Date', '<=', $to);
        })

          ->where('acccounting_manuals.Code', 'like', $wordsSabta.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Creditor_Coin');



           $EgmalyAsoulSabta=$TotalDebAsoulSabta - $TotalCredAsoulSabta ;
  $Amtdawla=AcccountingManual::where('id',22)->first();

        if($Amtdawla->Parent == 0){

          $wordsMtdawla=$Amtdawla->Code.'0';

        }else{

           $wordsMtdawla=$Amtdawla->Code;

        }

              $TotalDebAsoulMtadawla = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {

                  $to =date('Y-m-d');
            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->where('general_dailies.Date', '<=', $to);
        })

          ->where('acccounting_manuals.Code', 'like', $wordsMtdawla.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Debitor_Coin');



        $TotalCredAsoulMtadawla = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {

                    $to =date('Y-m-d');
            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->where('general_dailies.Date', '<=', $to);
        })

          ->where('acccounting_manuals.Code', 'like', $wordsMtdawla.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Creditor_Coin');



           $EgmalyAsoulMtadawla=$TotalDebAsoulMtadawla - $TotalCredAsoulMtadawla ;
  $AOther=AcccountingManual::where('id',147)->first();

        if($AOther->Parent == 0){

          $wordsOther=$AOther->Code.'0';

        }else{

           $wordsOther=$AOther->Code;

        }


                $TotalDebAsoulOkhraa = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {

                  $to =date('Y-m-d');
            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->where('general_dailies.Date', '<=', $to);
        })

          ->where('acccounting_manuals.Code', 'like', $wordsOther.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Debitor_Coin');



        $TotalCredAsoulOkhraa = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {

                $to =date('Y-m-d');
            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->where('general_dailies.Date', '<=', $to);
        })

          ->where('acccounting_manuals.Code', 'like', $wordsOther.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Creditor_Coin');



           $EgmalyAsoulOkhraa=$TotalDebAsoulOkhraa - $TotalCredAsoulOkhraa ;
   $EgmalyAsoul=$EgmalyAsoulSabta + $EgmalyAsoulMtadawla + $EgmalyAsoulOkhraa ;
$ATaklfa=AcccountingManual::where('id',16)->first();

        if($ATaklfa->Parent == 0){

          $wordss=$ATaklfa->Code.'0';

        }else{

           $wordss=$ATaklfa->Code;

        }

         $TotalDebKhsoum = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {

                   $to =date('Y-m-d');
            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->where('general_dailies.Date', '<=', $to);
        })

          ->where('acccounting_manuals.Code', 'like', $wordss.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Debitor_Coin');



        $TotalCredKhsoum = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {

                $to =date('Y-m-d');
            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->where('general_dailies.Date', '<=', $to);
        })

          ->where('acccounting_manuals.Code', 'like', $wordss.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Creditor_Coin');



           $EgmalyKhsoum= $TotalCredKhsoum  - $TotalDebKhsoum ;
        $ATaklfaTweel=AcccountingManual::where('id',148)->first();

        if($ATaklfaTweel->Parent == 0){

          $wordssTweel=$ATaklfaTweel->Code.'0';

        }else{

           $wordssTweel=$ATaklfaTweel->Code;

        }


                $TotalDebKhsoumTweel = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {

                  $to =date('Y-m-d');
            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->where('general_dailies.Date', '<=', $to);
        })

          ->where('acccounting_manuals.Code', 'like', $wordssTweel.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Debitor_Coin');



        $TotalCredKhsoumTweel = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {

                $to =date('Y-m-d');
            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->where('general_dailies.Date', '<=', $to);
        })

          ->where('acccounting_manuals.Code', 'like', $wordssTweel.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Creditor_Coin');



           $EgmalyKhsoumTweel= $TotalCredKhsoumTweel  - $TotalDebKhsoumTweel ;
 $EgmalyKhsoumQaserr= $EgmalyKhsoum -  $EgmalyKhsoumTweel;
    $RasMal3aml=$EgmalyAsoulMtadawla - $EgmalyKhsoumQaserr;
 $Estsmar=$RasMal3aml + $EgmalyAsoulSabta + $EgmalyAsoulOkhraa ;
 $AMsrfoat=AcccountingManual::where('id',17)->first();

        if($AMsrfoat->Parent == 0){

          $wordsss=$AMsrfoat->Code.'0';

        }else{

           $wordsss=$AMsrfoat->Code;

        }
  $AErydaaat=AcccountingManual::where('id',18)->first();

        if($AErydaaat->Parent == 0){

          $wordsX=$AErydaaat->Code.'0';

        }else{

           $wordsX=$AEryd->Code;

        }


    $ATaklfaaat=AcccountingManual::where('id',19)->first();

        if($ATaklfaaat->Parent == 0){

          $wordssX=$ATaklfaaat->Code.'0';

        }else{

           $wordssX=$ATaklfaaat->Code;

        }


        $AMasroffffffffat=AcccountingManual::where('id',20)->first();

        if($AMasroffffffffat->Parent == 0){

          $wordsssX=$AMasroffffffffat->Code.'0';

        }else{

           $wordsssX=$AMasroffffffffat->Code;

        }


              $TotalDebErydat = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {

                       $to =date('Y-m-d');
            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->where('general_dailies.Date', '<=', $to);
        })

          ->where('acccounting_manuals.Code', 'like', $wordsX.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Debitor_Coin');



        $TotalCredErydat = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {

                       $to =date('Y-m-d');
            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->where('general_dailies.Date', '<=', $to);
        })

          ->where('acccounting_manuals.Code', 'like', $wordsX.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Creditor_Coin');

        $EgmalyErydat=$TotalCredErydat - $TotalDebErydat  ;




              $TotalDebTaklfaat = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {

                    $to =date('Y-m-d');
            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->where('general_dailies.Date', '<=', $to);
        })

          ->where('acccounting_manuals.Code', 'like', $wordssX.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Debitor_Coin');



        $TotalCredTaklfaat = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {

                  $to =date('Y-m-d');
            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->where('general_dailies.Date', '<=', $to);
        })

          ->where('acccounting_manuals.Code', 'like', $wordssX.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Creditor_Coin');

        $EgmalyTaklfaat=$TotalDebTaklfaat - $TotalCredTaklfaat  ;


           $TotalDebMasrofaat = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {

                       $to =date('Y-m-d');
            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->where('general_dailies.Date', '<=', $to);
        })

          ->where('acccounting_manuals.Code', 'like', $wordsssX.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Debitor_Coin');



        $TotalCredMasrofaat = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {

                     $to =date('Y-m-d');
            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->where('general_dailies.Date', '<=', $to);
        })

          ->where('acccounting_manuals.Code', 'like', $wordsssX.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Creditor_Coin');

        $EgmalyMasrofaat=$TotalDebMasrofaat - $TotalCredMasrofaat  ;


        $fg= $EgmalyTaklfaat + $EgmalyMasrofaat;
        if($EgmalyErydat >  $fg){
         if($fg  <  0 ){

            $SafyRab7 =$EgmalyErydat + ($EgmalyTaklfaat  +   $EgmalyMasrofaat)  ;
        }else{

              $SafyRab7 = $EgmalyErydat -  ($EgmalyTaklfaat  +   $EgmalyMasrofaat)  ;

        }
        }elseif($EgmalyErydat <  $fg){

        if($fg  <  0 ){

            $SafyRab7 =$EgmalyErydat + ($EgmalyTaklfaat  +   $EgmalyMasrofaat)  ;
        }else{

              $SafyRab7 = $EgmalyErydat -  ($EgmalyTaklfaat  +   $EgmalyMasrofaat)  ;

        }


        }elseif($EgmalyErydat ==  $fg){

           $SafyRab7 = $EgmalyErydat -  ($EgmalyTaklfaat  +   $EgmalyMasrofaat)  ;

        }
   $TotalDebHkook = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {

                  $to = date('Y-m-d');
            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->where('general_dailies.Date', '<=', $to);
        })

          ->where('acccounting_manuals.Code', 'like', $wordsss.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Debitor_Coin');



        $TotalCredHkook = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {

                 $to = date('Y-m-d');
            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->where('general_dailies.Date', '<=', $to);
        })

          ->where('acccounting_manuals.Code', 'like', $wordsss.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Creditor_Coin');




      $EgmalyHkook=($TotalCredHkook - $TotalDebHkook) + $SafyRab7 ;


            //Salries
              $EmpSalaries=Employess::where('EmpSort',1)
                  ->where('Active',1)
                  ->where('id','!=',38)
                  ->where('id','!=',39)
                  ->where('id','!=',40)
                  ->where('id','!=',41)
                  ->where('id','!=',42)
                  ->get()->Sum('Salary');

            //Emp Count
                      $EmpCount=Employess::where('EmpSort',1)
                          ->where('Active',1)
                         ->where('id','!=',38)
                  ->where('id','!=',39)
                  ->where('id','!=',40)
                  ->where('id','!=',41)
                  ->where('id','!=',42)
                  ->count();


            //Safe Balnces

  $TotalDebSafes = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {

                  $to = date('Y-m-d');
            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->where('general_dailies.Date', '<=', $to);
        })

          ->whereIn('acccounting_manuals.Parent', [28,29])

                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Debitor_Coin');



        $TotalCredSafes = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {

                 $to = date('Y-m-d');
            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->where('general_dailies.Date', '<=', $to);
        })

             ->whereIn('acccounting_manuals.Parent', [28,29])

                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Creditor_Coin');




      $EgmalySafes=$TotalDebSafes  - $TotalCredSafes;



             //Clients Balnces
            $TotalDebClient = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {

                  $to = date('Y-m-d');
            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->where('general_dailies.Date', '<=', $to);
        })

          ->whereIn('acccounting_manuals.Parent', [24])

                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Debitor_Coin');



        $TotalCredClient = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {

                 $to = date('Y-m-d');
            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->where('general_dailies.Date', '<=', $to);
        })

             ->whereIn('acccounting_manuals.Parent', [24])

                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Creditor_Coin');




      $EgmalyClient=$TotalDebClient  - $TotalCredClient;


               //Vendors Balnces
            $TotalDebVendors = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {

                  $to = date('Y-m-d');
            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->where('general_dailies.Date', '<=', $to);
        })

          ->whereIn('acccounting_manuals.Parent', [37])

                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Debitor_Coin');



        $TotalCredVendors = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {

                 $to = date('Y-m-d');
            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->where('general_dailies.Date', '<=', $to);
        })

             ->whereIn('acccounting_manuals.Parent', [37])

                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Creditor_Coin');




      $EgmalyVendors=$TotalDebVendors  - $TotalCredVendors;



         return view('admin.StatisticsTotal',[

             'TotalSalesToday'=>$TotalSalesToday,
             'TotalSalesThisMonth'=>$TotalSalesThisMonth,
             'TotalSalesThisYear'=>$TotalSalesThisYear,
             'TotalSalesLast3Months'=>$TotalSalesLast3Months,
             'TotalSalesFirstQuarter'=>$TotalSalesFirstQuarter,
             'TotalSalesSecondQuarter'=>$TotalSalesSecondQuarter,
             'TotalSalesThirdQuarter'=>$TotalSalesThirdQuarter,
             'TotalSalesFourthQuarter'=>$TotalSalesFourthQuarter,
             'TotalSalesFirstHalf'=>$TotalSalesFirstHalf,
             'TotalSalesSecondHalf'=>$TotalSalesSecondHalf,
             'TotalSalesTodayCash'=>$TotalSalesTodayCash,
             'TotalSalesTodayLater'=>$TotalSalesTodayLater,

             'TotalPurchasesToday'=>$TotalPurchasesToday,
             'TotalPurchasesThisMonth'=>$TotalPurchasesThisMonth,
             'TotalPurchasesThisYear'=>$TotalPurchasesThisYear,
             'TotalPurchasesLast3Months'=>$TotalPurchasesLast3Months,
             'TotalPurchasesFirstQuarter'=>$TotalPurchasesFirstQuarter,
             'TotalPurchasesSecondQuarter'=>$TotalPurchasesSecondQuarter,
             'TotalPurchasesThirdQuarter'=>$TotalPurchasesThirdQuarter,
             'TotalPurchasesFourthQuarter'=>$TotalPurchasesFourthQuarter,
             'TotalPurchasesFirstHalf'=>$TotalPurchasesFirstHalf,
             'TotalPurchasesSecondHalf'=>$TotalPurchasesSecondHalf,
             'TotalPurchasesTodayCash'=>$TotalPurchasesTodayCash,
             'TotalPurchasesTodayLater'=>$TotalPurchasesTodayLater,

             'TotalReturnSalesToday'=>$TotalReturnSalesToday,
             'TotalReturnSalesThisMonth'=>$TotalReturnSalesThisMonth,
             'TotalReturnSalesThisYear'=>$TotalReturnSalesThisYear,
             'TotalReturnSalesLast3Months'=>$TotalReturnSalesLast3Months,
             'TotalReturnSalesFirstQuarter'=>$TotalReturnSalesFirstQuarter,
             'TotalReturnSalesSecondQuarter'=>$TotalReturnSalesSecondQuarter,
             'TotalReturnSalesThirdQuarter'=>$TotalReturnSalesThirdQuarter,
             'TotalReturnSalesFourthQuarter'=>$TotalReturnSalesFourthQuarter,
             'TotalReturnSalesFirstHalf'=>$TotalReturnSalesFirstHalf,
             'TotalReturnSalesSecondHalf'=>$TotalReturnSalesSecondHalf,
             'TotalReturnSalesTodayCash'=>$TotalReturnSalesTodayCash,
             'TotalReturnSalesTodayLater'=>$TotalReturnSalesTodayLater,

             'TotalReturnPurchToday'=>$TotalReturnPurchToday,
             'TotalReturnPurchThisMonth'=>$TotalReturnPurchThisMonth,
             'TotalReturnPurchThisYear'=>$TotalReturnPurchThisYear,
             'TotalReturnPurchLast3Months'=>$TotalReturnPurchLast3Months,
             'TotalReturnPurchFirstQuarter'=>$TotalReturnPurchFirstQuarter,
             'TotalReturnPurchSecondQuarter'=>$TotalReturnPurchSecondQuarter,
             'TotalReturnPurchThirdQuarter'=>$TotalReturnPurchThirdQuarter,
             'TotalReturnPurchFourthQuarter'=>$TotalReturnPurchFourthQuarter,
             'TotalReturnPurchFirstHalf'=>$TotalReturnPurchFirstHalf,
             'TotalReturnPurchSecondHalf'=>$TotalReturnPurchSecondHalf,
             'TotalReturnPurchTodayCash'=>$TotalReturnPurchTodayCash,
             'TotalReturnPurchTodayLater'=>$TotalReturnPurchTodayLater,

             'PaymentVoucherToday'=>$PaymentVoucherToday,
             'ReciptVoucherToday'=>$ReciptVoucherToday,
             'PaymentVoucherMonthly'=>$PaymentVoucherMonthly,
             'ReciptVoucherMonthly'=>$ReciptVoucherMonthly,
             'PaymentVoucherYearly'=>$PaymentVoucherYearly,
             'ReciptVoucherYearly'=>$ReciptVoucherYearly,
             'PaymentVoucherLast3Month'=>$PaymentVoucherLast3Month,
             'ReciptVoucherLast3Month'=>$ReciptVoucherLast3Month,
             'PaymentVoucherQ1'=>$PaymentVoucherQ1,
             'ReciptVoucherQ1'=>$ReciptVoucherQ1,
             'PaymentVoucherQ2'=>$PaymentVoucherQ2,
             'ReciptVoucherQ2'=>$ReciptVoucherQ2,
             'PaymentVoucherQ3'=>$PaymentVoucherQ3,
             'ReciptVoucherQ3'=>$ReciptVoucherQ3,
             'PaymentVoucherQ4'=>$PaymentVoucherQ4,
             'ReciptVoucherQ4'=>$ReciptVoucherQ4,
             'PaymentVoucherH1'=>$PaymentVoucherH1,
             'ReciptVoucherH1'=>$ReciptVoucherH1,
             'PaymentVoucherH2'=>$PaymentVoucherH2,
             'ReciptVoucherH2'=>$ReciptVoucherH2,

             'TotalInsurancePaperToday'=>$TotalInsurancePaperToday,
             'TotalInsurancePaperThisMonth'=>$TotalInsurancePaperThisMonth,
             'TotalInsurancePaperThisYear'=>$TotalInsurancePaperThisYear,
             'TotalInsurancePaperLast3Months'=>$TotalInsurancePaperLast3Months,
             'TotalInsurancePaperFirstQuarter'=>$TotalInsurancePaperFirstQuarter,
             'TotalInsurancePaperSecondQuarter'=>$TotalInsurancePaperSecondQuarter,
             'TotalInsurancePaperThirdQuarter'=>$TotalInsurancePaperThirdQuarter,
             'TotalInsurancePaperFourthQuarter'=>$TotalInsurancePaperFourthQuarter,
             'TotalInsurancePaperFirstHalf'=>$TotalInsurancePaperFirstHalf,
             'TotalInsurancePaperSecondHalf'=>$TotalInsurancePaperSecondHalf,
             'TotalInsurancePaperCollected'=>$TotalInsurancePaperCollected,

             'TotalIncomChecksToday'=>$TotalIncomChecksToday,
             'TotalIncomChecksThisMonth'=>$TotalIncomChecksThisMonth,
             'TotalIncomChecksThisYear'=>$TotalIncomChecksThisYear,
             'TotalIncomChecksLast3Months'=>$TotalIncomChecksLast3Months,
             'TotalIncomChecksFirstQuarter'=>$TotalIncomChecksFirstQuarter,
             'TotalIncomChecksSecondQuarter'=>$TotalIncomChecksSecondQuarter,
             'TotalIncomChecksThirdQuarter'=>$TotalIncomChecksThirdQuarter,
             'TotalIncomChecksFourthQuarter'=>$TotalIncomChecksFourthQuarter,
             'TotalIncomChecksFirstHalf'=>$TotalIncomChecksFirstHalf,
             'TotalIncomChecksSecondHalf'=>$TotalIncomChecksSecondHalf,
             'TotalIncomChecksCollected'=>$TotalIncomChecksCollected,

             'TotalExportChecksToday'=>$TotalExportChecksToday,
             'TotalExportChecksThisMonth'=>$TotalExportChecksThisMonth,
             'TotalExportChecksThisYear'=>$TotalExportChecksThisYear,
             'TotalExportChecksLast3Months'=>$TotalExportChecksLast3Months,
             'TotalExportChecksFirstQuarter'=>$TotalExportChecksFirstQuarter,
             'TotalExportChecksSecondQuarter'=>$TotalExportChecksSecondQuarter,
             'TotalExportChecksThirdQuarter'=>$TotalExportChecksThirdQuarter,
             'TotalExportChecksFourthQuarter'=>$TotalExportChecksFourthQuarter,
             'TotalExportChecksFirstHalf'=>$TotalExportChecksFirstHalf,
             'TotalExportChecksSecondHalf'=>$TotalExportChecksSecondHalf,
             'TotalExportChecksCollected'=>$TotalExportChecksCollected,

             'TotalStoreCost'=>$TotalStoreCost,
             'TotalStoreQty'=>$TotalStoreQty,


             'EgmalyMasrofatToday'=>$EgmalyMasrofatToday,
             'EgmalyMasrofatMonthly'=>$EgmalyMasrofatMonthly,
             'EgmalyMasrofatYearly'=>$EgmalyMasrofatYearly,
             'EgmalyMasrofatLast3Month'=>$EgmalyMasrofatLast3Month,
             'EgmalyMasrofatQ1'=>$EgmalyMasrofatQ1,
             'EgmalyMasrofatQ2'=>$EgmalyMasrofatQ2,
             'EgmalyMasrofatQ3'=>$EgmalyMasrofatQ3,
             'EgmalyMasrofatQ4'=>$EgmalyMasrofatQ4,
             'EgmalyMasrofatH1'=>$EgmalyMasrofatH1,
             'EgmalyMasrofatH2'=>$EgmalyMasrofatH2,

             'EgmalyErydaat'=>$EgmalyErydaat,
             'EgmalyTaklfa'=>$EgmalyTaklfa,
             'Mogml'=>$Mogml,
             'EgmalyAsoulSabta'=>$EgmalyAsoulSabta,
             'EgmalyAsoulMtadawla'=>$EgmalyAsoulMtadawla,
             'EgmalyAsoulOkhraa'=>$EgmalyAsoulOkhraa,
             'EgmalyAsoul'=>$EgmalyAsoul,
             'EgmalyKhsoum'=>$EgmalyKhsoum,
             'EgmalyKhsoumTweel'=>$EgmalyKhsoumTweel,
             'EgmalyKhsoumQaserr'=>$EgmalyKhsoumQaserr,
             'RasMal3aml'=>$RasMal3aml,
             'Estsmar'=>$Estsmar,
             'SafyRab7'=>$SafyRab7,
             'EgmalyHkook'=>$EgmalyHkook,

             'EmpSalaries'=>$EmpSalaries,
             'EgmalySafes'=>$EgmalySafes,
             'EgmalyClient'=>$EgmalyClient,
             'EgmalyVendors'=>$EgmalyVendors,
             'EmpCount'=>$EmpCount,

         ]);
    }


        public function StatisticsGraph(){


            //Sales
              $SalesJan=Sales::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-01-01'),date('Y-02-01')])
            ->where('Status',1)
          ->where('KitchenEnd',1)->where('RecivedOrder',1)
            ->get()->sum('Total_Price');


                      $SalesFeb=Sales::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-02-01'),date('Y-03-01')])
            ->where('Status',1)
          ->where('KitchenEnd',1)->where('RecivedOrder',1)
            ->get()->sum('Total_Price');

                      $SalesMar=Sales::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-03-01'),date('Y-04-01')])
            ->where('Status',1)
          ->where('KitchenEnd',1)->where('RecivedOrder',1)
            ->get()->sum('Total_Price');

                      $SalesApr=Sales::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-04-01'),date('Y-05-01')])
            ->where('Status',1)
          ->where('KitchenEnd',1)->where('RecivedOrder',1)
            ->get()->sum('Total_Price');

                      $SalesMay=Sales::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-05-01'),date('Y-06-01')])
            ->where('Status',1)
          ->where('KitchenEnd',1)->where('RecivedOrder',1)
            ->get()->sum('Total_Price');

                      $SalesJun=Sales::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-06-01'),date('Y-07-01')])
            ->where('Status',1)
          ->where('KitchenEnd',1)->where('RecivedOrder',1)
            ->get()->sum('Total_Price');

                      $SalesJul=Sales::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-07-01'),date('Y-08-01')])
            ->where('Status',1)
          ->where('KitchenEnd',1)->where('RecivedOrder',1)
            ->get()->sum('Total_Price');

                      $SalesAug=Sales::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-08-01'),date('Y-09-01')])
            ->where('Status',1)
          ->where('KitchenEnd',1)->where('RecivedOrder',1)
            ->get()->sum('Total_Price');

                      $SalesSep=Sales::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-09-01'),date('Y-10-01')])
            ->where('Status',1)
          ->where('KitchenEnd',1)->where('RecivedOrder',1)
            ->get()->sum('Total_Price');


                      $SalesOct=Sales::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-10-01'),date('Y-11-01')])
            ->where('Status',1)
          ->where('KitchenEnd',1)->where('RecivedOrder',1)
            ->get()->sum('Total_Price');

                      $SalesNov=Sales::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-11-01'),date('Y-12-01')])
            ->where('Status',1)
          ->where('KitchenEnd',1)->where('RecivedOrder',1)
            ->get()->sum('Total_Price');

                      $SalesDec=Sales::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-12-01'),date('Y-12-31')])
            ->where('Status',1)
          ->where('KitchenEnd',1)->where('RecivedOrder',1)
            ->get()->sum('Total_Price');




              //Purchases
              $PurchJan=Purchases::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-01-01'),date('Y-02-01')])
            ->where('Status',1)
            ->get()->sum('Total_Price');


                      $PurchFeb=Purchases::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-02-01'),date('Y-03-01')])
            ->where('Status',1)
            ->get()->sum('Total_Price');

                      $PurchMar=Purchases::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-03-01'),date('Y-04-01')])
            ->where('Status',1)

            ->get()->sum('Total_Price');

                      $PurchApr=Purchases::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-04-01'),date('Y-05-01')])
            ->where('Status',1)

            ->get()->sum('Total_Price');

                      $PurchMay=Purchases::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-05-01'),date('Y-06-01')])
            ->where('Status',1)

            ->get()->sum('Total_Price');

                      $PurchJun=Purchases::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-06-01'),date('Y-07-01')])
            ->where('Status',1)

            ->get()->sum('Total_Price');

                      $PurchJul=Purchases::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-07-01'),date('Y-08-01')])
            ->where('Status',1)
            ->get()->sum('Total_Price');

                      $PurchAug=Purchases::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-08-01'),date('Y-09-01')])
            ->where('Status',1)

            ->get()->sum('Total_Price');

                      $PurchSep=Purchases::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-09-01'),date('Y-10-01')])
            ->where('Status',1)
            ->get()->sum('Total_Price');


                      $PurchOct=Purchases::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-10-01'),date('Y-11-01')])
            ->where('Status',1)
            ->get()->sum('Total_Price');

                      $PurchNov=Purchases::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-11-01'),date('Y-12-01')])
            ->where('Status',1)
            ->get()->sum('Total_Price');

                      $PurchDec=Purchases::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-12-01'),date('Y-12-31')])
            ->where('Status',1)
            ->get()->sum('Total_Price');



            //ReturnSales
                   $ReturnSalesJan=ReturnSales::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-01-01'),date('Y-02-01')])

            ->get()->sum('Total_Return_Value');


                      $ReturnSalesFeb=ReturnSales::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-02-01'),date('Y-03-01')])

            ->get()->sum('Total_Return_Value');

                      $ReturnSalesMar=ReturnSales::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-03-01'),date('Y-04-01')])

            ->get()->sum('Total_Return_Value');

                      $ReturnSalesApr=ReturnSales::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-04-01'),date('Y-05-01')])


            ->get()->sum('Total_Return_Value');

                      $ReturnSalesMay=ReturnSales::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-05-01'),date('Y-06-01')])


            ->get()->sum('Total_Return_Value');

                      $ReturnSalesJun=ReturnSales::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-06-01'),date('Y-07-01')])


            ->get()->sum('Total_Return_Value');

                      $ReturnSalesJul=ReturnSales::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-07-01'),date('Y-08-01')])

            ->get()->sum('Total_Return_Value');

                      $ReturnSalesAug=ReturnSales::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-08-01'),date('Y-09-01')])


            ->get()->sum('Total_Return_Value');

                      $ReturnSalesSep=ReturnSales::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-09-01'),date('Y-10-01')])

            ->get()->sum('Total_Return_Value');


                      $ReturnSalesOct=ReturnSales::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-10-01'),date('Y-11-01')])

            ->get()->sum('Total_Return_Value');

                      $ReturnSalesNov=ReturnSales::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-11-01'),date('Y-12-01')])

            ->get()->sum('Total_Return_Value');

                      $ReturnSalesDec=ReturnSales::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-12-01'),date('Y-12-31')])

            ->get()->sum('Total_Return_Value');


//ReturnPurch
                 $ReturnPurchJan=ReturnPurch::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-01-01'),date('Y-02-01')])

            ->get()->sum('Total_Return_Value');


                      $ReturnPurchFeb=ReturnPurch::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-02-01'),date('Y-03-01')])

            ->get()->sum('Total_Return_Value');

                      $ReturnPurchMar=ReturnPurch::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-03-01'),date('Y-04-01')])

            ->get()->sum('Total_Return_Value');

                      $ReturnPurchApr=ReturnPurch::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-04-01'),date('Y-05-01')])


            ->get()->sum('Total_Return_Value');

                      $ReturnPurchMay=ReturnPurch::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-05-01'),date('Y-06-01')])


            ->get()->sum('Total_Return_Value');

                      $ReturnPurchJun=ReturnPurch::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-06-01'),date('Y-07-01')])


            ->get()->sum('Total_Return_Value');

                      $ReturnPurchJul=ReturnPurch::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-07-01'),date('Y-08-01')])

            ->get()->sum('Total_Return_Value');

                      $ReturnPurchAug=ReturnPurch::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-08-01'),date('Y-09-01')])


            ->get()->sum('Total_Return_Value');

                      $ReturnPurchSep=ReturnPurch::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-09-01'),date('Y-10-01')])

            ->get()->sum('Total_Return_Value');


                      $ReturnPurchOct=ReturnPurch::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-10-01'),date('Y-11-01')])

            ->get()->sum('Total_Return_Value');

                      $ReturnPurchNov=ReturnPurch::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-11-01'),date('Y-12-01')])

            ->get()->sum('Total_Return_Value');

                      $ReturnPurchDec=ReturnPurch::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-12-01'),date('Y-12-31')])

            ->get()->sum('Total_Return_Value');


            //Expenses
                             $AMsrfoat=AcccountingManual::where('id',20)->first();

        if($AMsrfoat->Parent == 0){

          $wordsss=$AMsrfoat->Code.'0';

        }else{

           $wordsss=$AMsrfoat->Code;

        }





                    $TotalDebMasrofatJan = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {

            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->whereBetween('general_dailies.Date',[date('Y-01-01'),date('Y-02-01')]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordsss.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Debitor_Coin');



        $TotalCredMasrofatJan = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {

            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->whereBetween('general_dailies.Date',[date('Y-01-01'),date('Y-02-01')]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordsss.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Creditor_Coin');



            $EgmalyMasrofatJan=  $TotalDebMasrofatJan -  $TotalCredMasrofatJan ;



                $TotalDebMasrofatFeb = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {

            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->whereBetween('general_dailies.Date',[date('Y-02-01'),date('Y-03-01')]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordsss.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Debitor_Coin');



        $TotalCredMasrofatFeb = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {

            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->whereBetween('general_dailies.Date',[date('Y-02-01'),date('Y-03-01')]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordsss.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Creditor_Coin');



            $EgmalyMasrofatFeb=  $TotalDebMasrofatFeb -  $TotalCredMasrofatFeb ;



                   $TotalDebMasrofatMar = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {

            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->whereBetween('general_dailies.Date',[date('Y-03-01'),date('Y-04-01')]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordsss.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Debitor_Coin');



        $TotalCredMasrofatMar = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {

            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->whereBetween('general_dailies.Date',[date('Y-03-01'),date('Y-04-01')]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordsss.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Creditor_Coin');



            $EgmalyMasrofatMar=  $TotalDebMasrofatMar -  $TotalCredMasrofatMar ;



                             $TotalDebMasrofatApr = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {

            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->whereBetween('general_dailies.Date',[date('Y-04-01'),date('Y-05-01')]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordsss.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Debitor_Coin');



        $TotalCredMasrofatApr = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {

            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->whereBetween('general_dailies.Date',[date('Y-04-01'),date('Y-05-01')]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordsss.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Creditor_Coin');



            $EgmalyMasrofatApr=  $TotalDebMasrofatApr -  $TotalCredMasrofatApr ;



                                        $TotalDebMasrofatMay = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {

            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->whereBetween('general_dailies.Date',[date('Y-05-01'),date('Y-06-01')]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordsss.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Debitor_Coin');



        $TotalCredMasrofatMay = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {

            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->whereBetween('general_dailies.Date',[date('Y-05-01'),date('Y-06-01')]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordsss.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Creditor_Coin');



            $EgmalyMasrofatMay=  $TotalDebMasrofatMay -  $TotalCredMasrofatMay ;



                       $TotalDebMasrofatJun = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {

            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->whereBetween('general_dailies.Date',[date('Y-06-01'),date('Y-07-01')]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordsss.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Debitor_Coin');



        $TotalCredMasrofatJun = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {

            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->whereBetween('general_dailies.Date',[date('Y-06-01'),date('Y-07-01')]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordsss.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Creditor_Coin');



            $EgmalyMasrofatJun=  $TotalDebMasrofatJun -  $TotalCredMasrofatJun ;


                    $TotalDebMasrofatJul = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {

            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->whereBetween('general_dailies.Date',[date('Y-07-01'),date('Y-08-01')]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordsss.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Debitor_Coin');



        $TotalCredMasrofatJul = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {

            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->whereBetween('general_dailies.Date',[date('Y-07-01'),date('Y-08-01')]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordsss.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Creditor_Coin');



            $EgmalyMasrofatJul=  $TotalDebMasrofatJul -  $TotalCredMasrofatJul ;




                    $TotalDebMasrofatAug = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {

            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->whereBetween('general_dailies.Date',[date('Y-08-01'),date('Y-09-01')]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordsss.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Debitor_Coin');



        $TotalCredMasrofatAug = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {

            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->whereBetween('general_dailies.Date',[date('Y-08-01'),date('Y-09-01')]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordsss.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Creditor_Coin');



            $EgmalyMasrofatAug=  $TotalDebMasrofatAug -  $TotalCredMasrofatAug ;


                $TotalDebMasrofatSep = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {

            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->whereBetween('general_dailies.Date',[date('Y-09-01'),date('Y-10-01')]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordsss.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Debitor_Coin');



        $TotalCredMasrofatSep = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {

            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->whereBetween('general_dailies.Date',[date('Y-09-01'),date('Y-10-01')]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordsss.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Creditor_Coin');



            $EgmalyMasrofatSep=  $TotalDebMasrofatSep -  $TotalCredMasrofatSep ;




                $TotalDebMasrofatOct = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {

            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->whereBetween('general_dailies.Date',[date('Y-10-01'),date('Y-11-01')]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordsss.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Debitor_Coin');



        $TotalCredMasrofatOct = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {

            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->whereBetween('general_dailies.Date',[date('Y-10-01'),date('Y-11-01')]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordsss.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Creditor_Coin');



            $EgmalyMasrofatOct=  $TotalDebMasrofatOct -  $TotalCredMasrofatOct ;




                $TotalDebMasrofatNov = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {

            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->whereBetween('general_dailies.Date',[date('Y-11-01'),date('Y-12-01')]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordsss.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Debitor_Coin');



        $TotalCredMasrofatNov = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {

            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->whereBetween('general_dailies.Date',[date('Y-11-01'),date('Y-12-01')]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordsss.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Creditor_Coin');



            $EgmalyMasrofatNov=  $TotalDebMasrofatNov -  $TotalCredMasrofatNov ;



              $TotalDebMasrofatDec = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {

            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->whereBetween('general_dailies.Date',[date('Y-12-01'),date('Y-12-31')]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordsss.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Debitor_Coin');



        $TotalCredMasrofatDec = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {

            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->whereBetween('general_dailies.Date',[date('Y-12-01'),date('Y-12-31')]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordsss.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Creditor_Coin');



            $EgmalyMasrofatDec=  $TotalDebMasrofatDec -  $TotalCredMasrofatDec ;



            //Vouchers
        $PaymentVoucherJan=GeneralDaily::whereBetween('Date',[date('Y-01-01'),date('Y-02-01')])
          ->where('Type','سند صرف')
          ->get()->sum('Debitor_Coin');


    $ReciptVoucherJan=GeneralDaily::whereBetween('Date',[date('Y-01-01'),date('Y-02-01')])
        ->where('Type','سند قبض')
          ->get()->sum('Creditor_Coin');


                $PaymentVoucherFeb=GeneralDaily::whereBetween('Date',[date('Y-02-01'),date('Y-03-01')])
          ->where('Type','سند صرف')
          ->get()->sum('Debitor_Coin');


    $ReciptVoucherFeb=GeneralDaily::whereBetween('Date',[date('Y-02-01'),date('Y-03-01')])
        ->where('Type','سند قبض')
          ->get()->sum('Creditor_Coin');



                    $PaymentVoucherMar=GeneralDaily::whereBetween('Date',[date('Y-03-01'),date('Y-04-01')])
          ->where('Type','سند صرف')
          ->get()->sum('Debitor_Coin');


    $ReciptVoucherMar=GeneralDaily::whereBetween('Date',[date('Y-03-01'),date('Y-04-01')])
        ->where('Type','سند قبض')
          ->get()->sum('Creditor_Coin');




                    $PaymentVoucherApr=GeneralDaily::whereBetween('Date',[date('Y-04-01'),date('Y-05-01')])
          ->where('Type','سند صرف')
          ->get()->sum('Debitor_Coin');


    $ReciptVoucherApr=GeneralDaily::whereBetween('Date',[date('Y-04-01'),date('Y-05-01')])
        ->where('Type','سند قبض')
          ->get()->sum('Creditor_Coin');



                    $PaymentVoucherMay=GeneralDaily::whereBetween('Date',[date('Y-05-01'),date('Y-06-01')])
          ->where('Type','سند صرف')
          ->get()->sum('Debitor_Coin');


    $ReciptVoucherMay=GeneralDaily::whereBetween('Date',[date('Y-05-01'),date('Y-06-01')])
        ->where('Type','سند قبض')
          ->get()->sum('Creditor_Coin');



                    $PaymentVoucherJun=GeneralDaily::whereBetween('Date',[date('Y-06-01'),date('Y-07-01')])
          ->where('Type','سند صرف')
          ->get()->sum('Debitor_Coin');


    $ReciptVoucherJun=GeneralDaily::whereBetween('Date',[date('Y-06-01'),date('Y-07-01')])
        ->where('Type','سند قبض')
          ->get()->sum('Creditor_Coin');



                    $PaymentVoucherJul=GeneralDaily::whereBetween('Date',[date('Y-07-01'),date('Y-08-01')])
          ->where('Type','سند صرف')
          ->get()->sum('Debitor_Coin');


    $ReciptVoucherJul=GeneralDaily::whereBetween('Date',[date('Y-07-01'),date('Y-08-01')])
        ->where('Type','سند قبض')
          ->get()->sum('Creditor_Coin');


                    $PaymentVoucherAug=GeneralDaily::whereBetween('Date',[date('Y-08-01'),date('Y-09-01')])
          ->where('Type','سند صرف')
          ->get()->sum('Debitor_Coin');


    $ReciptVoucherAug=GeneralDaily::whereBetween('Date',[date('Y-08-01'),date('Y-09-01')])
        ->where('Type','سند قبض')
          ->get()->sum('Creditor_Coin');



                        $PaymentVoucherSep=GeneralDaily::whereBetween('Date',[date('Y-09-01'),date('Y-10-01')])
          ->where('Type','سند صرف')
          ->get()->sum('Debitor_Coin');


    $ReciptVoucherSep=GeneralDaily::whereBetween('Date',[date('Y-09-01'),date('Y-10-01')])
        ->where('Type','سند قبض')
          ->get()->sum('Creditor_Coin');


    $PaymentVoucherOct=GeneralDaily::whereBetween('Date',[date('Y-10-01'),date('Y-11-01')])
          ->where('Type','سند صرف')
          ->get()->sum('Debitor_Coin');


    $ReciptVoucherOct=GeneralDaily::whereBetween('Date',[date('Y-10-01'),date('Y-11-01')])
        ->where('Type','سند قبض')
          ->get()->sum('Creditor_Coin');


        $PaymentVoucherNov=GeneralDaily::whereBetween('Date',[date('Y-11-01'),date('Y-12-01')])
          ->where('Type','سند صرف')
          ->get()->sum('Debitor_Coin');


    $ReciptVoucherNov=GeneralDaily::whereBetween('Date',[date('Y-11-01'),date('Y-12-01')])
        ->where('Type','سند قبض')
          ->get()->sum('Creditor_Coin');


                $PaymentVoucherDec=GeneralDaily::whereBetween('Date',[date('Y-12-01'),date('Y-12-31')])
          ->where('Type','سند صرف')
          ->get()->sum('Debitor_Coin');


    $ReciptVoucherDec=GeneralDaily::whereBetween('Date',[date('Y-12-01'),date('Y-12-31')])
        ->where('Type','سند قبض')
          ->get()->sum('Creditor_Coin');


            //Checks
            $IncomChecksJan=IncomChecks::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-01-01'),date('Y-02-01')])
            ->get()->sum('Amount');

   $ExportChecksJan=ExportChecks::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-01-01'),date('Y-02-01')])
            ->get()->sum('Amount');

                        $IncomChecksFeb=IncomChecks::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-02-01'),date('Y-03-01')])
            ->get()->sum('Amount');

   $ExportChecksFeb=ExportChecks::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-02-01'),date('Y-03-01')])
            ->get()->sum('Amount');


                $IncomChecksMar=IncomChecks::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-03-01'),date('Y-04-01')])
            ->get()->sum('Amount');

   $ExportChecksMar=ExportChecks::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-03-01'),date('Y-04-01')])
            ->get()->sum('Amount');


                $IncomChecksApr=IncomChecks::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-04-01'),date('Y-05-01')])
            ->get()->sum('Amount');

   $ExportChecksApr=ExportChecks::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-04-01'),date('Y-05-01')])
            ->get()->sum('Amount');


                $IncomChecksMay=IncomChecks::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-05-01'),date('Y-06-01')])
            ->get()->sum('Amount');

   $ExportChecksMay=ExportChecks::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-05-01'),date('Y-06-01')])
            ->get()->sum('Amount');


            $IncomChecksJun=IncomChecks::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-06-01'),date('Y-07-01')])
            ->get()->sum('Amount');

   $ExportChecksJun=ExportChecks::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-06-01'),date('Y-07-01')])
            ->get()->sum('Amount');


                  $IncomChecksJul=IncomChecks::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-07-01'),date('Y-08-01')])
            ->get()->sum('Amount');

   $ExportChecksJul=ExportChecks::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-07-01'),date('Y-08-01')])
            ->get()->sum('Amount');


                  $IncomChecksAug=IncomChecks::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-08-01'),date('Y-09-01')])
            ->get()->sum('Amount');

   $ExportChecksAug=ExportChecks::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-08-01'),date('Y-09-01')])
            ->get()->sum('Amount');


                          $IncomChecksSep=IncomChecks::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-09-01'),date('Y-10-01')])
            ->get()->sum('Amount');

   $ExportChecksSep=ExportChecks::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-09-01'),date('Y-10-01')])
            ->get()->sum('Amount');



                        $IncomChecksOct=IncomChecks::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-10-01'),date('Y-11-01')])
            ->get()->sum('Amount');

   $ExportChecksOct=ExportChecks::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-10-01'),date('Y-11-01')])
            ->get()->sum('Amount');


                              $IncomChecksNov=IncomChecks::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-11-01'),date('Y-12-01')])
            ->get()->sum('Amount');

   $ExportChecksNov=ExportChecks::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-11-01'),date('Y-12-01')])
            ->get()->sum('Amount');


                                  $IncomChecksDec=IncomChecks::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-12-01'),date('Y-12-31')])
            ->get()->sum('Amount');

   $ExportChecksDec=ExportChecks::orderBy('id','desc')
            ->whereBetween('Date',[date('Y-12-01'),date('Y-12-31')])
            ->get()->sum('Amount');



$datesat = date("Y-m-d",strtotime('-2 day monday this week'));
$datesun = date("Y-m-d",strtotime('-1 day monday this week'));
$datemon = date("Y-m-d",strtotime('monday this week'));
$datetue = date("Y-m-d",strtotime("tuesday this week"));
$datewed = date("Y-m-d",strtotime("wednesday this week"));
$datethu = date("Y-m-d",strtotime("thursday this week"));
$datefri = date("Y-m-d",strtotime("friday this week"));


            //Sales
             $SalesSat=Sales::orderBy('id','desc')
            ->where('Date',$datesat)
            ->where('Status',1)
          ->where('KitchenEnd',1)->where('RecivedOrder',1)
            ->get()->sum('Total_Price');


                   $SalesSun=Sales::orderBy('id','desc')
            ->where('Date',$datesun)
            ->where('Status',1)
          ->where('KitchenEnd',1)->where('RecivedOrder',1)
            ->get()->sum('Total_Price');

                   $SalesMon=Sales::orderBy('id','desc')
            ->where('Date',$datemon)
            ->where('Status',1)
          ->where('KitchenEnd',1)->where('RecivedOrder',1)
            ->get()->sum('Total_Price');

                   $SalesTue=Sales::orderBy('id','desc')
            ->where('Date',$datetue)
            ->where('Status',1)
          ->where('KitchenEnd',1)->where('RecivedOrder',1)
            ->get()->sum('Total_Price');

                   $SalesWed=Sales::orderBy('id','desc')
            ->where('Date',$datewed)
            ->where('Status',1)
          ->where('KitchenEnd',1)->where('RecivedOrder',1)
            ->get()->sum('Total_Price');

                   $SalesThr=Sales::orderBy('id','desc')
            ->where('Date',$datethu)
            ->where('Status',1)
          ->where('KitchenEnd',1)->where('RecivedOrder',1)
            ->get()->sum('Total_Price');

                   $SalesFri=Sales::orderBy('id','desc')
            ->where('Date',$datefri)
            ->where('Status',1)
          ->where('KitchenEnd',1)->where('RecivedOrder',1)
            ->get()->sum('Total_Price');



                //Purchases
             $PurchSat=Purchases::orderBy('id','desc')
            ->where('Date',$datesat)
            ->where('Status',1)

            ->get()->sum('Total_Price');


                   $PurchSun=Purchases::orderBy('id','desc')
            ->where('Date',$datesun)
            ->where('Status',1)

            ->get()->sum('Total_Price');

                   $PurchMon=Purchases::orderBy('id','desc')
            ->where('Date',$datemon)
            ->where('Status',1)

            ->get()->sum('Total_Price');

                   $PurchTue=Purchases::orderBy('id','desc')
            ->where('Date',$datetue)
            ->where('Status',1)

            ->get()->sum('Total_Price');

                   $PurchWed=Purchases::orderBy('id','desc')
            ->where('Date',$datewed)
            ->where('Status',1)

            ->get()->sum('Total_Price');

                   $PurchThr=Purchases::orderBy('id','desc')
            ->where('Date',$datethu)
            ->where('Status',1)

            ->get()->sum('Total_Price');

                   $PurchFri=Purchases::orderBy('id','desc')
            ->where('Date',$datefri)
            ->where('Status',1)

            ->get()->sum('Total_Price');


                //ReturnSales
               $ReturnSalesSat=ReturnSales::orderBy('id','desc')
            ->where('Date',$datesat)


            ->get()->sum('Total_Return_Value');


                   $ReturnSalesSun=ReturnSales::orderBy('id','desc')
            ->where('Date',$datesun)


            ->get()->sum('Total_Return_Value');

                   $ReturnSalesMon=ReturnSales::orderBy('id','desc')
            ->where('Date',$datemon)


            ->get()->sum('Total_Return_Value');

                   $ReturnSalesTue=ReturnSales::orderBy('id','desc')
            ->where('Date',$datetue)


            ->get()->sum('Total_Return_Value');

                   $ReturnSalesWed=ReturnSales::orderBy('id','desc')
            ->where('Date',$datewed)


            ->get()->sum('Total_Return_Value');

                   $ReturnSalesThr=ReturnSales::orderBy('id','desc')
            ->where('Date',$datethu)


            ->get()->sum('Total_Return_Value');

                   $ReturnSalesFri=ReturnSales::orderBy('id','desc')
            ->where('Date',$datefri)


            ->get()->sum('Total_Return_Value');

//ReturnPurch
              $ReturnPurchSat=ReturnPurch::orderBy('id','desc')
            ->where('Date',$datesat)


            ->get()->sum('Total_Return_Value');


                   $ReturnPurchSun=ReturnPurch::orderBy('id','desc')
            ->where('Date',$datesun)


            ->get()->sum('Total_Return_Value');

                   $ReturnPurchMon=ReturnPurch::orderBy('id','desc')
            ->where('Date',$datemon)


            ->get()->sum('Total_Return_Value');

                   $ReturnPurchTue=ReturnPurch::orderBy('id','desc')
            ->where('Date',$datetue)


            ->get()->sum('Total_Return_Value');

                   $ReturnPurchWed=ReturnPurch::orderBy('id','desc')
            ->where('Date',$datewed)


            ->get()->sum('Total_Return_Value');

                   $ReturnPurchThr=ReturnPurch::orderBy('id','desc')
            ->where('Date',$datethu)


            ->get()->sum('Total_Return_Value');

                   $ReturnPurchFri=ReturnPurch::orderBy('id','desc')
            ->where('Date',$datefri)


            ->get()->sum('Total_Return_Value');



            //Vouchers


        $PaymentVoucherSat=GeneralDaily::where('Date',$datesat)
          ->where('Type','سند صرف')
          ->get()->sum('Debitor_Coin');


    $ReciptVoucherSat=GeneralDaily::where('Date',$datesat)
        ->where('Type','سند قبض')
          ->get()->sum('Creditor_Coin');


            $PaymentVoucherSun=GeneralDaily::where('Date',$datesun)
          ->where('Type','سند صرف')
          ->get()->sum('Debitor_Coin');


    $ReciptVoucherSun=GeneralDaily::where('Date',$datesun)
        ->where('Type','سند قبض')
          ->get()->sum('Creditor_Coin');


                $PaymentVoucherMon=GeneralDaily::where('Date',$datemon)
          ->where('Type','سند صرف')
          ->get()->sum('Debitor_Coin');


    $ReciptVoucherMon=GeneralDaily::where('Date',$datemon)
        ->where('Type','سند قبض')
          ->get()->sum('Creditor_Coin');



                    $PaymentVoucherTue=GeneralDaily::where('Date',$datetue)
          ->where('Type','سند صرف')
          ->get()->sum('Debitor_Coin');


    $ReciptVoucherTue=GeneralDaily::where('Date',$datetue)
        ->where('Type','سند قبض')
          ->get()->sum('Creditor_Coin');


                 $PaymentVoucherWed=GeneralDaily::where('Date',$datewed)
          ->where('Type','سند صرف')
          ->get()->sum('Debitor_Coin');


    $ReciptVoucherWed=GeneralDaily::where('Date',$datewed)
        ->where('Type','سند قبض')
          ->get()->sum('Creditor_Coin');


    $PaymentVoucherThr=GeneralDaily::where('Date',$datethu)
          ->where('Type','سند صرف')
          ->get()->sum('Debitor_Coin');


    $ReciptVoucherThr=GeneralDaily::where('Date',$datethu)
        ->where('Type','سند قبض')
          ->get()->sum('Creditor_Coin');



    $PaymentVoucherFri=GeneralDaily::where('Date',$datefri)
          ->where('Type','سند صرف')
          ->get()->sum('Debitor_Coin');


    $ReciptVoucherFri=GeneralDaily::where('Date',$datefri)
        ->where('Type','سند قبض')
          ->get()->sum('Creditor_Coin');





            //Checks
        $IncomChecksSat=IncomChecks::orderBy('id','desc')
            ->where('Date',$datesat)
            ->get()->sum('Amount');

   $ExportChecksSat=ExportChecks::orderBy('id','desc')
            ->where('Date',$datesat)
            ->get()->sum('Amount');


            $IncomChecksSun=IncomChecks::orderBy('id','desc')
            ->where('Date',$datesun)
            ->get()->sum('Amount');

   $ExportChecksSun=ExportChecks::orderBy('id','desc')
            ->where('Date',$datesun)
            ->get()->sum('Amount');



           $IncomChecksMon=IncomChecks::orderBy('id','desc')
            ->where('Date',$datemon)
            ->get()->sum('Amount');

   $ExportChecksMon=ExportChecks::orderBy('id','desc')
            ->where('Date',$datemon)
            ->get()->sum('Amount');


       $IncomChecksTue=IncomChecks::orderBy('id','desc')
            ->where('Date',$datetue)
            ->get()->sum('Amount');

   $ExportChecksTue=ExportChecks::orderBy('id','desc')
            ->where('Date',$datetue)
            ->get()->sum('Amount');


           $IncomChecksWed=IncomChecks::orderBy('id','desc')
            ->where('Date',$datewed)
            ->get()->sum('Amount');

   $ExportChecksWed=ExportChecks::orderBy('id','desc')
            ->where('Date',$datewed)
            ->get()->sum('Amount');


         $IncomChecksThr=IncomChecks::orderBy('id','desc')
            ->where('Date',$datethu)
            ->get()->sum('Amount');

   $ExportChecksThr=ExportChecks::orderBy('id','desc')
            ->where('Date',$datethu)
            ->get()->sum('Amount');



                $IncomChecksFri=IncomChecks::orderBy('id','desc')
            ->where('Date',$datefri)
            ->get()->sum('Amount');

   $ExportChecksFri=ExportChecks::orderBy('id','desc')
            ->where('Date',$datefri)
            ->get()->sum('Amount');




            //Expenses

              $TotalDebMasrofatSat = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {
                 $datesat = date("Y-m-d",strtotime('-2 day monday this week'));
            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->where('general_dailies.Date',$datesat);
        })

          ->where('acccounting_manuals.Code', 'like', $wordsss.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Debitor_Coin');



        $TotalCredMasrofatSat = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {
                 $datesat = date("Y-m-d",strtotime('-2 day monday this week'));
            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->where('general_dailies.Date',$datesat);
        })

          ->where('acccounting_manuals.Code', 'like', $wordsss.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Creditor_Coin');



            $EgmalyMasrofatSat=  $TotalDebMasrofatSat -  $TotalCredMasrofatSat ;



               $TotalDebMasrofatSun = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {
           $datesun = date("Y-m-d",strtotime('-1 day monday this week'));
            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->where('general_dailies.Date',$datesun);
        })

          ->where('acccounting_manuals.Code', 'like', $wordsss.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Debitor_Coin');



        $TotalCredMasrofatSun = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {
           $datesun = date("Y-m-d",strtotime('-1 day monday this week'));
            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->where('general_dailies.Date',$datesun);
        })

          ->where('acccounting_manuals.Code', 'like', $wordsss.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Creditor_Coin');



            $EgmalyMasrofatSun=  $TotalDebMasrofatSun -  $TotalCredMasrofatSun ;



               $TotalDebMasrofatMon = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {
           $datemon = date("Y-m-d",strtotime('monday this week'));
            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->where('general_dailies.Date',$datemon);
        })

          ->where('acccounting_manuals.Code', 'like', $wordsss.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Debitor_Coin');



        $TotalCredMasrofatMon = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {
           $datemon = date("Y-m-d",strtotime('monday this week'));
            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->where('general_dailies.Date',$datemon);
        })

          ->where('acccounting_manuals.Code', 'like', $wordsss.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Creditor_Coin');



            $EgmalyMasrofatMon=  $TotalDebMasrofatMon -  $TotalCredMasrofatMon ;



                 $TotalDebMasrofatTue = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {
           $datetue = date("Y-m-d",strtotime("tuesday this week"));
            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->where('general_dailies.Date',$datetue);
        })

          ->where('acccounting_manuals.Code', 'like', $wordsss.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Debitor_Coin');



        $TotalCredMasrofatTue = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {
           $datetue = date("Y-m-d",strtotime("tuesday this week"));
            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->where('general_dailies.Date',$datetue);
        })

          ->where('acccounting_manuals.Code', 'like', $wordsss.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Creditor_Coin');



            $EgmalyMasrofatTue=  $TotalDebMasrofatTue -  $TotalCredMasrofatTue ;


                    $TotalDebMasrofatWed = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {
           $datewed = date("Y-m-d",strtotime("wednesday this week"));
            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->where('general_dailies.Date',$datewed);
        })

          ->where('acccounting_manuals.Code', 'like', $wordsss.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Debitor_Coin');



        $TotalCredMasrofatWed = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {
           $datewed = date("Y-m-d",strtotime("wednesday this week"));
            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->where('general_dailies.Date',$datewed);
        })

          ->where('acccounting_manuals.Code', 'like', $wordsss.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Creditor_Coin');



            $EgmalyMasrofatWed=  $TotalDebMasrofatWed -  $TotalCredMasrofatWed ;


                      $TotalDebMasrofatThr = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {
           $datethu = date("Y-m-d",strtotime("thursday this week"));
            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->where('general_dailies.Date',$datethu);
        })

          ->where('acccounting_manuals.Code', 'like', $wordsss.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Debitor_Coin');



        $TotalCredMasrofatThr = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {
           $datethu = date("Y-m-d",strtotime("thursday this week"));
            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->where('general_dailies.Date',$datethu);
        })

          ->where('acccounting_manuals.Code', 'like', $wordsss.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Creditor_Coin');



            $EgmalyMasrofatThr=  $TotalDebMasrofatThr -  $TotalCredMasrofatThr ;





             $TotalDebMasrofatFri = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {
           $datefri = date("Y-m-d",strtotime("friday this week"));
            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->where('general_dailies.Date',$datefri);
        })

          ->where('acccounting_manuals.Code', 'like', $wordsss.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Debitor_Coin');



        $TotalCredMasrofatFri = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {
           $datefri = date("Y-m-d",strtotime("friday this week"));
            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->where('general_dailies.Date',$datefri);
        })

          ->where('acccounting_manuals.Code', 'like', $wordsss.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Creditor_Coin');



            $EgmalyMasrofatFri=  $TotalDebMasrofatFri -  $TotalCredMasrofatFri ;




        $Y1F=date("Y-01-01",strtotime("-1 year"));
        $Y1T=date("Y-12-31",strtotime("-1 year"));


       $Y2F=date("Y-01-01",strtotime("-2 year"));
        $Y2T=date("Y-12-31",strtotime("-2 year"));


           $Y3F=date("Y-01-01",strtotime("-3 year"));
        $Y3T=date("Y-12-31",strtotime("-3 year"));


               $Y4F=date("Y-01-01",strtotime("-4 year"));
        $Y4T=date("Y-12-31",strtotime("-4 year"));



        $YCF=date("Y-01-01");
        $YCT=date("Y-12-31");



            //Sales
                      $SalesPrevY1=Sales::orderBy('id','desc')
            ->whereBetween('Date',[$Y1F,$Y1T])
            ->where('Status',1)
          ->where('KitchenEnd',1)->where('RecivedOrder',1)
            ->get()->sum('Total_Price');


                           $SalesPrevY2=Sales::orderBy('id','desc')
            ->whereBetween('Date',[$Y2F,$Y2T])
            ->where('Status',1)
          ->where('KitchenEnd',1)->where('RecivedOrder',1)
            ->get()->sum('Total_Price');


                           $SalesPrevY3=Sales::orderBy('id','desc')
            ->whereBetween('Date',[$Y3F,$Y3T])
            ->where('Status',1)
          ->where('KitchenEnd',1)->where('RecivedOrder',1)
            ->get()->sum('Total_Price');

                           $SalesPrevY4=Sales::orderBy('id','desc')
            ->whereBetween('Date',[$Y4F,$Y4T])
            ->where('Status',1)
          ->where('KitchenEnd',1)->where('RecivedOrder',1)
            ->get()->sum('Total_Price');

                          $SalesPrevYC=Sales::orderBy('id','desc')
            ->whereBetween('Date',[$YCF,$YCT])
            ->where('Status',1)
          ->where('KitchenEnd',1)->where('RecivedOrder',1)
            ->get()->sum('Total_Price');



             //Purchases
                      $PurchPrevY1=Purchases::orderBy('id','desc')
            ->whereBetween('Date',[$Y1F,$Y1T])
            ->where('Status',1)

            ->get()->sum('Total_Price');


                           $PurchPrevY2=Purchases::orderBy('id','desc')
            ->whereBetween('Date',[$Y2F,$Y2T])
            ->where('Status',1)

            ->get()->sum('Total_Price');


                           $PurchPrevY3=Purchases::orderBy('id','desc')
            ->whereBetween('Date',[$Y3F,$Y3T])
            ->where('Status',1)

            ->get()->sum('Total_Price');

                           $PurchPrevY4=Purchases::orderBy('id','desc')
            ->whereBetween('Date',[$Y4F,$Y4T])
            ->where('Status',1)

            ->get()->sum('Total_Price');

                          $PurchPrevYC=Purchases::orderBy('id','desc')
            ->whereBetween('Date',[$YCF,$YCT])
            ->where('Status',1)

            ->get()->sum('Total_Price');


                //ReturnSales
                  $ReturnSalesPrevY1=ReturnSales::orderBy('id','desc')
            ->whereBetween('Date',[$Y1F,$Y1T])


            ->get()->sum('Total_Return_Value');


                           $ReturnSalesPrevY2=ReturnSales::orderBy('id','desc')
            ->whereBetween('Date',[$Y2F,$Y2T])


            ->get()->sum('Total_Return_Value');


                           $ReturnSalesPrevY3=ReturnSales::orderBy('id','desc')
            ->whereBetween('Date',[$Y3F,$Y3T])


            ->get()->sum('Total_Return_Value');

                           $ReturnSalesPrevY4=ReturnSales::orderBy('id','desc')
            ->whereBetween('Date',[$Y4F,$Y4T])


            ->get()->sum('Total_Return_Value');

                          $ReturnSalesPrevYC=ReturnSales::orderBy('id','desc')
            ->whereBetween('Date',[$YCF,$YCT])


            ->get()->sum('Total_Return_Value');
//ReturnPurch
                 $ReturnPurchPrevY1=ReturnPurch::orderBy('id','desc')
            ->whereBetween('Date',[$Y1F,$Y1T])


            ->get()->sum('Total_Return_Value');


                           $ReturnPurchPrevY2=ReturnPurch::orderBy('id','desc')
            ->whereBetween('Date',[$Y2F,$Y2T])


            ->get()->sum('Total_Return_Value');


                           $ReturnPurchPrevY3=ReturnPurch::orderBy('id','desc')
            ->whereBetween('Date',[$Y3F,$Y3T])


            ->get()->sum('Total_Return_Value');

                           $ReturnPurchPrevY4=ReturnPurch::orderBy('id','desc')
            ->whereBetween('Date',[$Y4F,$Y4T])


            ->get()->sum('Total_Return_Value');

                          $ReturnPurchPrevYC=ReturnPurch::orderBy('id','desc')
            ->whereBetween('Date',[$YCF,$YCT])


            ->get()->sum('Total_Return_Value');


            //Expenses

            $TotalDebMasrofatY1 = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {
                  $Y1F=date("Y-01-01",strtotime("-1 year"));
        $Y1T=date("Y-12-31",strtotime("-1 year"));
            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->whereBetween('general_dailies.Date',[$Y1F,$Y1T]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordsss.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Debitor_Coin');



        $TotalCredMasrofatY1 = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {
                  $Y1F=date("Y-01-01",strtotime("-1 year"));
        $Y1T=date("Y-12-31",strtotime("-1 year"));
            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->whereBetween('general_dailies.Date',[$Y1F,$Y1T]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordsss.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Creditor_Coin');



            $EgmalyMasrofatY1=  $TotalDebMasrofatY1 -  $TotalCredMasrofatY1 ;



               $TotalDebMasrofatY2 = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {
                  $Y2F=date("Y-01-01",strtotime("-2 year"));
        $Y2T=date("Y-12-31",strtotime("-2 year"));
            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->whereBetween('general_dailies.Date',[$Y2F,$Y2T]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordsss.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Debitor_Coin');



        $TotalCredMasrofatY2 = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {
                  $Y2F=date("Y-01-01",strtotime("-2 year"));
        $Y2T=date("Y-12-31",strtotime("-2 year"));
            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->whereBetween('general_dailies.Date',[$Y2F,$Y2T]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordsss.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Creditor_Coin');



            $EgmalyMasrofatY2=  $TotalDebMasrofatY2 -  $TotalCredMasrofatY2 ;




                   $TotalDebMasrofatY3 = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {
                $Y3F=date("Y-01-01",strtotime("-3 year"));
        $Y3T=date("Y-12-31",strtotime("-3 year"));
            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->whereBetween('general_dailies.Date',[$Y3F,$Y3T]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordsss.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Debitor_Coin');



        $TotalCredMasrofatY3 = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {
                $Y3F=date("Y-01-01",strtotime("-3 year"));
        $Y3T=date("Y-12-31",strtotime("-3 year"));
            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->whereBetween('general_dailies.Date',[$Y3F,$Y3T]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordsss.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Creditor_Coin');



            $EgmalyMasrofatY3=  $TotalDebMasrofatY3 -  $TotalCredMasrofatY3 ;



                 $TotalDebMasrofatY4 = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {
                          $Y4F=date("Y-01-01",strtotime("-4 year"));
        $Y4T=date("Y-12-31",strtotime("-4 year"));


            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->whereBetween('general_dailies.Date',[$Y4F,$Y4T]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordsss.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Debitor_Coin');



        $TotalCredMasrofatY4 = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {
                          $Y4F=date("Y-01-01",strtotime("-4 year"));
        $Y4T=date("Y-12-31",strtotime("-4 year"));


            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->whereBetween('general_dailies.Date',[$Y4F,$Y4T]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordsss.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Creditor_Coin');



            $EgmalyMasrofatY4=  $TotalDebMasrofatY4 -  $TotalCredMasrofatY4 ;




                  $TotalDebMasrofatCY = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {
               $YCF=date("Y-01-01");
        $YCT=date("Y-12-31");
            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->whereBetween('general_dailies.Date',[$YCF,$YCT]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordsss.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Debitor_Coin');



        $TotalCredMasrofatCY = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {
               $YCF=date("Y-01-01");
        $YCT=date("Y-12-31");
            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->whereBetween('general_dailies.Date',[$YCF,$YCT]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordsss.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Creditor_Coin');



            $EgmalyMasrofatCY=  $TotalDebMasrofatCY -  $TotalCredMasrofatCY ;


            // =============  Safy Rab7   ===============


            $AMsrfoat=AcccountingManual::where('id',17)->first();

        if($AMsrfoat->Parent == 0){

          $wordsss=$AMsrfoat->Code.'0';

        }else{

           $wordsss=$AMsrfoat->Code;

        }
  $AErydaaat=AcccountingManual::where('id',18)->first();

        if($AErydaaat->Parent == 0){

          $wordsX=$AErydaaat->Code.'0';

        }else{

           $wordsX=$AEryd->Code;

        }


    $ATaklfaaat=AcccountingManual::where('id',19)->first();

        if($ATaklfaaat->Parent == 0){

          $wordssX=$ATaklfaaat->Code.'0';

        }else{

           $wordssX=$ATaklfaaat->Code;

        }


        $AMasroffffffffat=AcccountingManual::where('id',20)->first();

        if($AMasroffffffffat->Parent == 0){

          $wordsssX=$AMasroffffffffat->Code.'0';

        }else{

           $wordsssX=$AMasroffffffffat->Code;

        }


              $TotalDebErydatJan = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {
            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->whereBetween('general_dailies.Date', [date('Y-01-01'),date('Y-02-01')]);
        })
          ->where('acccounting_manuals.Code', 'like', $wordsX.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Debitor_Coin');



        $TotalCredErydatJan = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {
            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->whereBetween('general_dailies.Date', [date('Y-01-01'),date('Y-02-01')]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordsX.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Creditor_Coin');

        $EgmalyErydatJan=$TotalCredErydatJan - $TotalDebErydatJan  ;




              $TotalDebTaklfaatJan = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {

            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                   ->whereBetween('general_dailies.Date', [date('Y-01-01'),date('Y-02-01')]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordssX.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Debitor_Coin');



        $TotalCredTaklfaatJan = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {
            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
               ->whereBetween('general_dailies.Date', [date('Y-01-01'),date('Y-02-01')]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordssX.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Creditor_Coin');

        $EgmalyTaklfaatJan=$TotalDebTaklfaatJan - $TotalCredTaklfaatJan  ;


           $TotalDebMasrofaatJan = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {


            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->whereBetween('general_dailies.Date',[date('Y-01-01'),date('Y-02-01')]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordsssX.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Debitor_Coin');



        $TotalCredMasrofaatJan = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {


            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                   ->whereBetween('general_dailies.Date', [date('Y-01-01'),date('Y-02-01')]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordsssX.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Creditor_Coin');

        $EgmalyMasrofaatJan=$TotalDebMasrofaatJan - $TotalCredMasrofaatJan  ;


        $fgJan= $EgmalyTaklfaatJan + $EgmalyMasrofaatJan;
        if($EgmalyErydatJan >  $fgJan){
         if($fgJan  <  0 ){

            $SafyRab7Jan =$EgmalyErydatJan + ($EgmalyTaklfaatJan  +   $EgmalyMasrofaatJan)  ;
        }else{

              $SafyRab7Jan = $EgmalyErydatJan -  ($EgmalyTaklfaatJan  +   $EgmalyMasrofaatJan)  ;

        }
        }elseif($EgmalyErydatJan <  $fgJan){

        if($fgJan  <  0 ){

            $SafyRab7Jan =$EgmalyErydatJan + ($EgmalyTaklfaatJan  +   $EgmalyMasrofaatJan)  ;
        }else{

              $SafyRab7Jan = $EgmalyErydatJan -  ($EgmalyTaklfaatJan  +   $EgmalyMasrofaatJan)  ;

        }


        }elseif($EgmalyErydatJan ==  $fgJan){

           $SafyRab7Jan = $EgmalyErydatJan -  ($EgmalyTaklfaatJan  +   $EgmalyMasrofaatJan)  ;

        }


       $TotalDebErydatFeb = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {
            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->whereBetween('general_dailies.Date', [date('Y-02-01'),date('Y-03-01')]);
        })
          ->where('acccounting_manuals.Code', 'like', $wordsX.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Debitor_Coin');



        $TotalCredErydatFeb = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {
            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->whereBetween('general_dailies.Date',[date('Y-02-01'),date('Y-03-01')]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordsX.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Creditor_Coin');

        $EgmalyErydatFeb=$TotalCredErydatFeb - $TotalDebErydatFeb  ;




              $TotalDebTaklfaatFeb = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {

            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                   ->whereBetween('general_dailies.Date', [date('Y-02-01'),date('Y-03-01')]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordssX.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Debitor_Coin');



        $TotalCredTaklfaatFeb = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {
            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
               ->whereBetween('general_dailies.Date', [date('Y-02-01'),date('Y-03-01')]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordssX.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Creditor_Coin');

        $EgmalyTaklfaatFeb=$TotalDebTaklfaatFeb - $TotalCredTaklfaatFeb  ;


           $TotalDebMasrofaatFeb = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {


            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->whereBetween('general_dailies.Date', [date('Y-02-01'),date('Y-03-01')]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordsssX.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Debitor_Coin');



        $TotalCredMasrofaatFeb = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {


            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                   ->whereBetween('general_dailies.Date', [date('Y-02-01'),date('Y-03-01')]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordsssX.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Creditor_Coin');

        $EgmalyMasrofaatFeb=$TotalDebMasrofaatFeb - $TotalCredMasrofaatFeb  ;


        $fgFeb= $EgmalyTaklfaatFeb + $EgmalyMasrofaatFeb;
        if($EgmalyErydatFeb >  $fgFeb){
         if($fgFeb  <  0 ){

            $SafyRab7Feb =$EgmalyErydatFeb + ($EgmalyTaklfaatFeb  +   $EgmalyMasrofaatFeb)  ;
        }else{

              $SafyRab7Feb = $EgmalyErydatFeb -  ($EgmalyTaklfaatFeb  +   $EgmalyMasrofaatFeb)  ;

        }
        }elseif($EgmalyErydatFeb <  $fgFeb){

        if($fgFeb  <  0 ){

            $SafyRab7Feb =$EgmalyErydatFeb + ($EgmalyTaklfaatFeb  +   $EgmalyMasrofaatFeb)  ;
        }else{

              $SafyRab7Feb = $EgmalyErydatFeb -  ($EgmalyTaklfaatFeb  +   $EgmalyMasrofaatFeb)  ;

        }


        }elseif($EgmalyErydatFeb ==  $fgFeb){

           $SafyRab7Feb = $EgmalyErydatFeb -  ($EgmalyTaklfaatFeb  +   $EgmalyMasrofaatFeb)  ;

        }



     $TotalDebErydatMar = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {
            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->whereBetween('general_dailies.Date', [date('Y-03-01'),date('Y-04-01')]);
        })
          ->where('acccounting_manuals.Code', 'like', $wordsX.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Debitor_Coin');



        $TotalCredErydatMar = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {
            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->whereBetween('general_dailies.Date', [date('Y-03-01'),date('Y-04-01')]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordsX.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Creditor_Coin');

        $EgmalyErydatMar=$TotalCredErydatMar - $TotalDebErydatMar  ;




              $TotalDebTaklfaatMar = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {

            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                   ->whereBetween('general_dailies.Date', [date('Y-03-01'),date('Y-04-01')]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordssX.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Debitor_Coin');



        $TotalCredTaklfaatMar = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {
            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
               ->whereBetween('general_dailies.Date', [date('Y-03-01'),date('Y-04-01')]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordssX.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Creditor_Coin');

        $EgmalyTaklfaatMar=$TotalDebTaklfaatMar - $TotalCredTaklfaatMar  ;


           $TotalDebMasrofaatMar = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {


            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->whereBetween('general_dailies.Date', [date('Y-03-01'),date('Y-04-01')]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordsssX.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Debitor_Coin');



        $TotalCredMasrofaatMar = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {


            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                   ->whereBetween('general_dailies.Date', [date('Y-03-01'),date('Y-04-01')]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordsssX.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Creditor_Coin');

        $EgmalyMasrofaatMar=$TotalDebMasrofaatMar - $TotalCredMasrofaatMar  ;


        $fgMar= $EgmalyTaklfaatMar + $EgmalyMasrofaatMar;
        if($EgmalyErydatMar >  $fgMar){
         if($fgMar  <  0 ){

            $SafyRab7Mar =$EgmalyErydatMar + ($EgmalyTaklfaatMar  +   $EgmalyMasrofaatMar)  ;
        }else{

              $SafyRab7Mar = $EgmalyErydatMar -  ($EgmalyTaklfaatMar  +   $EgmalyMasrofaatMar)  ;

        }
        }elseif($EgmalyErydatMar <  $fgMar){

        if($fgMar  <  0 ){

            $SafyRab7Mar =$EgmalyErydatMar + ($EgmalyTaklfaatMar  +   $EgmalyMasrofaatMar)  ;
        }else{

              $SafyRab7Mar = $EgmalyErydatMar -  ($EgmalyTaklfaatMar  +   $EgmalyMasrofaatMar)  ;

        }


        }elseif($EgmalyErydatMar ==  $fgMar){

           $SafyRab7Mar = $EgmalyErydatMar -  ($EgmalyTaklfaatMar  +   $EgmalyMasrofaatMar)  ;

        }

     $TotalDebErydatApr = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {
            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->whereBetween('general_dailies.Date', [date('Y-04-01'),date('Y-05-01')]);
        })
          ->where('acccounting_manuals.Code', 'like', $wordsX.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Debitor_Coin');



        $TotalCredErydatApr = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {
            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->whereBetween('general_dailies.Date', [date('Y-04-01'),date('Y-05-01')]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordsX.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Creditor_Coin');

        $EgmalyErydatApr=$TotalCredErydatApr - $TotalDebErydatApr  ;




              $TotalDebTaklfaatApr = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {

            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                   ->whereBetween('general_dailies.Date', [date('Y-04-01'),date('Y-05-01')]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordssX.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Debitor_Coin');



        $TotalCredTaklfaatApr = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {
            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
               ->whereBetween('general_dailies.Date', [date('Y-04-01'),date('Y-05-01')]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordssX.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Creditor_Coin');

        $EgmalyTaklfaatApr=$TotalDebTaklfaatApr - $TotalCredTaklfaatApr  ;


           $TotalDebMasrofaatApr = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {


            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->whereBetween('general_dailies.Date', [date('Y-04-01'),date('Y-05-01')]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordsssX.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Debitor_Coin');



        $TotalCredMasrofaatApr = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {


            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                   ->whereBetween('general_dailies.Date', [date('Y-04-01'),date('Y-05-01')]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordsssX.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Creditor_Coin');

        $EgmalyMasrofaatApr=$TotalDebMasrofaatApr - $TotalCredMasrofaatApr  ;


        $fgApr= $EgmalyTaklfaatApr + $EgmalyMasrofaatApr;
        if($EgmalyErydatApr >  $fgApr){
         if($fgApr  <  0 ){

            $SafyRab7Apr =$EgmalyErydatApr + ($EgmalyTaklfaatApr  +   $EgmalyMasrofaatApr)  ;
        }else{

              $SafyRab7Apr = $EgmalyErydatApr -  ($EgmalyTaklfaatApr  +   $EgmalyMasrofaatApr)  ;

        }
        }elseif($EgmalyErydatApr <  $fgApr){

        if($fgApr  <  0 ){

            $SafyRab7Apr =$EgmalyErydatApr + ($EgmalyTaklfaatApr  +   $EgmalyMasrofaatApr)  ;
        }else{

              $SafyRab7Apr = $EgmalyErydatApr -  ($EgmalyTaklfaatApr  +   $EgmalyMasrofaatApr)  ;

        }


        }elseif($EgmalyErydatApr ==  $fgApr){

           $SafyRab7Apr = $EgmalyErydatApr -  ($EgmalyTaklfaatApr  +   $EgmalyMasrofaatApr)  ;

        }


     $TotalDebErydatMay = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {
            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->whereBetween('general_dailies.Date', [date('Y-05-01'),date('Y-06-01')]);
        })
          ->where('acccounting_manuals.Code', 'like', $wordsX.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Debitor_Coin');



        $TotalCredErydatMay = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {
            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->whereBetween('general_dailies.Date', [date('Y-05-01'),date('Y-06-01')]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordsX.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Creditor_Coin');

        $EgmalyErydatMay=$TotalCredErydatMay - $TotalDebErydatMay  ;




              $TotalDebTaklfaatMay = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {

            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                   ->whereBetween('general_dailies.Date', [date('Y-05-01'),date('Y-06-01')]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordssX.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Debitor_Coin');



        $TotalCredTaklfaatMay = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {
            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
               ->whereBetween('general_dailies.Date', [date('Y-05-01'),date('Y-06-01')]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordssX.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Creditor_Coin');

        $EgmalyTaklfaatMay=$TotalDebTaklfaatMay - $TotalCredTaklfaatMay  ;


           $TotalDebMasrofaatMay = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {


            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->whereBetween('general_dailies.Date', [date('Y-05-01'),date('Y-06-01')]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordsssX.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Debitor_Coin');



        $TotalCredMasrofaatMay = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {


            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                   ->whereBetween('general_dailies.Date', [date('Y-05-01'),date('Y-06-01')]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordsssX.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Creditor_Coin');

        $EgmalyMasrofaatMay=$TotalDebMasrofaatMay - $TotalCredMasrofaatMay  ;


        $fgMay= $EgmalyTaklfaatMay + $EgmalyMasrofaatMay;
        if($EgmalyErydatMay >  $fgMay){
         if($fgMay  <  0 ){

            $SafyRab7May =$EgmalyErydatMay + ($EgmalyTaklfaatMay  +   $EgmalyMasrofaatMay)  ;
        }else{

              $SafyRab7May = $EgmalyErydatMay -  ($EgmalyTaklfaatMay  +   $EgmalyMasrofaatMay)  ;

        }
        }elseif($EgmalyErydatMay <  $fgMay){

        if($fgMay  <  0 ){

            $SafyRab7May =$EgmalyErydatMay + ($EgmalyTaklfaatMay  +   $EgmalyMasrofaatMay)  ;
        }else{

              $SafyRab7May = $EgmalyErydatMay -  ($EgmalyTaklfaatMay  +   $EgmalyMasrofaatMay)  ;

        }


        }elseif($EgmalyErydatMay ==  $fgMay){

           $SafyRab7May = $EgmalyErydatMay -  ($EgmalyTaklfaatMay  +   $EgmalyMasrofaatMay)  ;

        }

     $TotalDebErydatJun = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {
            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->whereBetween('general_dailies.Date', [date('Y-06-01'),date('Y-07-01')]);
        })
          ->where('acccounting_manuals.Code', 'like', $wordsX.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Debitor_Coin');



        $TotalCredErydatJun = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {
            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->whereBetween('general_dailies.Date', [date('Y-06-01'),date('Y-07-01')]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordsX.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Creditor_Coin');

        $EgmalyErydatJun=$TotalCredErydatJun - $TotalDebErydatJun  ;




              $TotalDebTaklfaatJun = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {

            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                   ->whereBetween('general_dailies.Date', [date('Y-06-01'),date('Y-07-01')]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordssX.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Debitor_Coin');



        $TotalCredTaklfaatJun = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {
            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
               ->whereBetween('general_dailies.Date', [date('Y-06-01'),date('Y-07-01')]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordssX.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Creditor_Coin');

        $EgmalyTaklfaatJun=$TotalDebTaklfaatJun - $TotalCredTaklfaatJun  ;


           $TotalDebMasrofaatJun = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {


            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->whereBetween('general_dailies.Date', [date('Y-06-01'),date('Y-07-01')]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordsssX.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Debitor_Coin');



        $TotalCredMasrofaatJun = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {


            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                   ->whereBetween('general_dailies.Date', [date('Y-06-01'),date('Y-07-01')]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordsssX.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Creditor_Coin');

        $EgmalyMasrofaatJun=$TotalDebMasrofaatJun - $TotalCredMasrofaatJun  ;


        $fgJun= $EgmalyTaklfaatJun + $EgmalyMasrofaatJun;
        if($EgmalyErydatJun >  $fgJun){
         if($fgJun  <  0 ){

            $SafyRab7Jun =$EgmalyErydatJun + ($EgmalyTaklfaatJun  +   $EgmalyMasrofaatJun)  ;
        }else{

              $SafyRab7Jun = $EgmalyErydatJun -  ($EgmalyTaklfaatJun  +   $EgmalyMasrofaatJun)  ;

        }
        }elseif($EgmalyErydatJun <  $fgJun){

        if($fgJun  <  0 ){

            $SafyRab7Jun =$EgmalyErydatJun + ($EgmalyTaklfaatJun  +   $EgmalyMasrofaatJun)  ;
        }else{

              $SafyRab7Jun = $EgmalyErydatJun -  ($EgmalyTaklfaatJun  +   $EgmalyMasrofaatJun)  ;

        }


        }elseif($EgmalyErydatJun ==  $fgJun){

           $SafyRab7Jun = $EgmalyErydatJun -  ($EgmalyTaklfaatJun  +   $EgmalyMasrofaatJun)  ;

        }

     $TotalDebErydatJul = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {
            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->whereBetween('general_dailies.Date', [date('Y-07-01'),date('Y-08-01')]);
        })
          ->where('acccounting_manuals.Code', 'like', $wordsX.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Debitor_Coin');



        $TotalCredErydatJul = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {
            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->whereBetween('general_dailies.Date', [date('Y-07-01'),date('Y-08-01')]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordsX.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Creditor_Coin');

        $EgmalyErydatJul=$TotalCredErydatJul - $TotalDebErydatJul  ;




              $TotalDebTaklfaatJul = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {

            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                   ->whereBetween('general_dailies.Date', [date('Y-07-01'),date('Y-08-01')]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordssX.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Debitor_Coin');



        $TotalCredTaklfaatJul = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {
            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
               ->whereBetween('general_dailies.Date', [date('Y-07-01'),date('Y-08-01')]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordssX.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Creditor_Coin');

        $EgmalyTaklfaatJul=$TotalDebTaklfaatJul - $TotalCredTaklfaatJul  ;


           $TotalDebMasrofaatJul = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {


            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->whereBetween('general_dailies.Date', [date('Y-07-01'),date('Y-08-01')]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordsssX.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Debitor_Coin');



        $TotalCredMasrofaatJul = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {


            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                   ->whereBetween('general_dailies.Date', [date('Y-07-01'),date('Y-08-01')]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordsssX.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Creditor_Coin');

        $EgmalyMasrofaatJul=$TotalDebMasrofaatJul - $TotalCredMasrofaatJul  ;


        $fgJul= $EgmalyTaklfaatJul + $EgmalyMasrofaatJul;
        if($EgmalyErydatJul >  $fgJul){
         if($fgJul  <  0 ){

            $SafyRab7Jul =$EgmalyErydatJul + ($EgmalyTaklfaatJul  +   $EgmalyMasrofaatJul)  ;
        }else{

              $SafyRab7Jul = $EgmalyErydatJul -  ($EgmalyTaklfaatJul  +   $EgmalyMasrofaatJul)  ;

        }
        }elseif($EgmalyErydatJul <  $fgJul){

        if($fgJul  <  0 ){

            $SafyRab7Jul =$EgmalyErydatJul + ($EgmalyTaklfaatJul  +   $EgmalyMasrofaatJul)  ;
        }else{

              $SafyRab7Jul = $EgmalyErydatJul -  ($EgmalyTaklfaatJul  +   $EgmalyMasrofaatJul)  ;

        }


        }elseif($EgmalyErydatJul ==  $fgJul){

           $SafyRab7Jul = $EgmalyErydatJul -  ($EgmalyTaklfaatJul  +   $EgmalyMasrofaatJul)  ;

        }

     $TotalDebErydatAug = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {
            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->whereBetween('general_dailies.Date', [date('Y-08-01'),date('Y-09-01')]);
        })
          ->where('acccounting_manuals.Code', 'like', $wordsX.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Debitor_Coin');



        $TotalCredErydatAug = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {
            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->whereBetween('general_dailies.Date', [date('Y-08-01'),date('Y-09-01')]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordsX.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Creditor_Coin');

        $EgmalyErydatAug=$TotalCredErydatAug - $TotalDebErydatAug  ;




              $TotalDebTaklfaatAug = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {

            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                   ->whereBetween('general_dailies.Date', [date('Y-08-01'),date('Y-09-01')]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordssX.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Debitor_Coin');



        $TotalCredTaklfaatAug = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {
            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
               ->whereBetween('general_dailies.Date', [date('Y-08-01'),date('Y-09-01')]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordssX.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Creditor_Coin');

        $EgmalyTaklfaatAug=$TotalDebTaklfaatAug - $TotalCredTaklfaatAug  ;


           $TotalDebMasrofaatAug = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {


            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->whereBetween('general_dailies.Date', [date('Y-08-01'),date('Y-09-01')]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordsssX.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Debitor_Coin');



        $TotalCredMasrofaatAug = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {


            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                   ->whereBetween('general_dailies.Date', [date('Y-08-01'),date('Y-09-01')]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordsssX.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Creditor_Coin');

        $EgmalyMasrofaatAug=$TotalDebMasrofaatAug - $TotalCredMasrofaatAug  ;


        $fgAug= $EgmalyTaklfaatAug + $EgmalyMasrofaatAug;
        if($EgmalyErydatAug >  $fgAug){
         if($fgAug  <  0 ){

            $SafyRab7Aug =$EgmalyErydatAug + ($EgmalyTaklfaatAug  +   $EgmalyMasrofaatAug)  ;
        }else{

              $SafyRab7Aug = $EgmalyErydatAug -  ($EgmalyTaklfaatAug  +   $EgmalyMasrofaatAug)  ;

        }
        }elseif($EgmalyErydatAug <  $fgAug){

        if($fgAug  <  0 ){

            $SafyRab7Aug =$EgmalyErydatAug + ($EgmalyTaklfaatAug  +   $EgmalyMasrofaatAug)  ;
        }else{

              $SafyRab7Aug = $EgmalyErydatAug -  ($EgmalyTaklfaatAug  +   $EgmalyMasrofaatAug)  ;

        }


        }elseif($EgmalyErydatAug ==  $fgAug){

           $SafyRab7Aug = $EgmalyErydatAug -  ($EgmalyTaklfaatAug  +   $EgmalyMasrofaatAug)  ;

        }

     $TotalDebErydatSep = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {
            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->whereBetween('general_dailies.Date', [date('Y-09-01'),date('Y-10-01')]);
        })
          ->where('acccounting_manuals.Code', 'like', $wordsX.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Debitor_Coin');



        $TotalCredErydatSep = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {
            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->whereBetween('general_dailies.Date', [date('Y-09-01'),date('Y-10-01')]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordsX.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Creditor_Coin');

        $EgmalyErydatSep=$TotalCredErydatSep - $TotalDebErydatSep  ;




              $TotalDebTaklfaatSep = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {

            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                   ->whereBetween('general_dailies.Date', [date('Y-09-01'),date('Y-10-01')]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordssX.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Debitor_Coin');



        $TotalCredTaklfaatSep = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {
            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
               ->whereBetween('general_dailies.Date', [date('Y-09-01'),date('Y-10-01')]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordssX.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Creditor_Coin');

        $EgmalyTaklfaatSep=$TotalDebTaklfaatSep - $TotalCredTaklfaatSep  ;


           $TotalDebMasrofaatSep = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {


            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->whereBetween('general_dailies.Date', [date('Y-09-01'),date('Y-10-01')]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordsssX.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Debitor_Coin');



        $TotalCredMasrofaatSep = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {


            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                   ->whereBetween('general_dailies.Date', [date('Y-09-01'),date('Y-10-01')]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordsssX.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Creditor_Coin');

        $EgmalyMasrofaatSep=$TotalDebMasrofaatSep - $TotalCredMasrofaatSep  ;


        $fgSep= $EgmalyTaklfaatSep + $EgmalyMasrofaatSep;
        if($EgmalyErydatSep >  $fgSep){
         if($fgSep  <  0 ){

            $SafyRab7Sep =$EgmalyErydatSep + ($EgmalyTaklfaatSep  +   $EgmalyMasrofaatSep)  ;
        }else{

              $SafyRab7Sep = $EgmalyErydatSep -  ($EgmalyTaklfaatSep  +   $EgmalyMasrofaatSep)  ;

        }
        }elseif($EgmalyErydatSep <  $fgSep){

        if($fgSep  <  0 ){

            $SafyRab7Sep =$EgmalyErydatSep + ($EgmalyTaklfaatSep  +   $EgmalyMasrofaatSep)  ;
        }else{

              $SafyRab7Sep = $EgmalyErydatSep -  ($EgmalyTaklfaatSep  +   $EgmalyMasrofaatSep)  ;

        }


        }elseif($EgmalyErydatSep ==  $fgSep){

           $SafyRab7Sep = $EgmalyErydatSep -  ($EgmalyTaklfaatSep  +   $EgmalyMasrofaatSep)  ;

        }

     $TotalDebErydatOct = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {
            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->whereBetween('general_dailies.Date', [date('Y-10-01'),date('Y-11-01')]);
        })
          ->where('acccounting_manuals.Code', 'like', $wordsX.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Debitor_Coin');



        $TotalCredErydatOct = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {
            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->whereBetween('general_dailies.Date', [date('Y-10-01'),date('Y-11-01')]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordsX.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Creditor_Coin');

        $EgmalyErydatOct=$TotalCredErydatOct - $TotalDebErydatOct  ;




              $TotalDebTaklfaatOct = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {

            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                   ->whereBetween('general_dailies.Date', [date('Y-10-01'),date('Y-11-01')]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordssX.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Debitor_Coin');



        $TotalCredTaklfaatOct = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {
            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
               ->whereBetween('general_dailies.Date', [date('Y-10-01'),date('Y-11-01')]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordssX.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Creditor_Coin');

        $EgmalyTaklfaatOct=$TotalDebTaklfaatOct - $TotalCredTaklfaatOct  ;


           $TotalDebMasrofaatOct = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {


            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->whereBetween('general_dailies.Date', [date('Y-10-01'),date('Y-11-01')]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordsssX.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Debitor_Coin');



        $TotalCredMasrofaatOct = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {


            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                   ->whereBetween('general_dailies.Date', [date('Y-10-01'),date('Y-11-01')]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordsssX.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Creditor_Coin');

        $EgmalyMasrofaatOct=$TotalDebMasrofaatOct - $TotalCredMasrofaatOct  ;


        $fgOct= $EgmalyTaklfaatOct + $EgmalyMasrofaatOct;
        if($EgmalyErydatOct >  $fgOct){
         if($fgOct  <  0 ){

            $SafyRab7Oct =$EgmalyErydatOct + ($EgmalyTaklfaatOct  +   $EgmalyMasrofaatOct)  ;
        }else{

              $SafyRab7Oct = $EgmalyErydatOct -  ($EgmalyTaklfaatOct  +   $EgmalyMasrofaatOct)  ;

        }
        }elseif($EgmalyErydatOct <  $fgOct){

        if($fgOct  <  0 ){

            $SafyRab7Oct =$EgmalyErydatOct + ($EgmalyTaklfaatOct  +   $EgmalyMasrofaatOct)  ;
        }else{

              $SafyRab7Oct = $EgmalyErydatOct -  ($EgmalyTaklfaatOct  +   $EgmalyMasrofaatOct)  ;

        }


        }elseif($EgmalyErydatOct ==  $fgOct){

           $SafyRab7Oct = $EgmalyErydatOct -  ($EgmalyTaklfaatOct  +   $EgmalyMasrofaatOct)  ;

        }

     $TotalDebErydatNov = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {
            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->whereBetween('general_dailies.Date', [date('Y-11-01'),date('Y-12-01')]);
        })
          ->where('acccounting_manuals.Code', 'like', $wordsX.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Debitor_Coin');



        $TotalCredErydatNov = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {
            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->whereBetween('general_dailies.Date', [date('Y-11-01'),date('Y-12-01')]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordsX.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Creditor_Coin');

        $EgmalyErydatNov=$TotalCredErydatNov - $TotalDebErydatNov  ;




              $TotalDebTaklfaatNov = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {

            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                   ->whereBetween('general_dailies.Date', [date('Y-11-01'),date('Y-12-01')]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordssX.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Debitor_Coin');



        $TotalCredTaklfaatNov = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {
            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
               ->whereBetween('general_dailies.Date', [date('Y-11-01'),date('Y-12-01')]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordssX.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Creditor_Coin');

        $EgmalyTaklfaatNov=$TotalDebTaklfaatNov - $TotalCredTaklfaatNov  ;


           $TotalDebMasrofaatNov = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {


            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->whereBetween('general_dailies.Date', [date('Y-11-01'),date('Y-12-01')]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordsssX.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Debitor_Coin');



        $TotalCredMasrofaatNov = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {


            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                   ->whereBetween('general_dailies.Date', [date('Y-11-01'),date('Y-12-01')]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordsssX.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Creditor_Coin');

        $EgmalyMasrofaatNov=$TotalDebMasrofaatNov - $TotalCredMasrofaatNov  ;


        $fgNov= $EgmalyTaklfaatNov + $EgmalyMasrofaatNov;
        if($EgmalyErydatNov >  $fgNov){
         if($fgNov  <  0 ){

            $SafyRab7Nov =$EgmalyErydatNov + ($EgmalyTaklfaatNov  +   $EgmalyMasrofaatNov)  ;
        }else{

              $SafyRab7Nov = $EgmalyErydatNov -  ($EgmalyTaklfaatNov  +   $EgmalyMasrofaatNov)  ;

        }
        }elseif($EgmalyErydatNov <  $fgNov){

        if($fgNov  <  0 ){

            $SafyRab7Nov =$EgmalyErydatNov + ($EgmalyTaklfaatNov  +   $EgmalyMasrofaatNov)  ;
        }else{

              $SafyRab7Nov = $EgmalyErydatNov -  ($EgmalyTaklfaatNov  +   $EgmalyMasrofaatNov)  ;

        }


        }elseif($EgmalyErydatNov ==  $fgNov){

           $SafyRab7Nov = $EgmalyErydatNov -  ($EgmalyTaklfaatNov  +   $EgmalyMasrofaatNov)  ;

        }

     $TotalDebErydatDec = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {
            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->whereBetween('general_dailies.Date', [date('Y-12-01'),date('Y-12-31')]);
        })
          ->where('acccounting_manuals.Code', 'like', $wordsX.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Debitor_Coin');



        $TotalCredErydatDec = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {
            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->whereBetween('general_dailies.Date', [date('Y-12-01'),date('Y-12-31')]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordsX.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Creditor_Coin');

        $EgmalyErydatDec=$TotalCredErydatDec - $TotalDebErydatDec  ;




              $TotalDebTaklfaatDec = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {

            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                   ->whereBetween('general_dailies.Date', [date('Y-12-01'),date('Y-12-31')]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordssX.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Debitor_Coin');



        $TotalCredTaklfaatDec = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {
            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
               ->whereBetween('general_dailies.Date', [date('Y-12-01'),date('Y-12-31')]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordssX.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Creditor_Coin');

        $EgmalyTaklfaatDec=$TotalDebTaklfaatDec - $TotalCredTaklfaatDec  ;


           $TotalDebMasrofaatDec = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {


            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                 ->whereBetween('general_dailies.Date', [date('Y-12-01'),date('Y-12-31')]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordsssX.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Debitor_Coin');



        $TotalCredMasrofaatDec = DB::table('acccounting_manuals')
            ->join('general_dailies', function ($join) {


            $join->on('acccounting_manuals.id', '=', 'general_dailies.Account')
                   ->whereBetween('general_dailies.Date', [date('Y-12-01'),date('Y-12-31')]);
        })

          ->where('acccounting_manuals.Code', 'like', $wordsssX.'%')
                 ->where('acccounting_manuals.Type',  '=',1)
            ->get()->sum('Creditor_Coin');

        $EgmalyMasrofaatDec=$TotalDebMasrofaatDec - $TotalCredMasrofaatDec  ;


        $fgDec= $EgmalyTaklfaatDec + $EgmalyMasrofaatDec;
        if($EgmalyErydatDec >  $fgDec){
         if($fgDec  <  0 ){

            $SafyRab7Dec =$EgmalyErydatDec + ($EgmalyTaklfaatDec  +   $EgmalyMasrofaatDec)  ;
        }else{

              $SafyRab7Dec = $EgmalyErydatDec -  ($EgmalyTaklfaatDec  +   $EgmalyMasrofaatDec)  ;

        }
        }elseif($EgmalyErydatDec <  $fgDec){

        if($fgDec  <  0 ){

            $SafyRab7Dec =$EgmalyErydatDec + ($EgmalyTaklfaatDec  +   $EgmalyMasrofaatDec)  ;
        }else{

              $SafyRab7Dec = $EgmalyErydatDec -  ($EgmalyTaklfaatDec  +   $EgmalyMasrofaatDec)  ;

        }


        }elseif($EgmalyErydatDec ==  $fgDec){

           $SafyRab7Dec = $EgmalyErydatDec -  ($EgmalyTaklfaatDec  +   $EgmalyMasrofaatDec)  ;

        }



            $Groups=ItemsGroups::select('Name','NameEn','id')->get();
            $Brands=Brands::select('Name','NameEn','id')->get();






         return view('admin.StatisticsGraph',[

             'SalesJan'=>$SalesJan,
             'SalesFeb'=>$SalesFeb,
             'SalesMar'=>$SalesMar,
             'SalesApr'=>$SalesApr,
             'SalesMay'=>$SalesMay,
             'SalesJun'=>$SalesJun,
             'SalesJul'=>$SalesJul,
             'SalesAug'=>$SalesAug,
             'SalesSep'=>$SalesSep,
             'SalesOct'=>$SalesOct,
             'SalesNov'=>$SalesNov,
             'SalesDec'=>$SalesDec,

             'SalesSat'=>$SalesSat,
             'SalesSun'=>$SalesSun,
             'SalesMon'=>$SalesMon,
             'SalesTue'=>$SalesTue,
             'SalesWed'=>$SalesWed,
             'SalesThr'=>$SalesThr,
             'SalesFri'=>$SalesFri,

             'SalesPrevY1'=>$SalesPrevY1,
             'SalesPrevY2'=>$SalesPrevY2,
             'SalesPrevY3'=>$SalesPrevY3,
             'SalesPrevY4'=>$SalesPrevY4,
             'SalesPrevYC'=>$SalesPrevYC,

              'PurchJan'=>$PurchJan,
             'PurchFeb'=>$PurchFeb,
             'PurchMar'=>$PurchMar,
             'PurchApr'=>$PurchApr,
             'PurchMay'=>$PurchMay,
             'PurchJun'=>$PurchJun,
             'PurchJul'=>$PurchJul,
             'PurchAug'=>$PurchAug,
             'PurchSep'=>$PurchSep,
             'PurchOct'=>$PurchOct,
             'PurchNov'=>$PurchNov,
             'PurchDec'=>$PurchDec,

             'PurchSat'=>$PurchSat,
             'PurchSun'=>$PurchSun,
             'PurchMon'=>$PurchMon,
             'PurchTue'=>$PurchTue,
             'PurchWed'=>$PurchWed,
             'PurchThr'=>$PurchThr,
             'PurchFri'=>$PurchFri,

             'PurchPrevY1'=>$PurchPrevY1,
             'PurchPrevY2'=>$PurchPrevY2,
             'PurchPrevY3'=>$PurchPrevY3,
             'PurchPrevY4'=>$PurchPrevY4,
             'PurchPrevYC'=>$PurchPrevYC,


               'ReturnSalesJan'=>$ReturnSalesJan,
             'ReturnSalesFeb'=>$ReturnSalesFeb,
             'ReturnSalesMar'=>$ReturnSalesMar,
             'ReturnSalesApr'=>$ReturnSalesApr,
             'ReturnSalesMay'=>$ReturnSalesMay,
             'ReturnSalesJun'=>$ReturnSalesJun,
             'ReturnSalesJul'=>$ReturnSalesJul,
             'ReturnSalesAug'=>$ReturnSalesAug,
             'ReturnSalesSep'=>$ReturnSalesSep,
             'ReturnSalesOct'=>$ReturnSalesOct,
             'ReturnSalesNov'=>$ReturnSalesNov,
             'ReturnSalesDec'=>$ReturnSalesDec,

             'ReturnSalesSat'=>$ReturnSalesSat,
             'ReturnSalesSun'=>$ReturnSalesSun,
             'ReturnSalesMon'=>$ReturnSalesMon,
             'ReturnSalesTue'=>$ReturnSalesTue,
             'ReturnSalesWed'=>$ReturnSalesWed,
             'ReturnSalesThr'=>$ReturnSalesThr,
             'ReturnSalesFri'=>$ReturnSalesFri,

             'ReturnSalesPrevY1'=>$ReturnSalesPrevY1,
             'ReturnSalesPrevY2'=>$ReturnSalesPrevY2,
             'ReturnSalesPrevY3'=>$ReturnSalesPrevY3,
             'ReturnSalesPrevY4'=>$ReturnSalesPrevY4,
             'ReturnSalesPrevYC'=>$ReturnSalesPrevYC,

              'ReturnPurchJan'=>$ReturnPurchJan,
             'ReturnPurchFeb'=>$ReturnPurchFeb,
             'ReturnPurchMar'=>$ReturnPurchMar,
             'ReturnPurchApr'=>$ReturnPurchApr,
             'ReturnPurchMay'=>$ReturnPurchMay,
             'ReturnPurchJun'=>$ReturnPurchJun,
             'ReturnPurchJul'=>$ReturnPurchJul,
             'ReturnPurchAug'=>$ReturnPurchAug,
             'ReturnPurchSep'=>$ReturnPurchSep,
             'ReturnPurchOct'=>$ReturnPurchOct,
             'ReturnPurchNov'=>$ReturnPurchNov,
             'ReturnPurchDec'=>$ReturnPurchDec,

             'ReturnPurchSat'=>$ReturnPurchSat,
             'ReturnPurchSun'=>$ReturnPurchSun,
             'ReturnPurchMon'=>$ReturnPurchMon,
             'ReturnPurchTue'=>$ReturnPurchTue,
             'ReturnPurchWed'=>$ReturnPurchWed,
             'ReturnPurchThr'=>$ReturnPurchThr,
             'ReturnPurchFri'=>$ReturnPurchFri,

             'ReturnPurchPrevY1'=>$ReturnPurchPrevY1,
             'ReturnPurchPrevY2'=>$ReturnPurchPrevY2,
             'ReturnPurchPrevY3'=>$ReturnPurchPrevY3,
             'ReturnPurchPrevY4'=>$ReturnPurchPrevY4,
             'ReturnPurchPrevYC'=>$ReturnPurchPrevYC,



             'EgmalyMasrofatJan'=>$EgmalyMasrofatJan,
             'EgmalyMasrofatFeb'=>$EgmalyMasrofatFeb,
             'EgmalyMasrofatMar'=>$EgmalyMasrofatMar,
             'EgmalyMasrofatApr'=>$EgmalyMasrofatApr,
             'EgmalyMasrofatMay'=>$EgmalyMasrofatMay,
             'EgmalyMasrofatJun'=>$EgmalyMasrofatJun,
             'EgmalyMasrofatJul'=>$EgmalyMasrofatJul,
             'EgmalyMasrofatAug'=>$EgmalyMasrofatAug,
             'EgmalyMasrofatSep'=>$EgmalyMasrofatSep,
             'EgmalyMasrofatOct'=>$EgmalyMasrofatOct,
             'EgmalyMasrofatNov'=>$EgmalyMasrofatNov,
             'EgmalyMasrofatDec'=>$EgmalyMasrofatDec,

             'EgmalyMasrofatSat'=>$EgmalyMasrofatSat,
             'EgmalyMasrofatSun'=>$EgmalyMasrofatSun,
             'EgmalyMasrofatMon'=>$EgmalyMasrofatMon,
             'EgmalyMasrofatTue'=>$EgmalyMasrofatTue,
             'EgmalyMasrofatWed'=>$EgmalyMasrofatWed,
             'EgmalyMasrofatThr'=>$EgmalyMasrofatThr,
             'EgmalyMasrofatFri'=>$EgmalyMasrofatFri,

             'EgmalyMasrofatY1'=>$EgmalyMasrofatY1,
             'EgmalyMasrofatY2'=>$EgmalyMasrofatY2,
             'EgmalyMasrofatY3'=>$EgmalyMasrofatY3,
             'EgmalyMasrofatY4'=>$EgmalyMasrofatY4,
             'EgmalyMasrofatCY'=>$EgmalyMasrofatCY,


             'PaymentVoucherJan'=>$PaymentVoucherJan,
             'ReciptVoucherJan'=>$ReciptVoucherJan,
             'PaymentVoucherFeb'=>$PaymentVoucherFeb,
             'ReciptVoucherFeb'=>$ReciptVoucherFeb,
             'PaymentVoucherMar'=>$PaymentVoucherMar,
             'ReciptVoucherMar'=>$ReciptVoucherMar,
             'PaymentVoucherApr'=>$PaymentVoucherApr,
             'ReciptVoucherApr'=>$ReciptVoucherApr,
             'PaymentVoucherMay'=>$PaymentVoucherMay,
             'ReciptVoucherMay'=>$ReciptVoucherMay,
             'PaymentVoucherJun'=>$PaymentVoucherJun,
             'ReciptVoucherJun'=>$ReciptVoucherJun,
             'PaymentVoucherJul'=>$PaymentVoucherJul,
             'ReciptVoucherJul'=>$ReciptVoucherJul,
             'PaymentVoucherAug'=>$PaymentVoucherAug,
             'ReciptVoucherAug'=>$ReciptVoucherAug,
             'PaymentVoucherSep'=>$PaymentVoucherSep,
             'ReciptVoucherSep'=>$ReciptVoucherSep,
             'PaymentVoucherOct'=>$PaymentVoucherOct,
             'ReciptVoucherOct'=>$ReciptVoucherOct,
             'PaymentVoucherNov'=>$PaymentVoucherNov,
             'ReciptVoucherNov'=>$ReciptVoucherNov,
             'PaymentVoucherDec'=>$PaymentVoucherDec,
             'ReciptVoucherDec'=>$ReciptVoucherDec,
             'PaymentVoucherSat'=>$PaymentVoucherSat,
             'ReciptVoucherSat'=>$ReciptVoucherSat,
             'PaymentVoucherSun'=>$PaymentVoucherSun,
             'ReciptVoucherSun'=>$ReciptVoucherSun,
             'PaymentVoucherMon'=>$PaymentVoucherMon,
             'ReciptVoucherMon'=>$ReciptVoucherMon,
             'PaymentVoucherTue'=>$PaymentVoucherTue,
             'ReciptVoucherTue'=>$ReciptVoucherTue,
             'PaymentVoucherWed'=>$PaymentVoucherWed,
             'ReciptVoucherWed'=>$ReciptVoucherWed,
             'PaymentVoucherThr'=>$PaymentVoucherThr,
             'ReciptVoucherThr'=>$ReciptVoucherThr,
             'PaymentVoucherFri'=>$PaymentVoucherFri,
             'ReciptVoucherFri'=>$ReciptVoucherFri,
             'IncomChecksJan'=>$IncomChecksJan,
             'ExportChecksJan'=>$ExportChecksJan,
             'IncomChecksFeb'=>$IncomChecksFeb,
             'ExportChecksFeb'=>$ExportChecksFeb,
             'IncomChecksMar'=>$IncomChecksMar,
             'ExportChecksMar'=>$ExportChecksMar,
             'IncomChecksApr'=>$IncomChecksApr,
             'ExportChecksApr'=>$ExportChecksApr,
             'IncomChecksMay'=>$IncomChecksMay,
             'ExportChecksMay'=>$ExportChecksMay,
             'IncomChecksJun'=>$IncomChecksJun,
             'ExportChecksJun'=>$ExportChecksJun,
             'IncomChecksJul'=>$IncomChecksJul,
             'ExportChecksJul'=>$ExportChecksJul,
             'IncomChecksAug'=>$IncomChecksAug,
             'ExportChecksAug'=>$ExportChecksAug,
             'IncomChecksSep'=>$IncomChecksSep,
             'ExportChecksSep'=>$ExportChecksSep,
             'IncomChecksOct'=>$IncomChecksOct,
             'ExportChecksOct'=>$ExportChecksOct,
             'IncomChecksNov'=>$IncomChecksNov,
             'ExportChecksNov'=>$ExportChecksNov,
             'IncomChecksDec'=>$IncomChecksDec,
             'ExportChecksDec'=>$ExportChecksDec,
             'IncomChecksSat'=>$IncomChecksSat,
             'ExportChecksSat'=>$ExportChecksSat,
             'IncomChecksSun'=>$IncomChecksSun,
             'ExportChecksSun'=>$ExportChecksSun,
             'IncomChecksMon'=>$IncomChecksMon,
             'ExportChecksMon'=>$ExportChecksMon,
             'IncomChecksTue'=>$IncomChecksTue,
             'ExportChecksTue'=>$ExportChecksTue,
             'IncomChecksWed'=>$IncomChecksWed,
             'ExportChecksWed'=>$ExportChecksWed,
             'IncomChecksThr'=>$IncomChecksThr,
             'ExportChecksThr'=>$ExportChecksThr,
             'IncomChecksFri'=>$IncomChecksFri,
             'ExportChecksFri'=>$ExportChecksFri,
             'SafyRab7Jan'=>$SafyRab7Jan,
             'SafyRab7Feb'=>$SafyRab7Feb,
             'SafyRab7Mar'=>$SafyRab7Mar,
             'SafyRab7Apr'=>$SafyRab7Apr,
             'SafyRab7May'=>$SafyRab7May,
             'SafyRab7Jun'=>$SafyRab7Jun,
             'SafyRab7Jul'=>$SafyRab7Jul,
             'SafyRab7Aug'=>$SafyRab7Aug,
             'SafyRab7Sep'=>$SafyRab7Sep,
             'SafyRab7Oct'=>$SafyRab7Oct,
             'SafyRab7Nov'=>$SafyRab7Nov,
             'SafyRab7Dec'=>$SafyRab7Dec,

             'Groups'=>$Groups,
             'Brands'=>$Brands,


         ]);
    }


}
